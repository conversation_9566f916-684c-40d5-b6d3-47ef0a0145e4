# PowerShell script to fix all compilation errors

Write-Host "Fixing compilation errors..." -ForegroundColor Green

# Fix UsersController.cs
$usersControllerPath = "Controllers\UsersController.cs"
$content = Get-Content $usersControllerPath -Raw

# Fix line 169: user.id -> user.id.ToString()
$content = $content -replace 'user\.id,(\s+)"INFO",(\s+)true,(\s+)clientIp,(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\)', 'user.id.ToString(),$1clientIp,$3Request.Headers["User-Agent"].ToString(),$1"INFO",$2true'

# Fix line 192: user.id -> user.id.ToString()
$content = $content -replace '(\s+)user\.id,(\s+)"INFO",(\s+)true,(\s+)clientIp,(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\)', '$1user.id.ToString(),$2clientIp,$4Request.Headers["User-Agent"].ToString(),$2"INFO",$3true'

# Fix line 222: parameter order
$content = $content -replace '(\s+)"WARNING",(\s+)false,(\s+)clientIp,(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\)', '$1clientIp,$3Request.Headers["User-Agent"].ToString(),$1"WARNING",$2false'

# Fix line 294: user.id -> user.id.ToString()
$content = $content -replace '(\s+)user\.id,(\s+)"INFO",(\s+)true,(\s+)clientIp,(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\),(\s+)new \{ sessionId, jti, expiresAt \}', '$1user.id.ToString(),$2clientIp,$4Request.Headers["User-Agent"].ToString(),$2"INFO",$3true,$6JsonSerializer.Serialize(new { sessionId, jti, expiresAt })'

Set-Content $usersControllerPath $content

Write-Host "Fixed UsersController.cs" -ForegroundColor Yellow

# Fix PasswordValidationService.cs
$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath -Raw

# Fix line 161: userId -> userId?.ToString()
$content = $content -replace '(\s+)userId,(\s+)"WARNING",(\s+)false,(\s+)ipAddress,(\s+)userAgent', '$1userId?.ToString(),$2ipAddress,$4userAgent,$2"WARNING",$3false'

# Fix line 206: userId -> userId.ToString()
$content = $content -replace '(\s+)userId,(\s+)"WARNING",(\s+)false,(\s+)ipAddress,(\s+)userAgent', '$1userId.ToString(),$2ipAddress,$4userAgent,$2"WARNING",$3false'

# Fix line 350: parameter order
$content = $content -replace '(\s+)"INFO",(\s+)true,(\s+)ipAddress,(\s+)userAgent', '$1ipAddress,$3userAgent,$1"INFO",$2true'

# Fix line 474: userId -> userId.ToString()
$content = $content -replace '(\s+)userId,(\s+)"INFO",(\s+)true,(\s+)ipAddress,(\s+)userAgent', '$1userId.ToString(),$2ipAddress,$4userAgent,$2"INFO",$3true'

Set-Content $passwordServicePath $content

Write-Host "Fixed PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "All fixes applied! Running build..." -ForegroundColor Green
dotnet build
