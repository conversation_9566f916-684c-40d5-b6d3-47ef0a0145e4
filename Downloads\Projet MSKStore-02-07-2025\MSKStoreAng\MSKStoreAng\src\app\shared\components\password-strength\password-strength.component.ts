import { Component, Input, OnInit, OnChang<PERSON>, SimpleChanges } from '@angular/core';
import { InputValidationService, PasswordStrengthResult } from 'app/auth/services/input-validation.service';

@Component({
  selector: 'app-password-strength',
  templateUrl: './password-strength.component.html',
  styleUrls: ['./password-strength.component.scss']
})
export class PasswordStrengthComponent implements OnInit, OnChanges {
  @Input() password: string = '';
  @Input() email?: string;
  @Input() name?: string;
  @Input() showRequirements: boolean = true;
  @Input() showSuggestions: boolean = true;

  strengthResult: PasswordStrengthResult = {
    score: 0,
    level: 'Très faible',
    feedback: [],
    requirements: {
      length: false,
      uppercase: false,
      lowercase: false,
      numbers: false,
      special: false
    }
  };

  constructor(private validationService: InputValidationService) { }

  ngOnInit(): void {
    this.updateStrength();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['password']) {
      this.updateStrength();
    }
  }

  private updateStrength(): void {
    if (this.password) {
      this.strengthResult = this.validationService.calculatePasswordStrength(this.password);
    } else {
      this.strengthResult = {
        score: 0,
        level: 'Très faible',
        feedback: [],
        requirements: {
          length: false,
          uppercase: false,
          lowercase: false,
          numbers: false,
          special: false
        }
      };
    }
  }

  getStrengthClass(): string {
    if (this.strengthResult.score >= 80) return 'strength-excellent';
    if (this.strengthResult.score >= 60) return 'strength-strong';
    if (this.strengthResult.score >= 40) return 'strength-good';
    if (this.strengthResult.score >= 20) return 'strength-fair';
    return 'strength-weak';
  }

  getStrengthColor(): string {
    if (this.strengthResult.score >= 80) return '#28a745'; // Green
    if (this.strengthResult.score >= 60) return '#20c997'; // Teal
    if (this.strengthResult.score >= 40) return '#ffc107'; // Yellow
    if (this.strengthResult.score >= 20) return '#fd7e14'; // Orange
    return '#dc3545'; // Red
  }

  getProgressWidth(): string {
    return `${this.strengthResult.score}%`;
  }
}
