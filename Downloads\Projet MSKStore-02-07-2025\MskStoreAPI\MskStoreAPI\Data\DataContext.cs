﻿using Microsoft.EntityFrameworkCore;
using MskStoreAPI.Models;

namespace MskStoreAPI.Data
{
    public class DataContext : DbContext
    {
        public DataContext(DbContextOptions<DataContext> options) : base(options) { }

        public DbSet<MskStoreAPI.Models.Client> Clients { get; set; }

        public DbSet<MskStoreAPI.Models.Categorie> Categorie { get; set; }

        public DbSet<MskStoreAPI.Models.Produit> Produit { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Factureligneproduit>()
                .<PERSON><PERSON><PERSON>(c => new { c.FactureclientId, c.ProduitId, c.Id });

            modelBuilder.Entity<Commandelignefournisseur>()
        .<PERSON><PERSON><PERSON>(c => new { c.CommandefournisseurId, c.ProduitId, c.Id });

            modelBuilder.Entity<Commandeligneclient>()
       .<PERSON><PERSON><PERSON>(c => new { c.<PERSON>d, c.ProduitId, c.Id });

            modelBuilder.Entity<Devisligneproduit>()
     .<PERSON><PERSON><PERSON>(d => new { d.<PERSON>s<PERSON>d, d.ProduitId, d.Id });

            modelBuilder.Entity<User>()
                .HasDiscriminator<string>("role")
                .HasValue<Employee>("Employee")
                .HasValue<Admin>("Admin");

            modelBuilder.Entity<Produit>()
      .HasMany(p => p.Photoproduits)
      .WithOne()
      .HasForeignKey(p => p.ProduitId)
      .OnDelete(DeleteBehavior.Cascade);

          
        }

        public DbSet<MskStoreAPI.Models.Factureligneproduit> Factureligneproduit { get; set; }

        public DbSet<MskStoreAPI.Models.Factureclient> Factureclient { get; set; }

        public DbSet<MskStoreAPI.Models.Admin> Admin { get; set; }

        public DbSet<MskStoreAPI.Models.Employee> Employee { get; set; }

        public DbSet<MskStoreAPI.Models.User> User { get; set; }

        public DbSet<MskStoreAPI.Models.Depot>? Depot { get; set; }

        public DbSet<MskStoreAPI.Models.Colonne>? Colonne { get; set; }

        public DbSet<MskStoreAPI.Models.Rangement>? Rangement { get; set; }

        public DbSet<MskStoreAPI.Models.Ticketresto>? Ticketresto { get; set; }

        public DbSet<MskStoreAPI.Models.Typeticketresto>? Typeticketresto { get; set; }

        public DbSet<MskStoreAPI.Models.Fermeturecaisse>? Fermeturecaisse { get; set; }

        public DbSet<MskStoreAPI.Models.Fournisseur>? Fournisseur { get; set; }

        public DbSet<MskStoreAPI.Models.Commandefournisseur>? Commandefournisseur { get; set; }

        public DbSet<MskStoreAPI.Models.Photoproduit>? Photoproduit { get; set; }

        public DbSet<MskStoreAPI.Models.Commandelignefournisseur>? Commandelignefournisseur { get; set; }

        public DbSet<MskStoreAPI.Models.Clientweb>? Clientweb { get; set; }

        public DbSet<MskStoreAPI.Models.Commandeclient>? Commandeclient { get; set; }

        public DbSet<MskStoreAPI.Models.Commandeligneclient>? Commandeligneclient { get; set; }

        public DbSet<MskStoreAPI.Models.Produitdefectueux>? Produitdefectueux { get; set; }

        public DbSet<MskStoreAPI.Models.Timbrefiscal>? Timbrefiscal { get; set; }

        public DbSet<MskStoreAPI.Models.Devis>? Devis { get; set; }



    }
}
