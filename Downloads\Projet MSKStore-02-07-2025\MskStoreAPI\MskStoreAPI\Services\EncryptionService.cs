using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;

namespace MskStoreAPI.Services
{
    /// <summary>
    /// AES-256 encryption service for securing sensitive data
    /// </summary>
    public class EncryptionService : IEncryptionService
    {
        private readonly byte[] _key;
        private readonly byte[] _iv;

        public EncryptionService(IConfiguration configuration)
        {
            // Get encryption key from configuration (should be stored securely in production)
            var keyString = configuration["Security:EncryptionKey"] ?? GenerateSecureKey();
            var ivString = configuration["Security:EncryptionIV"] ?? GenerateSecureIV();

            _key = Convert.FromBase64String(keyString);
            _iv = Convert.FromBase64String(ivString);

            // Ensure key is 256 bits (32 bytes) for AES-256
            if (_key.Length != 32)
            {
                throw new ArgumentException("Encryption key must be 256 bits (32 bytes) for AES-256");
            }

            // Ensure IV is 128 bits (16 bytes)
            if (_iv.Length != 16)
            {
                throw new ArgumentException("Initialization Vector must be 128 bits (16 bytes)");
            }
        }

        public string Encrypt(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            try
            {
                using (var aes = Aes.Create())
                {
                    aes.Key = _key;
                    aes.IV = _iv;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    using (var encryptor = aes.CreateEncryptor())
                    using (var msEncrypt = new MemoryStream())
                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (var swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                        swEncrypt.Close();
                        return Convert.ToBase64String(msEncrypt.ToArray());
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Encryption failed", ex);
            }
        }

        public string Decrypt(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                return string.Empty;

            try
            {
                var cipherBytes = Convert.FromBase64String(cipherText);

                using (var aes = Aes.Create())
                {
                    aes.Key = _key;
                    aes.IV = _iv;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    using (var decryptor = aes.CreateDecryptor())
                    using (var msDecrypt = new MemoryStream(cipherBytes))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var srDecrypt = new StreamReader(csDecrypt))
                    {
                        return srDecrypt.ReadToEnd();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Decryption failed", ex);
            }
        }

        public string EncryptSensitiveData(string sensitiveData)
        {
            if (string.IsNullOrEmpty(sensitiveData))
                return string.Empty;

            // Add additional salt for sensitive data
            var saltedData = $"SENSITIVE:{sensitiveData}:{DateTime.UtcNow.Ticks}";
            return Encrypt(saltedData);
        }

        public string DecryptSensitiveData(string encryptedData)
        {
            if (string.IsNullOrEmpty(encryptedData))
                return string.Empty;

            var decryptedData = Decrypt(encryptedData);
            
            // Remove salt prefix and timestamp
            if (decryptedData.StartsWith("SENSITIVE:"))
            {
                var parts = decryptedData.Split(':');
                if (parts.Length >= 3)
                {
                    return parts[1]; // Return the original sensitive data
                }
            }

            return decryptedData;
        }

        public string EncryptFinancialData(decimal amount)
        {
            // Add financial data prefix for identification
            var financialData = $"FINANCIAL:{amount:F2}:{DateTime.UtcNow.Ticks}";
            return Encrypt(financialData);
        }

        public decimal DecryptFinancialData(string encryptedAmount)
        {
            if (string.IsNullOrEmpty(encryptedAmount))
                return 0;

            var decryptedData = Decrypt(encryptedAmount);
            
            // Remove financial prefix and timestamp
            if (decryptedData.StartsWith("FINANCIAL:"))
            {
                var parts = decryptedData.Split(':');
                if (parts.Length >= 3 && decimal.TryParse(parts[1], out var amount))
                {
                    return amount;
                }
            }

            // Fallback: try to parse as decimal directly
            if (decimal.TryParse(decryptedData, out var fallbackAmount))
            {
                return fallbackAmount;
            }

            return 0;
        }

        /// <summary>
        /// Generates a secure 256-bit key for AES-256 encryption
        /// </summary>
        private static string GenerateSecureKey()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var keyBytes = new byte[32]; // 256 bits
                rng.GetBytes(keyBytes);
                return Convert.ToBase64String(keyBytes);
            }
        }

        /// <summary>
        /// Generates a secure 128-bit IV for AES encryption
        /// </summary>
        private static string GenerateSecureIV()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var ivBytes = new byte[16]; // 128 bits
                rng.GetBytes(ivBytes);
                return Convert.ToBase64String(ivBytes);
            }
        }

        /// <summary>
        /// Generates secure encryption keys for production use
        /// Call this method once to generate keys, then store them securely
        /// </summary>
        public static (string Key, string IV) GenerateProductionKeys()
        {
            return (GenerateSecureKey(), GenerateSecureIV());
        }
    }
}
