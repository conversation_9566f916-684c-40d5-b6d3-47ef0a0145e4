<div class="card">
    <div class="card-body">
      <div class="row">
        <div class="col-sm-6 text-left">
          <h2>{{ 'numero' | translate }} {{commandeFournisseur?.id}}</h2>
          <p>{{ 'Date' | translate }}: {{commandeFournisseur?.datecommande | date: 'dd/MM/yyyy à HH:mm:ss'}}</p>
          <p>{{ 'cachier' | translate }}: {{username}}</p>
          <p>{{ 'fournisseur' | translate }}: {{fournisseurName}} </p>
          <p>{{ 'etat' | translate }}: {{commandeFournisseur?.etat}} </p>
          <p *ngIf="commandeFournisseur.payee">payée</p>
        </div>
        <div class="col-sm-6 text-right">
          <img src="favicon.ico" alt="Logo" height="50" width="auto" />
          <h3>MskStore</h3>
          <p>{{ 'Tél' | translate }}: 51019923</p>
        </div>
      </div>
     
      <hr />
      <div class="row">
        <div class="col-md-12">
          <h4>{{ 'Lignesdeproduits' | translate }}:</h4>
          <div class="table-responsive">
            <table class="table">
              <thead>
                <tr>
                  <th>{{ 'Produit' | translate }}</th>
                  <th>{{ 'Quantité' | translate }}</th>
                  <th>{{ 'Prixunitaire' | translate }} </th>
                  <th> {{ 'Total' | translate }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let ligne of commandeFournisseur?.commandelignefournisseur">
                  <td>{{ ligne.produit?.nom }}</td>
                  <td>{{ ligne.quantite }}</td>
                  <td>{{ ligne.prix }}</td>
                  <td>{{ (ligne.prix * ligne.quantite) }} </td>
  
                </tr>
               
              </tbody>
            </table>
           
          </div>
        </div>
      </div>
      <hr />
      <table class="float-right">
        <tr>
          <td ><h2>{{ 'Total' | translate }}:</h2></td>
          <td><h3>{{commandeFournisseur?.total }}</h3></td>
        </tr>
        
        <hr/>

        <button class="btn btn-primary mr-2" (click)="onModify()">{{ 'Modify' | translate }}</button>
            <button class="btn btn-danger" (click)="onDelete()">{{ 'Delete' | translate }}</button>
     
      </table>
    </div>
  </div>