-- Create Security Tables for MSKStore Database
-- This script adds the security tables to the existing SQLite database

-- Create SecurityAuditLogs table
CREATE TABLE IF NOT EXISTS "SecurityAuditLogs" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_SecurityAuditLogs" PRIMARY KEY AUTOINCREMENT,
    "Timestamp" TEXT NOT NULL,
    "EventType" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "UserId" INTEGER NULL,
    "IpAddress" TEXT NULL,
    "UserAgent" TEXT NULL,
    "Severity" TEXT NOT NULL,
    "Success" INTEGER NOT NULL
);

-- Create LoginAttempts table
CREATE TABLE IF NOT EXISTS "LoginAttempts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_LoginAttempts" PRIMARY KEY AUTOINCREMENT,
    "Email" TEXT NOT NULL,
    "IpAddress" TEXT NOT NULL,
    "AttemptTime" TEXT NOT NULL,
    "Success" INTEGER NOT NULL,
    "FailureReason" TEXT NULL
);

-- <PERSON>reate AccountLockouts table
CREATE TABLE IF NOT EXISTS "AccountLockouts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountLockouts" PRIMARY KEY AUTOINCREMENT,
    "Email" TEXT NOT NULL,
    "LockoutTime" TEXT NOT NULL,
    "UnlockTime" TEXT NULL,
    "FailedAttempts" INTEGER NOT NULL,
    "IsActive" INTEGER NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "IX_SecurityAuditLogs_Timestamp" ON "SecurityAuditLogs" ("Timestamp");
CREATE INDEX IF NOT EXISTS "IX_SecurityAuditLogs_EventType" ON "SecurityAuditLogs" ("EventType");
CREATE INDEX IF NOT EXISTS "IX_SecurityAuditLogs_UserId" ON "SecurityAuditLogs" ("UserId");
CREATE INDEX IF NOT EXISTS "IX_LoginAttempts_Email" ON "LoginAttempts" ("Email");
CREATE INDEX IF NOT EXISTS "IX_LoginAttempts_AttemptTime" ON "LoginAttempts" ("AttemptTime");
CREATE INDEX IF NOT EXISTS "IX_AccountLockouts_Email" ON "AccountLockouts" ("Email");
CREATE INDEX IF NOT EXISTS "IX_AccountLockouts_IsActive" ON "AccountLockouts" ("IsActive");
