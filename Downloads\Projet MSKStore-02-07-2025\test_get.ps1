try {
    Write-Host "Testing GET /api/Users endpoint..."
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method GET
    Write-Host "SUCCESS! Response:"
    Write-Host ($response | ConvertTo-Json)
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)"
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
    }
}
