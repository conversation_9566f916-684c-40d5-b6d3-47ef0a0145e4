using Microsoft.AspNetCore.Http;
using MskStoreAPI.Services;
using System.Threading.Tasks;

namespace MskStoreAPI.Middleware
{
    /// <summary>
    /// Middleware for rate limiting API requests
    /// </summary>
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;

        public RateLimitingMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, ISecurityService securityService)
        {
            var clientIp = securityService.GetClientIpAddress(context);
            var endpoint = context.Request.Path.Value ?? "unknown";

            // Check rate limiting
            if (await securityService.IsRateLimitExceededAsync(clientIp, endpoint))
            {
                context.Response.StatusCode = 429; // Too Many Requests
                context.Response.Headers.Add("Retry-After", "60");
                await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
                return;
            }

            await _next(context);
        }
    }
}
