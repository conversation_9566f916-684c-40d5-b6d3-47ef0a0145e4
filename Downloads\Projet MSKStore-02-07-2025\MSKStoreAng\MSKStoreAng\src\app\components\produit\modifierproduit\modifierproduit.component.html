  <div class="modern-form-container">
    <!-- Modern Form Header -->
    <div class="modern-form-header">
      <h2 class="form-title">
        <i data-feather="edit" class="title-icon"></i>
        {{ 'updateProduct' | translate }}
      </h2>
      <p class="form-subtitle">Update product information and manage photos</p>
    </div>

    <form (submit)="onSubmit()" class="modern-form">
      <div *ngIf="produit">

        <!-- Existing Photos Section -->
        <div class="modern-form-section" *ngIf="produit?.photoproduits?.length > 0">
          <h4 class="section-title">
            <i data-feather="image" class="section-icon"></i>
            Current Product Photos
          </h4>

          <div class="photo-management-container">
            <ngb-carousel [interval]="false" class="modern-photo-carousel">
              <ng-template ngbSlide *ngFor="let photo of produit.photoproduits">
                <div class="photo-slide">
                  <div class="photo-container">
                    <img [src]="photo.imageUrl" [alt]="produit.nom" class="product-photo">
                    <button
                      type="button"
                      class="btn-modern btn-danger photo-delete-btn"
                      (click)="removeImage(photo.id)"
                      [attr.aria-label]="'Delete photo'">
                      <i data-feather="trash-2" class="btn-icon"></i>
                    </button>
                  </div>
                </div>
              </ng-template>
            </ngb-carousel>
          </div>
        </div>

        <!-- Basic Information Section -->
        <div class="modern-form-section">
          <h4 class="section-title">
            <i data-feather="info" class="section-icon"></i>
            Basic Information
          </h4>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="nom">
                <i data-feather="package" class="label-icon"></i>
                {{ 'ProductName' | translate }}
                <span class="required">*</span>
              </label>
              <input
                type="text"
                class="form-control modern-input"
                id="nom"
                name="nom"
                [(ngModel)]="produit.nom"
                placeholder="Enter product name"
                required>
            </div>

            <div class="form-group col-md-6">
              <label class="modern-form-label" for="codebar">
                <i data-feather="hash" class="label-icon"></i>
                {{ 'BarCode' | translate }}
                <span class="required">*</span>
              </label>
              <input
                type="number"
                class="form-control modern-input"
                id="codebar"
                name="codebar"
                [(ngModel)]="produit.codebar"
                placeholder="Enter barcode"
                required>
            </div>
          </div>
        </div>


        <!-- Product Details Section -->
        <div class="modern-form-section">
          <h4 class="section-title">
            <i data-feather="file-text" class="section-icon"></i>
            Product Details
          </h4>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="description">
                <i data-feather="file-text" class="label-icon"></i>
                {{ 'Description' | translate }}
              </label>
              <textarea
                class="form-control modern-textarea"
                id="description"
                name="description"
                [(ngModel)]="produit.description"
                placeholder="Enter product description"
                rows="4"></textarea>
            </div>

            <div class="form-group col-md-6">
              <label class="modern-form-label" for="color">
                <i data-feather="palette" class="label-icon"></i>
                {{ 'Color' | translate }}
              </label>
              <input
                type="text"
                class="form-control modern-input"
                id="color"
                name="color"
                [(ngModel)]="produit.color"
                placeholder="Enter product color">
            </div>
          </div>
        </div>

        <!-- Pricing Information Section -->
        <div class="modern-form-section">
          <h4 class="section-title">
            <i data-feather="dollar-sign" class="section-icon"></i>
            Pricing Information
          </h4>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="price">
                <i data-feather="tag" class="label-icon"></i>
                {{ 'Price' | translate }}
                <span class="required">*</span>
              </label>
              <input
                type="number"
                class="form-control modern-input"
                id="price"
                name="price"
                [(ngModel)]="produit.prixvente"
                placeholder="Enter selling price"
                min="0"
                step="0.01"
                required>
            </div>

            <div class="form-group col-md-6">
              <label class="modern-form-label" for="prixFournisseur">
                <i data-feather="shopping-cart" class="label-icon"></i>
                {{ 'ProviderPrice' | translate }}
              </label>
              <input
                type="number"
                class="form-control modern-input"
                id="prixFournisseur"
                name="prixFournisseur"
                [(ngModel)]="produit.prixFournisseur"
                placeholder="Enter provider price"
                min="0"
                step="0.01">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="reductionprix">
                <i data-feather="percent" class="label-icon"></i>
                {{ 'PriceReduction' | translate }}
              </label>
              <input
                type="number"
                class="form-control modern-input"
                id="reductionprix"
                name="reductionprix"
                [(ngModel)]="produit.reductionprix"
                placeholder="Enter price reduction"
                min="0"
                step="0.01">
            </div>

            <div class="form-group col-md-6">
              <label class="modern-form-label" for="taxe">
                <i data-feather="calculator" class="label-icon"></i>
                {{ 'taxe' | translate }}
              </label>
              <input
                type="number"
                class="form-control modern-input"
                id="taxe"
                name="taxe"
                [(ngModel)]="produit.taxe"
                placeholder="Enter tax amount"
                min="0"
                step="0.01">
            </div>
          </div>
        </div>

        <!-- Category and Expiration Section -->
        <div class="modern-form-section">
          <h4 class="section-title">
            <i data-feather="folder" class="section-icon"></i>
            Category & Expiration
          </h4>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="category">
                <i data-feather="folder" class="label-icon"></i>
                {{ 'Category' | translate }}
                <span class="required">*</span>
              </label>
              <select
                id="category"
                name="category"
                class="form-control modern-select"
                [(ngModel)]="produit.categorieid"
                required>
                <option value="" disabled>Select a category</option>
                <option *ngFor="let category of categories" [value]="category.id">{{category.id}} - {{category.titre}}</option>
              </select>
            </div>

            <div class="form-group col-md-6">
              <label class="modern-form-label" for="dateexpiration">
                <i data-feather="calendar" class="label-icon"></i>
                {{ 'ExpirationDate' | translate }}
              </label>
              <input
                type="date"
                class="form-control modern-input"
                id="dateexpiration"
                name="dateexpiration"
                [(ngModel)]="produit.dateexpiration"
                [ngModelOptions]="{timezone: 'local'}">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group col-md-6">
              <div class="modern-checkbox-container">
                <label class="modern-checkbox">
                  <input
                    type="checkbox"
                    id="ispiece"
                    name="ispiece"
                    [(ngModel)]="produit.ispiece">
                  <span class="checkmark"></span>
                  <span class="checkbox-label">
                    <i data-feather="package" class="checkbox-icon"></i>
                    {{ 'IsPiece' | translate }}
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Piece Configuration Section -->
        <div class="modern-form-section" *ngIf="produit.ispiece">
          <h4 class="section-title">
            <i data-feather="layers" class="section-icon"></i>
            Piece Configuration
          </h4>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="produitparent">
                <i data-feather="link" class="label-icon"></i>
                {{ 'IsPieceof' | translate }}
                <span class="current-parent" *ngIf="produit?.produitparententity?.nom">
                  (Current: {{ produit?.produitparententity?.nom }})
                </span>
              </label>
              <input
                type="number"
                class="form-control modern-input"
                id="produitparent"
                name="produitparent"
                [(ngModel)]="produit.produitparent"
                (ngModelChange)="onProduitParentChange()"
                placeholder="Enter parent product ID">
              <div class="parent-info" *ngIf="parent">
                <i data-feather="info" class="info-icon"></i>
                <span class="info-text">{{ 'ispieceof' | translate }} {{parent.nom}}</span>
              </div>
            </div>

            <div class="form-group col-md-6">
              <label class="modern-form-label" for="qteInPacket">
                <i data-feather="package" class="label-icon"></i>
                {{ 'QuantityinPacket' | translate }}
              </label>
              <input
                type="number"
                class="form-control modern-input"
                id="qteInPacket"
                name="qteInPacket"
                [(ngModel)]="produit.qteInPacket"
                placeholder="Enter quantity in packet"
                min="1">
            </div>
          </div>
        </div>

        <!-- Add New Photos Section -->
        <div class="modern-form-section">
          <h4 class="section-title">
            <i data-feather="plus" class="section-icon"></i>
            Add New Photos
          </h4>

          <div class="form-group">
            <label class="modern-form-label" for="newPhotos">
              <i data-feather="upload" class="label-icon"></i>
              {{ 'photo' | translate }}
            </label>
            <div class="file-upload-container">
              <input
                type="file"
                class="form-control modern-file-input"
                id="newPhotos"
                name="newPhotos"
                (change)="onFileSelect($event)"
                multiple
                accept="image/*">
              <div class="file-upload-hint">
                <i data-feather="upload-cloud" class="hint-icon"></i>
                <span>Select additional images for this product</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="modern-form-actions">
          <button type="submit" class="btn-modern btn-primary">
            <i data-feather="save" class="btn-icon"></i>
            {{ 'updateProduct' | translate }}
          </button>
          <button type="button" class="btn-modern btn-secondary" onclick="history.back()">
            <i data-feather="x" class="btn-icon"></i>
            Cancel
          </button>
        </div>
      </div>
    </form>
  </div>
