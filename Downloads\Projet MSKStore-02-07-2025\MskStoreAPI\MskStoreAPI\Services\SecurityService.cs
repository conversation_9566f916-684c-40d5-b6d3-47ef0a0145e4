using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MskStoreAPI.Data;
using MskStoreAPI.Models.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;

namespace MskStoreAPI.Services
{
    /// <summary>
    /// Security service implementation for handling security operations
    /// </summary>
    public class SecurityService : ISecurityService
    {
        private readonly DataContext _context;
        private readonly IConfiguration _configuration;
        private readonly Dictionary<string, List<DateTime>> _rateLimitCache;

        public SecurityService(DataContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
            _rateLimitCache = new Dictionary<string, List<DateTime>>();
        }

        public async Task LogSecurityEventAsync(string eventType, string description, string? userId = null,
            string? ipAddress = null, string? userAgent = null, string severity = "Info",
            bool isSuccessful = true, string? additionalData = null)
        {
            try
            {
                var auditLog = new SecurityAuditLog
                {
                    EventType = eventType,
                    Description = description,
                    UserId = userId,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    Severity = severity,
                    IsSuccessful = isSuccessful,
                    AdditionalData = additionalData,
                    Timestamp = DateTime.UtcNow
                };

                _context.SecurityAuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log to system logger as fallback
                Console.WriteLine($"Failed to log security event: {ex.Message}");
            }
        }

        public async Task RecordLoginAttemptAsync(string email, string ipAddress, bool isSuccessful,
            string? userAgent = null, string? failureReason = null, int? userId = null)
        {
            try
            {
                var loginAttempt = new LoginAttempt
                {
                    Email = email,
                    IpAddress = ipAddress,
                    IsSuccessful = isSuccessful,
                    UserAgent = userAgent,
                    FailureReason = failureReason,
                    UserId = userId,
                    AttemptTime = DateTime.UtcNow
                };

                _context.LoginAttempts.Add(loginAttempt);
                await _context.SaveChangesAsync();

                // Log security event
                await LogSecurityEventAsync(
                    isSuccessful ? SecurityEventTypes.LOGIN_SUCCESS : SecurityEventTypes.LOGIN_FAILED,
                    $"Login attempt for {email}",
                    userId?.ToString(),
                    ipAddress,
                    userAgent,
                    isSuccessful ? SecuritySeverity.LOW : SecuritySeverity.MEDIUM,
                    isSuccessful,
                    failureReason
                );
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to record login attempt: {ex.Message}");
            }
        }

        public async Task<bool> ShouldLockAccountAsync(string email, string ipAddress)
        {
            try
            {
                var maxAttempts = _configuration.GetValue<int>("Security:PasswordPolicy:MaxFailedAttempts", 5);
                var windowMinutes = _configuration.GetValue<int>("Security:RateLimiting:LoginAttempts:WindowMinutes", 15);

                var cutoffTime = DateTime.UtcNow.AddMinutes(-windowMinutes);

                var recentFailedAttempts = await _context.LoginAttempts
                    .Where(la => la.Email == email && 
                                !la.IsSuccessful && 
                                la.AttemptTime >= cutoffTime)
                    .CountAsync();

                return recentFailedAttempts >= maxAttempts;
            }
            catch
            {
                return false;
            }
        }

        public async Task LockAccountAsync(int userId, string email, string ipAddress, string reason)
        {
            try
            {
                var lockoutDuration = _configuration.GetValue<int>("Security:PasswordPolicy:LockoutDurationMinutes", 30);

                var lockout = new AccountLockout
                {
                    UserId = userId,
                    Email = email,
                    IpAddress = ipAddress,
                    Reason = reason,
                    LockoutTime = DateTime.UtcNow,
                    UnlockTime = DateTime.UtcNow.AddMinutes(lockoutDuration),
                    IsActive = true
                };

                _context.AccountLockouts.Add(lockout);
                await _context.SaveChangesAsync();

                await LogSecurityEventAsync(
                    SecurityEventTypes.ACCOUNT_LOCKED,
                    $"Account locked for {email}: {reason}",
                    userId.ToString(),
                    ipAddress,
                    null,
                    SecuritySeverity.HIGH,
                    true,
                    $"Lockout duration: {lockoutDuration} minutes"
                );
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to lock account: {ex.Message}");
            }
        }

        public async Task<bool> IsAccountLockedAsync(string email)
        {
            try
            {
                var activeLockout = await _context.AccountLockouts
                    .Where(al => al.Email == email && 
                                al.IsActive && 
                                (al.UnlockTime == null || al.UnlockTime > DateTime.UtcNow))
                    .FirstOrDefaultAsync();

                return activeLockout != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task UnlockAccountAsync(string email)
        {
            try
            {
                var activeLockouts = await _context.AccountLockouts
                    .Where(al => al.Email == email && al.IsActive)
                    .ToListAsync();

                foreach (var lockout in activeLockouts)
                {
                    lockout.IsActive = false;
                    lockout.UnlockTime = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                await LogSecurityEventAsync(
                    SecurityEventTypes.ACCOUNT_UNLOCKED,
                    $"Account unlocked for {email}",
                    null,
                    null,
                    null,
                    SecuritySeverity.MEDIUM
                );
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to unlock account: {ex.Message}");
            }
        }

        public async Task<(bool IsValid, string[] Errors)> ValidatePasswordAsync(string password)
        {
            var errors = new List<string>();

            if (string.IsNullOrEmpty(password))
            {
                errors.Add("Password is required");
                return (false, errors.ToArray());
            }

            var minLength = _configuration.GetValue<int>("Security:PasswordPolicy:MinLength", 8);
            var requireUppercase = _configuration.GetValue<bool>("Security:PasswordPolicy:RequireUppercase", true);
            var requireLowercase = _configuration.GetValue<bool>("Security:PasswordPolicy:RequireLowercase", true);
            var requireDigit = _configuration.GetValue<bool>("Security:PasswordPolicy:RequireDigit", true);
            var requireSpecialChar = _configuration.GetValue<bool>("Security:PasswordPolicy:RequireSpecialChar", true);

            if (password.Length < minLength)
                errors.Add($"Password must be at least {minLength} characters long");

            if (requireUppercase && !password.Any(char.IsUpper))
                errors.Add("Password must contain at least one uppercase letter");

            if (requireLowercase && !password.Any(char.IsLower))
                errors.Add("Password must contain at least one lowercase letter");

            if (requireDigit && !password.Any(char.IsDigit))
                errors.Add("Password must contain at least one digit");

            if (requireSpecialChar && !password.Any(c => !char.IsLetterOrDigit(c)))
                errors.Add("Password must contain at least one special character");

            return (errors.Count == 0, errors.ToArray());
        }

        public async Task<bool> IsRateLimitExceededAsync(string identifier, string operation)
        {
            try
            {
                var maxRequests = _configuration.GetValue<int>("Security:RateLimiting:ApiCalls:MaxRequests", 100);
                var windowMinutes = _configuration.GetValue<int>("Security:RateLimiting:ApiCalls:WindowMinutes", 1);

                var key = $"{identifier}:{operation}";
                var now = DateTime.UtcNow;
                var windowStart = now.AddMinutes(-windowMinutes);

                if (!_rateLimitCache.ContainsKey(key))
                {
                    _rateLimitCache[key] = new List<DateTime>();
                }

                // Remove old entries
                _rateLimitCache[key].RemoveAll(time => time < windowStart);

                // Check if limit exceeded
                if (_rateLimitCache[key].Count >= maxRequests)
                {
                    await LogSecurityEventAsync(
                        SecurityEventTypes.RATE_LIMIT_EXCEEDED,
                        $"Rate limit exceeded for {identifier} on {operation}",
                        null,
                        identifier,
                        null,
                        SecuritySeverity.MEDIUM,
                        false
                    );
                    return true;
                }

                // Add current request
                _rateLimitCache[key].Add(now);
                return false;
            }
            catch
            {
                return false;
            }
        }

        public string GetClientIpAddress(HttpContext context)
        {
            try
            {
                var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
                if (string.IsNullOrEmpty(ipAddress))
                {
                    ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
                }
                if (string.IsNullOrEmpty(ipAddress))
                {
                    ipAddress = context.Connection.RemoteIpAddress?.ToString();
                }
                return ipAddress ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        public string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // HTML encode to prevent XSS
            return HttpUtility.HtmlEncode(input);
        }

        public bool IsInputSafe(string input)
        {
            if (string.IsNullOrEmpty(input))
                return true;

            // Check for common malicious patterns
            var maliciousPatterns = new[]
            {
                @"<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>",
                @"javascript:",
                @"vbscript:",
                @"onload\s*=",
                @"onerror\s*=",
                @"onclick\s*=",
                @"<iframe",
                @"<object",
                @"<embed",
                @"<link",
                @"<meta",
                @"eval\s*\(",
                @"expression\s*\(",
                @"url\s*\(",
                @"import\s*\("
            };

            foreach (var pattern in maliciousPatterns)
            {
                if (Regex.IsMatch(input, pattern, RegexOptions.IgnoreCase))
                {
                    return false;
                }
            }

            return true;
        }

        public async Task<bool> CheckRateLimitAsync(string identifier, string operation)
        {
            return await IsRateLimitExceededAsync(identifier, operation);
        }

        public async Task LogLoginAttemptAsync(string email, bool isSuccessful, string ipAddress, string? userAgent = null)
        {
            await RecordLoginAttemptAsync(email, ipAddress, isSuccessful, userAgent);
        }

        public async Task HandleFailedLoginAsync(string email, string ipAddress)
        {
            // Check if account should be locked
            if (await ShouldLockAccountAsync(email, ipAddress))
            {
                // Find user to get ID for locking
                var user = await _context.Users.FirstOrDefaultAsync(u => u.authemail == email);
                if (user != null && user.authid.HasValue)
                {
                    await LockAccountAsync(user.authid.Value, email, ipAddress, "Multiple failed login attempts");
                }
            }
        }

        public async Task ResetFailedAttemptsAsync(string email)
        {
            var recentAttempts = await _context.LoginAttempts
                .Where(la => la.Email == email && !la.IsSuccessful && la.AttemptTime >= DateTime.UtcNow.AddMinutes(-15))
                .ToListAsync();

            foreach (var attempt in recentAttempts)
            {
                _context.LoginAttempts.Remove(attempt);
            }

            await _context.SaveChangesAsync();
        }
    }
}
