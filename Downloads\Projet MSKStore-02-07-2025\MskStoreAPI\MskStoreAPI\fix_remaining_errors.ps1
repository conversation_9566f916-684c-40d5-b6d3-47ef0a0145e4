# PowerShell script to fix remaining compilation errors

Write-Host "Fixing remaining compilation errors..." -ForegroundColor Green

# Fix UsersController.cs - More precise replacements
$usersControllerPath = "Controllers\UsersController.cs"
$content = Get-Content $usersControllerPath -Raw

# Fix specific lines with exact patterns
$content = $content -replace '(\s+)user\.id\.ToString\(\),(\s+)"INFO",(\s+)true,(\s+)clientIp,(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\)', '$1user.id.ToString(),$2clientIp,$4Request.Headers["User-Agent"].ToString(),$2"INFO",$3true'

$content = $content -replace '(\s+)null,(\s+)"WARNING",(\s+)false,(\s+)clientIp,(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\)', '$1null,$2clientIp,$4Request.Headers["User-Agent"].ToString(),$2"WARNING",$3false'

$content = $content -replace '(\s+)user\.id,(\s+)"INFO",(\s+)true,(\s+)clientIp,(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\),(\s+)new \{ sessionId, jti, expiresAt \}', '$1user.id.ToString(),$2clientIp,$4Request.Headers["User-Agent"].ToString(),$2"INFO",$3true,$6JsonSerializer.Serialize(new { sessionId, jti, expiresAt })'

Set-Content $usersControllerPath $content

Write-Host "Fixed UsersController.cs" -ForegroundColor Yellow

# Fix PasswordValidationService.cs - More precise replacements
$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath -Raw

# Fix line 161-163: userId -> userId?.ToString()
$content = $content -replace '(\s+)userId\?,(\s+)"WARNING",(\s+)false,(\s+)ipAddress,(\s+)userAgent', '$1userId?.ToString(),$2ipAddress,$4userAgent,$2"WARNING",$3false'

# Fix line 206-208: userId -> userId.ToString()  
$content = $content -replace '(\s+)userId,(\s+)"WARNING",(\s+)false,(\s+)ipAddress,(\s+)userAgent', '$1userId.ToString(),$2ipAddress,$4userAgent,$2"WARNING",$3false'

# Fix line 350: parameter order
$content = $content -replace '(\s+)userId\?\.ToString\(\),(\s+)"INFO",(\s+)true,(\s+)ipAddress,(\s+)userAgent', '$1userId?.ToString(),$2ipAddress,$4userAgent,$2"INFO",$3true'

# Fix line 474-476: userId -> userId.ToString()
$content = $content -replace '(\s+)userId,(\s+)"INFO",(\s+)true,(\s+)ipAddress,(\s+)userAgent', '$1userId.ToString(),$2ipAddress,$4userAgent,$2"INFO",$3true'

Set-Content $passwordServicePath $content

Write-Host "Fixed PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "All fixes applied! Running build..." -ForegroundColor Green
dotnet build
