{"ast": null, "code": "'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nvar _defineProperty = require(\"C:/Users/<USER>/Downloads/Projet MSKStore-02-07-2025/MSKStoreAng/MSKStoreAng/node_modules/@babel/runtime/helpers/defineProperty.js\").default;\n\nconst global = globalThis; // __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\n\nfunction __symbol__(name) {\n  const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n  return symbolPrefix + name;\n}\n\nfunction initZone() {\n  const performance = global['performance'];\n\n  function mark(name) {\n    performance && performance['mark'] && performance['mark'](name);\n  }\n\n  function performanceMeasure(name, label) {\n    performance && performance['measure'] && performance['measure'](name, label);\n  }\n\n  mark('Zone');\n\n  class ZoneImpl {\n    static assertZonePatched() {\n      if (global['Promise'] !== patches['ZoneAwarePromise']) {\n        throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' + 'has been overwritten.\\n' + 'Most likely cause is that a Promise polyfill has been loaded ' + 'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' + 'If you must load one, do so before loading zone.js.)');\n      }\n    }\n\n    static get root() {\n      let zone = ZoneImpl.current;\n\n      while (zone.parent) {\n        zone = zone.parent;\n      }\n\n      return zone;\n    }\n\n    static get current() {\n      return _currentZoneFrame.zone;\n    }\n\n    static get currentTask() {\n      return _currentTask;\n    }\n\n    static __load_patch(name, fn, ignoreDuplicate = false) {\n      if (patches.hasOwnProperty(name)) {\n        // `checkDuplicate` option is defined from global variable\n        // so it works for all modules.\n        // `ignoreDuplicate` can work for the specified module\n        const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n\n        if (!ignoreDuplicate && checkDuplicate) {\n          throw Error('Already loaded patch: ' + name);\n        }\n      } else if (!global['__Zone_disable_' + name]) {\n        const perfName = 'Zone:' + name;\n        mark(perfName);\n        patches[name] = fn(global, ZoneImpl, _api);\n        performanceMeasure(perfName, perfName);\n      }\n    }\n\n    get parent() {\n      return this._parent;\n    }\n\n    get name() {\n      return this._name;\n    }\n\n    constructor(parent, zoneSpec) {\n      _defineProperty(this, \"_parent\", void 0);\n\n      _defineProperty(this, \"_name\", void 0);\n\n      _defineProperty(this, \"_properties\", void 0);\n\n      _defineProperty(this, \"_zoneDelegate\", void 0);\n\n      this._parent = parent;\n      this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n      this._properties = zoneSpec && zoneSpec.properties || {};\n      this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n    }\n\n    get(key) {\n      const zone = this.getZoneWith(key);\n      if (zone) return zone._properties[key];\n    }\n\n    getZoneWith(key) {\n      let current = this;\n\n      while (current) {\n        if (current._properties.hasOwnProperty(key)) {\n          return current;\n        }\n\n        current = current._parent;\n      }\n\n      return null;\n    }\n\n    fork(zoneSpec) {\n      if (!zoneSpec) throw new Error('ZoneSpec required!');\n      return this._zoneDelegate.fork(this, zoneSpec);\n    }\n\n    wrap(callback, source) {\n      if (typeof callback !== 'function') {\n        throw new Error('Expecting function got: ' + callback);\n      }\n\n      const _callback = this._zoneDelegate.intercept(this, callback, source);\n\n      const zone = this;\n      return function () {\n        return zone.runGuarded(_callback, this, arguments, source);\n      };\n    }\n\n    run(callback, applyThis, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n\n      try {\n        return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n\n    runGuarded(callback, applyThis = null, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n\n      try {\n        try {\n          return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n\n    runTask(task, applyThis, applyArgs) {\n      if (task.zone != this) {\n        throw new Error('A task can only be run in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n      }\n\n      const zoneTask = task; // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n      // will run in notScheduled(canceled) state, we should not try to\n      // run such kind of task but just return\n\n      const {\n        type,\n        data: {\n          isPeriodic = false,\n          isRefreshable = false\n        } = {}\n      } = task;\n\n      if (task.state === notScheduled && (type === eventTask || type === macroTask)) {\n        return;\n      }\n\n      const reEntryGuard = task.state != running;\n      reEntryGuard && zoneTask._transitionTo(running, scheduled);\n      const previousTask = _currentTask;\n      _currentTask = zoneTask;\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n\n      try {\n        if (type == macroTask && task.data && !isPeriodic && !isRefreshable) {\n          task.cancelFn = undefined;\n        }\n\n        try {\n          return this._zoneDelegate.invokeTask(this, zoneTask, applyThis, applyArgs);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        // if the task's state is notScheduled or unknown, then it has already been cancelled\n        // we should not reset the state to scheduled\n        const state = task.state;\n\n        if (state !== notScheduled && state !== unknown) {\n          if (type == eventTask || isPeriodic || isRefreshable && state === scheduling) {\n            reEntryGuard && zoneTask._transitionTo(scheduled, running, scheduling);\n          } else {\n            const zoneDelegates = zoneTask._zoneDelegates;\n\n            this._updateTaskCount(zoneTask, -1);\n\n            reEntryGuard && zoneTask._transitionTo(notScheduled, running, notScheduled);\n\n            if (isRefreshable) {\n              zoneTask._zoneDelegates = zoneDelegates;\n            }\n          }\n        }\n\n        _currentZoneFrame = _currentZoneFrame.parent;\n        _currentTask = previousTask;\n      }\n    }\n\n    scheduleTask(task) {\n      if (task.zone && task.zone !== this) {\n        // check if the task was rescheduled, the newZone\n        // should not be the children of the original zone\n        let newZone = this;\n\n        while (newZone) {\n          if (newZone === task.zone) {\n            throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n          }\n\n          newZone = newZone.parent;\n        }\n      }\n\n      task._transitionTo(scheduling, notScheduled);\n\n      const zoneDelegates = [];\n      task._zoneDelegates = zoneDelegates;\n      task._zone = this;\n\n      try {\n        task = this._zoneDelegate.scheduleTask(this, task);\n      } catch (err) {\n        // should set task's state to unknown when scheduleTask throw error\n        // because the err may from reschedule, so the fromState maybe notScheduled\n        task._transitionTo(unknown, scheduling, notScheduled); // TODO: @JiaLiPassion, should we check the result from handleError?\n\n\n        this._zoneDelegate.handleError(this, err);\n\n        throw err;\n      }\n\n      if (task._zoneDelegates === zoneDelegates) {\n        // we have to check because internally the delegate can reschedule the task.\n        this._updateTaskCount(task, 1);\n      }\n\n      if (task.state == scheduling) {\n        task._transitionTo(scheduled, scheduling);\n      }\n\n      return task;\n    }\n\n    scheduleMicroTask(source, callback, data, customSchedule) {\n      return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n    }\n\n    scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n    }\n\n    scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n    }\n\n    cancelTask(task) {\n      if (task.zone != this) throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n\n      if (task.state !== scheduled && task.state !== running) {\n        return;\n      }\n\n      task._transitionTo(canceling, scheduled, running);\n\n      try {\n        this._zoneDelegate.cancelTask(this, task);\n      } catch (err) {\n        // if error occurs when cancelTask, transit the state to unknown\n        task._transitionTo(unknown, canceling);\n\n        this._zoneDelegate.handleError(this, err);\n\n        throw err;\n      }\n\n      this._updateTaskCount(task, -1);\n\n      task._transitionTo(notScheduled, canceling);\n\n      task.runCount = -1;\n      return task;\n    }\n\n    _updateTaskCount(task, count) {\n      const zoneDelegates = task._zoneDelegates;\n\n      if (count == -1) {\n        task._zoneDelegates = null;\n      }\n\n      for (let i = 0; i < zoneDelegates.length; i++) {\n        zoneDelegates[i]._updateTaskCount(task.type, count);\n      }\n    }\n\n  }\n\n  _defineProperty(ZoneImpl, \"__symbol__\", __symbol__);\n\n  const DELEGATE_ZS = {\n    name: '',\n    onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n    onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n    onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n    onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n  };\n\n  class _ZoneDelegate {\n    get zone() {\n      return this._zone;\n    }\n\n    constructor(zone, parentDelegate, zoneSpec) {\n      _defineProperty(this, \"_zone\", void 0);\n\n      _defineProperty(this, \"_taskCounts\", {\n        'microTask': 0,\n        'macroTask': 0,\n        'eventTask': 0\n      });\n\n      _defineProperty(this, \"_parentDelegate\", void 0);\n\n      _defineProperty(this, \"_forkDlgt\", void 0);\n\n      _defineProperty(this, \"_forkZS\", void 0);\n\n      _defineProperty(this, \"_forkCurrZone\", void 0);\n\n      _defineProperty(this, \"_interceptDlgt\", void 0);\n\n      _defineProperty(this, \"_interceptZS\", void 0);\n\n      _defineProperty(this, \"_interceptCurrZone\", void 0);\n\n      _defineProperty(this, \"_invokeDlgt\", void 0);\n\n      _defineProperty(this, \"_invokeZS\", void 0);\n\n      _defineProperty(this, \"_invokeCurrZone\", void 0);\n\n      _defineProperty(this, \"_handleErrorDlgt\", void 0);\n\n      _defineProperty(this, \"_handleErrorZS\", void 0);\n\n      _defineProperty(this, \"_handleErrorCurrZone\", void 0);\n\n      _defineProperty(this, \"_scheduleTaskDlgt\", void 0);\n\n      _defineProperty(this, \"_scheduleTaskZS\", void 0);\n\n      _defineProperty(this, \"_scheduleTaskCurrZone\", void 0);\n\n      _defineProperty(this, \"_invokeTaskDlgt\", void 0);\n\n      _defineProperty(this, \"_invokeTaskZS\", void 0);\n\n      _defineProperty(this, \"_invokeTaskCurrZone\", void 0);\n\n      _defineProperty(this, \"_cancelTaskDlgt\", void 0);\n\n      _defineProperty(this, \"_cancelTaskZS\", void 0);\n\n      _defineProperty(this, \"_cancelTaskCurrZone\", void 0);\n\n      _defineProperty(this, \"_hasTaskDlgt\", void 0);\n\n      _defineProperty(this, \"_hasTaskDlgtOwner\", void 0);\n\n      _defineProperty(this, \"_hasTaskZS\", void 0);\n\n      _defineProperty(this, \"_hasTaskCurrZone\", void 0);\n\n      this._zone = zone;\n      this._parentDelegate = parentDelegate;\n      this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n      this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n      this._forkCurrZone = zoneSpec && (zoneSpec.onFork ? this._zone : parentDelegate._forkCurrZone);\n      this._interceptZS = zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n      this._interceptDlgt = zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n      this._interceptCurrZone = zoneSpec && (zoneSpec.onIntercept ? this._zone : parentDelegate._interceptCurrZone);\n      this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n      this._invokeDlgt = zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n      this._invokeCurrZone = zoneSpec && (zoneSpec.onInvoke ? this._zone : parentDelegate._invokeCurrZone);\n      this._handleErrorZS = zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n      this._handleErrorDlgt = zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n      this._handleErrorCurrZone = zoneSpec && (zoneSpec.onHandleError ? this._zone : parentDelegate._handleErrorCurrZone);\n      this._scheduleTaskZS = zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n      this._scheduleTaskDlgt = zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n      this._scheduleTaskCurrZone = zoneSpec && (zoneSpec.onScheduleTask ? this._zone : parentDelegate._scheduleTaskCurrZone);\n      this._invokeTaskZS = zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n      this._invokeTaskDlgt = zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n      this._invokeTaskCurrZone = zoneSpec && (zoneSpec.onInvokeTask ? this._zone : parentDelegate._invokeTaskCurrZone);\n      this._cancelTaskZS = zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n      this._cancelTaskDlgt = zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n      this._cancelTaskCurrZone = zoneSpec && (zoneSpec.onCancelTask ? this._zone : parentDelegate._cancelTaskCurrZone);\n      this._hasTaskZS = null;\n      this._hasTaskDlgt = null;\n      this._hasTaskDlgtOwner = null;\n      this._hasTaskCurrZone = null;\n      const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n      const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n\n      if (zoneSpecHasTask || parentHasTask) {\n        // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n        // a case all task related interceptors must go through this ZD. We can't short circuit it.\n        this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n        this._hasTaskDlgt = parentDelegate;\n        this._hasTaskDlgtOwner = this;\n        this._hasTaskCurrZone = this._zone;\n\n        if (!zoneSpec.onScheduleTask) {\n          this._scheduleTaskZS = DELEGATE_ZS;\n          this._scheduleTaskDlgt = parentDelegate;\n          this._scheduleTaskCurrZone = this._zone;\n        }\n\n        if (!zoneSpec.onInvokeTask) {\n          this._invokeTaskZS = DELEGATE_ZS;\n          this._invokeTaskDlgt = parentDelegate;\n          this._invokeTaskCurrZone = this._zone;\n        }\n\n        if (!zoneSpec.onCancelTask) {\n          this._cancelTaskZS = DELEGATE_ZS;\n          this._cancelTaskDlgt = parentDelegate;\n          this._cancelTaskCurrZone = this._zone;\n        }\n      }\n    }\n\n    fork(targetZone, zoneSpec) {\n      return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) : new ZoneImpl(targetZone, zoneSpec);\n    }\n\n    intercept(targetZone, callback, source) {\n      return this._interceptZS ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) : callback;\n    }\n\n    invoke(targetZone, callback, applyThis, applyArgs, source) {\n      return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) : callback.apply(applyThis, applyArgs);\n    }\n\n    handleError(targetZone, error) {\n      return this._handleErrorZS ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) : true;\n    }\n\n    scheduleTask(targetZone, task) {\n      let returnTask = task;\n\n      if (this._scheduleTaskZS) {\n        if (this._hasTaskZS) {\n          returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n        }\n\n        returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n        if (!returnTask) returnTask = task;\n      } else {\n        if (task.scheduleFn) {\n          task.scheduleFn(task);\n        } else if (task.type == microTask) {\n          scheduleMicroTask(task);\n        } else {\n          throw new Error('Task is missing scheduleFn.');\n        }\n      }\n\n      return returnTask;\n    }\n\n    invokeTask(targetZone, task, applyThis, applyArgs) {\n      return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) : task.callback.apply(applyThis, applyArgs);\n    }\n\n    cancelTask(targetZone, task) {\n      let value;\n\n      if (this._cancelTaskZS) {\n        value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n      } else {\n        if (!task.cancelFn) {\n          throw Error('Task is not cancelable');\n        }\n\n        value = task.cancelFn(task);\n      }\n\n      return value;\n    }\n\n    hasTask(targetZone, isEmpty) {\n      // hasTask should not throw error so other ZoneDelegate\n      // can still trigger hasTask callback\n      try {\n        this._hasTaskZS && this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n      } catch (err) {\n        this.handleError(targetZone, err);\n      }\n    }\n\n    _updateTaskCount(type, count) {\n      const counts = this._taskCounts;\n      const prev = counts[type];\n      const next = counts[type] = prev + count;\n\n      if (next < 0) {\n        throw new Error('More tasks executed then were scheduled.');\n      }\n\n      if (prev == 0 || next == 0) {\n        const isEmpty = {\n          microTask: counts['microTask'] > 0,\n          macroTask: counts['macroTask'] > 0,\n          eventTask: counts['eventTask'] > 0,\n          change: type\n        };\n        this.hasTask(this._zone, isEmpty);\n      }\n    }\n\n  }\n\n  class ZoneTask {\n    constructor(type, source, callback, options, scheduleFn, cancelFn) {\n      _defineProperty(this, \"type\", void 0);\n\n      _defineProperty(this, \"source\", void 0);\n\n      _defineProperty(this, \"invoke\", void 0);\n\n      _defineProperty(this, \"callback\", void 0);\n\n      _defineProperty(this, \"data\", void 0);\n\n      _defineProperty(this, \"scheduleFn\", void 0);\n\n      _defineProperty(this, \"cancelFn\", void 0);\n\n      _defineProperty(this, \"_zone\", null);\n\n      _defineProperty(this, \"runCount\", 0);\n\n      _defineProperty(this, \"_zoneDelegates\", null);\n\n      _defineProperty(this, \"_state\", 'notScheduled');\n\n      this.type = type;\n      this.source = source;\n      this.data = options;\n      this.scheduleFn = scheduleFn;\n      this.cancelFn = cancelFn;\n\n      if (!callback) {\n        throw new Error('callback is not defined');\n      }\n\n      this.callback = callback;\n      const self = this; // TODO: @JiaLiPassion options should have interface\n\n      if (type === eventTask && options && options.useG) {\n        this.invoke = ZoneTask.invokeTask;\n      } else {\n        this.invoke = function () {\n          return ZoneTask.invokeTask.call(global, self, this, arguments);\n        };\n      }\n    }\n\n    static invokeTask(task, target, args) {\n      if (!task) {\n        task = this;\n      }\n\n      _numberOfNestedTaskFrames++;\n\n      try {\n        task.runCount++;\n        return task.zone.runTask(task, target, args);\n      } finally {\n        if (_numberOfNestedTaskFrames == 1) {\n          drainMicroTaskQueue();\n        }\n\n        _numberOfNestedTaskFrames--;\n      }\n    }\n\n    get zone() {\n      return this._zone;\n    }\n\n    get state() {\n      return this._state;\n    }\n\n    cancelScheduleRequest() {\n      this._transitionTo(notScheduled, scheduling);\n    }\n\n    _transitionTo(toState, fromState1, fromState2) {\n      if (this._state === fromState1 || this._state === fromState2) {\n        this._state = toState;\n\n        if (toState == notScheduled) {\n          this._zoneDelegates = null;\n        }\n      } else {\n        throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? \" or '\" + fromState2 + \"'\" : ''}, was '${this._state}'.`);\n      }\n    }\n\n    toString() {\n      if (this.data && typeof this.data.handleId !== 'undefined') {\n        return this.data.handleId.toString();\n      } else {\n        return Object.prototype.toString.call(this);\n      }\n    } // add toJSON method to prevent cyclic error when\n    // call JSON.stringify(zoneTask)\n\n\n    toJSON() {\n      return {\n        type: this.type,\n        state: this.state,\n        source: this.source,\n        zone: this.zone.name,\n        runCount: this.runCount\n      };\n    }\n\n  } //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  MICROTASK QUEUE\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n\n\n  const symbolSetTimeout = __symbol__('setTimeout');\n\n  const symbolPromise = __symbol__('Promise');\n\n  const symbolThen = __symbol__('then');\n\n  let _microTaskQueue = [];\n  let _isDrainingMicrotaskQueue = false;\n  let nativeMicroTaskQueuePromise;\n\n  function nativeScheduleMicroTask(func) {\n    if (!nativeMicroTaskQueuePromise) {\n      if (global[symbolPromise]) {\n        nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n      }\n    }\n\n    if (nativeMicroTaskQueuePromise) {\n      let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n\n      if (!nativeThen) {\n        // native Promise is not patchable, we need to use `then` directly\n        // issue 1078\n        nativeThen = nativeMicroTaskQueuePromise['then'];\n      }\n\n      nativeThen.call(nativeMicroTaskQueuePromise, func);\n    } else {\n      global[symbolSetTimeout](func, 0);\n    }\n  }\n\n  function scheduleMicroTask(task) {\n    // if we are not running in any task, and there has not been anything scheduled\n    // we must bootstrap the initial task creation by manually scheduling the drain\n    if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n      // We are not running in Task, so we need to kickstart the microtask queue.\n      nativeScheduleMicroTask(drainMicroTaskQueue);\n    }\n\n    task && _microTaskQueue.push(task);\n  }\n\n  function drainMicroTaskQueue() {\n    if (!_isDrainingMicrotaskQueue) {\n      _isDrainingMicrotaskQueue = true;\n\n      while (_microTaskQueue.length) {\n        const queue = _microTaskQueue;\n        _microTaskQueue = [];\n\n        for (let i = 0; i < queue.length; i++) {\n          const task = queue[i];\n\n          try {\n            task.zone.runTask(task, null, null);\n          } catch (error) {\n            _api.onUnhandledError(error);\n          }\n        }\n      }\n\n      _api.microtaskDrainDone();\n\n      _isDrainingMicrotaskQueue = false;\n    }\n  } //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  BOOTSTRAP\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n\n\n  const NO_ZONE = {\n    name: 'NO ZONE'\n  };\n  const notScheduled = 'notScheduled',\n        scheduling = 'scheduling',\n        scheduled = 'scheduled',\n        running = 'running',\n        canceling = 'canceling',\n        unknown = 'unknown';\n  const microTask = 'microTask',\n        macroTask = 'macroTask',\n        eventTask = 'eventTask';\n  const patches = {};\n  const _api = {\n    symbol: __symbol__,\n    currentZoneFrame: () => _currentZoneFrame,\n    onUnhandledError: noop,\n    microtaskDrainDone: noop,\n    scheduleMicroTask: scheduleMicroTask,\n    showUncaughtError: () => !ZoneImpl[__symbol__('ignoreConsoleErrorUncaughtError')],\n    patchEventTarget: () => [],\n    patchOnProperties: noop,\n    patchMethod: () => noop,\n    bindArguments: () => [],\n    patchThen: () => noop,\n    patchMacroTask: () => noop,\n    patchEventPrototype: () => noop,\n    isIEOrEdge: () => false,\n    getGlobalObjects: () => undefined,\n    ObjectDefineProperty: () => noop,\n    ObjectGetOwnPropertyDescriptor: () => undefined,\n    ObjectCreate: () => undefined,\n    ArraySlice: () => [],\n    patchClass: () => noop,\n    wrapWithCurrentZone: () => noop,\n    filterProperties: () => [],\n    attachOriginToPatched: () => noop,\n    _redefineProperty: () => noop,\n    patchCallbacks: () => noop,\n    nativeScheduleMicroTask: nativeScheduleMicroTask\n  };\n  let _currentZoneFrame = {\n    parent: null,\n    zone: new ZoneImpl(null, null)\n  };\n  let _currentTask = null;\n  let _numberOfNestedTaskFrames = 0;\n\n  function noop() {}\n\n  performanceMeasure('Zone', 'Zone');\n  return ZoneImpl;\n}\n\nfunction loadZone() {\n  // if global['Zone'] already exists (maybe zone.js was already loaded or\n  // some other lib also registered a global object named Zone), we may need\n  // to throw an error, but sometimes user may not want this error.\n  // For example,\n  // we have two web pages, page1 includes zone.js, page2 doesn't.\n  // and the 1st time user load page1 and page2, everything work fine,\n  // but when user load page2 again, error occurs because global['Zone'] already exists.\n  // so we add a flag to let user choose whether to throw this error or not.\n  // By default, if existing Zone is from zone.js, we will not throw the error.\n  const global = globalThis;\n  const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n\n  if (global['Zone'] && (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function')) {\n    throw new Error('Zone already loaded.');\n  } // Initialize global `Zone` constant.\n\n\n  global['Zone'] ??= initZone();\n  return global['Zone'];\n}\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n\n/** Object.getOwnPropertyDescriptor */\n\n\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\n\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\n\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\n\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\n\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\n\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\n\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\n\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = __symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\n\n\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = __symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\n\n\nconst TRUE_STR = 'true';\n/** false string const */\n\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\n\nconst ZONE_SYMBOL_PREFIX = __symbol__('');\n\nfunction wrapWithCurrentZone(callback, source) {\n  return Zone.current.wrap(callback, source);\n}\n\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n  return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\n\nconst zoneSymbol = __symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\n\nconst _global = isWindowExists && internalWindow || globalThis;\n\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\n\nfunction bindArguments(args, source) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (typeof args[i] === 'function') {\n      args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n    }\n  }\n\n  return args;\n}\n\nfunction patchPrototype(prototype, fnNames) {\n  const source = prototype.constructor['name'];\n\n  for (let i = 0; i < fnNames.length; i++) {\n    const name = fnNames[i];\n    const delegate = prototype[name];\n\n    if (delegate) {\n      const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n\n      if (!isPropertyWritable(prototypeDesc)) {\n        continue;\n      }\n\n      prototype[name] = (delegate => {\n        const patched = function () {\n          return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n        };\n\n        attachOriginToPatched(patched, delegate);\n        return patched;\n      })(delegate);\n    }\n  }\n}\n\nfunction isPropertyWritable(propertyDesc) {\n  if (!propertyDesc) {\n    return true;\n  }\n\n  if (propertyDesc.writable === false) {\n    return false;\n  }\n\n  return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\n\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope; // Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\n\nconst isNode = !('nw' in _global) && typeof _global.process !== 'undefined' && _global.process.toString() === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']); // we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\n\nconst isMix = typeof _global.process !== 'undefined' && _global.process.toString() === '[object process]' && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst enableBeforeunloadSymbol = zoneSymbol('enable_beforeunload');\n\nconst wrapFn = function (event) {\n  // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n  // event will be undefined, so we need to use window.event\n  event = event || _global.event;\n\n  if (!event) {\n    return;\n  }\n\n  let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n  }\n\n  const target = this || event.target || _global;\n  const listener = target[eventNameSymbol];\n  let result;\n\n  if (isBrowser && target === internalWindow && event.type === 'error') {\n    // window.onerror have different signature\n    // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n    // and onerror callback will prevent default when callback return true\n    const errorEvent = event;\n    result = listener && listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n\n    if (result === true) {\n      event.preventDefault();\n    }\n  } else {\n    result = listener && listener.apply(this, arguments);\n\n    if ( // https://github.com/angular/angular/issues/47579\n    // https://www.w3.org/TR/2011/WD-html5-20110525/history.html#beforeunloadevent\n    // This is the only specific case we should check for. The spec defines that the\n    // `returnValue` attribute represents the message to show the user. When the event\n    // is created, this attribute must be set to the empty string.\n    event.type === 'beforeunload' && // To prevent any breaking changes resulting from this change, given that\n    // it was already causing a significant number of failures in G3, we have hidden\n    // that behavior behind a global configuration flag. Consumers can enable this\n    // flag explicitly if they want the `beforeunload` event to be handled as defined\n    // in the specification.\n    _global[enableBeforeunloadSymbol] && // The IDL event definition is `attribute DOMString returnValue`, so we check whether\n    // `typeof result` is a string.\n    typeof result === 'string') {\n      event.returnValue = result;\n    } else if (result != undefined && !result) {\n      event.preventDefault();\n    }\n  }\n\n  return result;\n};\n\nfunction patchProperty(obj, prop, prototype) {\n  let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n\n  if (!desc && prototype) {\n    // when patch window object, use prototype to check prop exist or not\n    const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n\n    if (prototypeDesc) {\n      desc = {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  } // if the descriptor not exists or is not configurable\n  // just return\n\n\n  if (!desc || !desc.configurable) {\n    return;\n  }\n\n  const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n\n  if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n    return;\n  } // A property descriptor cannot have getter/setter and be writable\n  // deleting the writable and value properties avoids this error:\n  //\n  // TypeError: property descriptors must not specify a value or be writable when a\n  // getter or setter has been specified\n\n\n  delete desc.writable;\n  delete desc.value;\n  const originalDescGet = desc.get;\n  const originalDescSet = desc.set; // slice(2) cuz 'onclick' -> 'click', etc\n\n  const eventName = prop.slice(2);\n  let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n  }\n\n  desc.set = function (newValue) {\n    // In some versions of Windows, the `this` context may be undefined\n    // in on-property callbacks.\n    // To handle this edge case, we check if `this` is falsy and\n    // fallback to `_global` if needed.\n    let target = this;\n\n    if (!target && obj === _global) {\n      target = _global;\n    }\n\n    if (!target) {\n      return;\n    }\n\n    const previousValue = target[eventNameSymbol];\n\n    if (typeof previousValue === 'function') {\n      target.removeEventListener(eventName, wrapFn);\n    } // https://github.com/angular/zone.js/issues/978\n    // If an inline handler (like `onload`) was defined before zone.js was loaded,\n    // call the original descriptor's setter to clean it up.\n\n\n    originalDescSet?.call(target, null);\n    target[eventNameSymbol] = newValue;\n\n    if (typeof newValue === 'function') {\n      target.addEventListener(eventName, wrapFn, false);\n    }\n  }; // The getter would return undefined for unassigned properties but the default value of an\n  // unassigned property is null\n\n\n  desc.get = function () {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n\n    if (!target && obj === _global) {\n      target = _global;\n    }\n\n    if (!target) {\n      return null;\n    }\n\n    const listener = target[eventNameSymbol];\n\n    if (listener) {\n      return listener;\n    } else if (originalDescGet) {\n      // result will be null when use inline event attribute,\n      // such as <button onclick=\"func();\">OK</button>\n      // because the onclick function is internal raw uncompiled handler\n      // the onclick will be evaluated when first time event was triggered or\n      // the property is accessed, https://github.com/angular/zone.js/issues/525\n      // so we should use original native get to retrieve the handler\n      let value = originalDescGet.call(this);\n\n      if (value) {\n        desc.set.call(this, value);\n\n        if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n          target.removeAttribute(prop);\n        }\n\n        return value;\n      }\n    }\n\n    return null;\n  };\n\n  ObjectDefineProperty(obj, prop, desc);\n  obj[onPropPatchedSymbol] = true;\n}\n\nfunction patchOnProperties(obj, properties, prototype) {\n  if (properties) {\n    for (let i = 0; i < properties.length; i++) {\n      patchProperty(obj, 'on' + properties[i], prototype);\n    }\n  } else {\n    const onProperties = [];\n\n    for (const prop in obj) {\n      if (prop.slice(0, 2) == 'on') {\n        onProperties.push(prop);\n      }\n    }\n\n    for (let j = 0; j < onProperties.length; j++) {\n      patchProperty(obj, onProperties[j], prototype);\n    }\n  }\n}\n\nconst originalInstanceKey = zoneSymbol('originalInstance'); // wrap some native API on `window`\n\nfunction patchClass(className) {\n  const OriginalClass = _global[className];\n  if (!OriginalClass) return; // keep original class in global\n\n  _global[zoneSymbol(className)] = OriginalClass;\n\n  _global[className] = function () {\n    const a = bindArguments(arguments, className);\n\n    switch (a.length) {\n      case 0:\n        this[originalInstanceKey] = new OriginalClass();\n        break;\n\n      case 1:\n        this[originalInstanceKey] = new OriginalClass(a[0]);\n        break;\n\n      case 2:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n        break;\n\n      case 3:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n        break;\n\n      case 4:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n        break;\n\n      default:\n        throw new Error('Arg list too long.');\n    }\n  }; // attach original delegate to patched function\n\n\n  attachOriginToPatched(_global[className], OriginalClass);\n  const instance = new OriginalClass(function () {});\n  let prop;\n\n  for (prop in instance) {\n    // https://bugs.webkit.org/show_bug.cgi?id=44721\n    if (className === 'XMLHttpRequest' && prop === 'responseBlob') continue;\n\n    (function (prop) {\n      if (typeof instance[prop] === 'function') {\n        _global[className].prototype[prop] = function () {\n          return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n        };\n      } else {\n        ObjectDefineProperty(_global[className].prototype, prop, {\n          set: function (fn) {\n            if (typeof fn === 'function') {\n              this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop); // keep callback in wrapped function so we can\n              // use it in Function.prototype.toString to return\n              // the native one.\n\n              attachOriginToPatched(this[originalInstanceKey][prop], fn);\n            } else {\n              this[originalInstanceKey][prop] = fn;\n            }\n          },\n          get: function () {\n            return this[originalInstanceKey][prop];\n          }\n        });\n      }\n    })(prop);\n  }\n\n  for (prop in OriginalClass) {\n    if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n      _global[className][prop] = OriginalClass[prop];\n    }\n  }\n}\n\nfunction patchMethod(target, name, patchFn) {\n  let proto = target;\n\n  while (proto && !proto.hasOwnProperty(name)) {\n    proto = ObjectGetPrototypeOf(proto);\n  }\n\n  if (!proto && target[name]) {\n    // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n    proto = target;\n  }\n\n  const delegateName = zoneSymbol(name);\n  let delegate = null;\n\n  if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n    delegate = proto[delegateName] = proto[name]; // check whether proto[name] is writable\n    // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n\n    const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n\n    if (isPropertyWritable(desc)) {\n      const patchDelegate = patchFn(delegate, delegateName, name);\n\n      proto[name] = function () {\n        return patchDelegate(this, arguments);\n      };\n\n      attachOriginToPatched(proto[name], delegate);\n    }\n  }\n\n  return delegate;\n} // TODO: @JiaLiPassion, support cancel task later if necessary\n\n\nfunction patchMacroTask(obj, funcName, metaCreator) {\n  let setNative = null;\n\n  function scheduleTask(task) {\n    const data = task.data;\n\n    data.args[data.cbIdx] = function () {\n      task.invoke.apply(this, arguments);\n    };\n\n    setNative.apply(data.target, data.args);\n    return task;\n  }\n\n  setNative = patchMethod(obj, funcName, delegate => function (self, args) {\n    const meta = metaCreator(self, args);\n\n    if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n      return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(self, args);\n    }\n  });\n}\n\nfunction attachOriginToPatched(patched, original) {\n  patched[zoneSymbol('OriginalDelegate')] = original;\n}\n\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\n\nfunction isIEOrEdge() {\n  if (isDetectedIEOrEdge) {\n    return ieOrEdge;\n  }\n\n  isDetectedIEOrEdge = true;\n\n  try {\n    const ua = internalWindow.navigator.userAgent;\n\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n      ieOrEdge = true;\n    }\n  } catch (error) {}\n\n  return ieOrEdge;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n// an identifier to tell ZoneTask do not create a new invoke closure\n\n\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n  useG: true\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\n\nfunction prepareEventNames(eventName, eventNameToString) {\n  const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n  const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n  const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n  const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n  zoneSymbolEventNames[eventName] = {};\n  zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n  zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\n\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n  const ADD_EVENT_LISTENER = patchOptions && patchOptions.add || ADD_EVENT_LISTENER_STR;\n  const REMOVE_EVENT_LISTENER = patchOptions && patchOptions.rm || REMOVE_EVENT_LISTENER_STR;\n  const LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.listeners || 'eventListeners';\n  const REMOVE_ALL_LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.rmAll || 'removeAllListeners';\n  const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n  const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n  const PREPEND_EVENT_LISTENER = 'prependListener';\n  const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n\n  const invokeTask = function (task, target, event) {\n    // for better performance, check isRemoved which is set\n    // by removeEventListener\n    if (task.isRemoved) {\n      return;\n    }\n\n    const delegate = task.callback;\n\n    if (typeof delegate === 'object' && delegate.handleEvent) {\n      // create the bind version of handleEvent when invoke\n      task.callback = event => delegate.handleEvent(event);\n\n      task.originalDelegate = delegate;\n    } // invoke static task.invoke\n    // need to try/catch error here, otherwise, the error in one event listener\n    // will break the executions of the other event listeners. Also error will\n    // not remove the event listener when `once` options is true.\n\n\n    let error;\n\n    try {\n      task.invoke(task, target, [event]);\n    } catch (err) {\n      error = err;\n    }\n\n    const options = task.options;\n\n    if (options && typeof options === 'object' && options.once) {\n      // if options.once is true, after invoke once remove listener here\n      // only browser need to do this, nodejs eventEmitter will cal removeListener\n      // inside EventEmitter.once\n      const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n      target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n    }\n\n    return error;\n  };\n\n  function globalCallback(context, event, isCapture) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n\n    if (!event) {\n      return;\n    } // event.target is needed for Samsung TV and SourceBuffer\n    // || global is needed https://github.com/angular/zone.js/issues/190\n\n\n    const target = context || event.target || _global;\n    const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n\n    if (tasks) {\n      const errors = []; // invoke all tasks which attached to current target with given event.type and capture = false\n      // for performance concern, if task.length === 1, just invoke\n\n      if (tasks.length === 1) {\n        const err = invokeTask(tasks[0], target, event);\n        err && errors.push(err);\n      } else {\n        // https://github.com/angular/zone.js/issues/836\n        // copy the tasks array before invoke, to avoid\n        // the callback will remove itself or other listener\n        const copyTasks = tasks.slice();\n\n        for (let i = 0; i < copyTasks.length; i++) {\n          if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n            break;\n          }\n\n          const err = invokeTask(copyTasks[i], target, event);\n          err && errors.push(err);\n        }\n      } // Since there is only one error, we don't need to schedule microTask\n      // to throw the error.\n\n\n      if (errors.length === 1) {\n        throw errors[0];\n      } else {\n        for (let i = 0; i < errors.length; i++) {\n          const err = errors[i];\n          api.nativeScheduleMicroTask(() => {\n            throw err;\n          });\n        }\n      }\n    }\n  } // global shared zoneAwareCallback to handle all event callback with capture = false\n\n\n  const globalZoneAwareCallback = function (event) {\n    return globalCallback(this, event, false);\n  }; // global shared zoneAwareCallback to handle all event callback with capture = true\n\n\n  const globalZoneAwareCaptureCallback = function (event) {\n    return globalCallback(this, event, true);\n  };\n\n  function patchEventTargetMethods(obj, patchOptions) {\n    if (!obj) {\n      return false;\n    }\n\n    let useGlobalCallback = true;\n\n    if (patchOptions && patchOptions.useG !== undefined) {\n      useGlobalCallback = patchOptions.useG;\n    }\n\n    const validateHandler = patchOptions && patchOptions.vh;\n    let checkDuplicate = true;\n\n    if (patchOptions && patchOptions.chkDup !== undefined) {\n      checkDuplicate = patchOptions.chkDup;\n    }\n\n    let returnTarget = false;\n\n    if (patchOptions && patchOptions.rt !== undefined) {\n      returnTarget = patchOptions.rt;\n    }\n\n    let proto = obj;\n\n    while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n      proto = ObjectGetPrototypeOf(proto);\n    }\n\n    if (!proto && obj[ADD_EVENT_LISTENER]) {\n      // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n      proto = obj;\n    }\n\n    if (!proto) {\n      return false;\n    }\n\n    if (proto[zoneSymbolAddEventListener]) {\n      return false;\n    }\n\n    const eventNameToString = patchOptions && patchOptions.eventNameToString; // We use a shared global `taskData` to pass data for `scheduleEventTask`,\n    // eliminating the need to create a new object solely for passing data.\n    // WARNING: This object has a static lifetime, meaning it is not created\n    // each time `addEventListener` is called. It is instantiated only once\n    // and captured by reference inside the `addEventListener` and\n    // `removeEventListener` functions. Do not add any new properties to this\n    // object, as doing so would necessitate maintaining the information\n    // between `addEventListener` calls.\n\n    const taskData = {};\n    const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n    const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] = proto[REMOVE_EVENT_LISTENER];\n    const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] = proto[LISTENERS_EVENT_LISTENER];\n    const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] = proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n    let nativePrependEventListener;\n\n    if (patchOptions && patchOptions.prepend) {\n      nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] = proto[patchOptions.prepend];\n    }\n    /**\n     * This util function will build an option object with passive option\n     * to handle all possible input from the user.\n     */\n\n\n    function buildEventListenerOptions(options, passive) {\n      if (!passive) {\n        return options;\n      }\n\n      if (typeof options === 'boolean') {\n        return {\n          capture: options,\n          passive: true\n        };\n      }\n\n      if (!options) {\n        return {\n          passive: true\n        };\n      }\n\n      if (typeof options === 'object' && options.passive !== false) {\n        return { ...options,\n          passive: true\n        };\n      }\n\n      return options;\n    }\n\n    const customScheduleGlobal = function (task) {\n      // if there is already a task for the eventName + capture,\n      // just return, because we use the shared globalZoneAwareCallback here.\n      if (taskData.isExisting) {\n        return;\n      }\n\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n    };\n    /**\n     * In the context of events and listeners, this function will be\n     * called at the end by `cancelTask`, which, in turn, calls `task.cancelFn`.\n     * Cancelling a task is primarily used to remove event listeners from\n     * the task target.\n     */\n\n\n    const customCancelGlobal = function (task) {\n      // if task is not marked as isRemoved, this call is directly\n      // from Zone.prototype.cancelTask, we should remove the task\n      // from tasksList of target first\n      if (!task.isRemoved) {\n        const symbolEventNames = zoneSymbolEventNames[task.eventName];\n        let symbolEventName;\n\n        if (symbolEventNames) {\n          symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n        }\n\n        const existingTasks = symbolEventName && task.target[symbolEventName];\n\n        if (existingTasks) {\n          for (let i = 0; i < existingTasks.length; i++) {\n            const existingTask = existingTasks[i];\n\n            if (existingTask === task) {\n              existingTasks.splice(i, 1); // set isRemoved to data for faster invokeTask check\n\n              task.isRemoved = true;\n\n              if (task.removeAbortListener) {\n                task.removeAbortListener();\n                task.removeAbortListener = null;\n              }\n\n              if (existingTasks.length === 0) {\n                // all tasks for the eventName + capture have gone,\n                // remove globalZoneAwareCallback and remove the task cache from target\n                task.allRemoved = true;\n                task.target[symbolEventName] = null;\n              }\n\n              break;\n            }\n          }\n        }\n      } // if all tasks for the eventName + capture have gone,\n      // we will really remove the global event callback,\n      // if not, return\n\n\n      if (!task.allRemoved) {\n        return;\n      }\n\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n    };\n\n    const customScheduleNonGlobal = function (task) {\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n\n    const customSchedulePrepend = function (task) {\n      return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n\n    const customCancelNonGlobal = function (task) {\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n    };\n\n    const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n    const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n\n    const compareTaskCallbackVsDelegate = function (task, delegate) {\n      const typeOfDelegate = typeof delegate;\n      return typeOfDelegate === 'function' && task.callback === delegate || typeOfDelegate === 'object' && task.originalDelegate === delegate;\n    };\n\n    const compare = patchOptions?.diff || compareTaskCallbackVsDelegate;\n    const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n\n    const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n\n    function copyEventListenerOptions(options) {\n      if (typeof options === 'object' && options !== null) {\n        // We need to destructure the target `options` object since it may\n        // be frozen or sealed (possibly provided implicitly by a third-party\n        // library), or its properties may be readonly.\n        const newOptions = { ...options\n        }; // The `signal` option was recently introduced, which caused regressions in\n        // third-party scenarios where `AbortController` was directly provided to\n        // `addEventListener` as options. For instance, in cases like\n        // `document.addEventListener('keydown', callback, abortControllerInstance)`,\n        // which is valid because `AbortController` includes a `signal` getter, spreading\n        // `{...options}` wouldn't copy the `signal`. Additionally, using `Object.create`\n        // isn't feasible since `AbortController` is a built-in object type, and attempting\n        // to create a new object directly with it as the prototype might result in\n        // unexpected behavior.\n\n        if (options.signal) {\n          newOptions.signal = options.signal;\n        }\n\n        return newOptions;\n      }\n\n      return options;\n    }\n\n    const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n      return function () {\n        const target = this || _global;\n        let eventName = arguments[0];\n\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n\n        let delegate = arguments[1];\n\n        if (!delegate) {\n          return nativeListener.apply(this, arguments);\n        }\n\n        if (isNode && eventName === 'uncaughtException') {\n          // don't patch uncaughtException of nodejs to prevent endless loop\n          return nativeListener.apply(this, arguments);\n        } // To improve `addEventListener` performance, we will create the callback\n        // for the task later when the task is invoked.\n\n\n        let isEventListenerObject = false;\n\n        if (typeof delegate !== 'function') {\n          // This checks whether the provided listener argument is an object with\n          // a `handleEvent` method (since we can call `addEventListener` with a\n          // function `event => ...` or with an object `{ handleEvent: event => ... }`).\n          if (!delegate.handleEvent) {\n            return nativeListener.apply(this, arguments);\n          }\n\n          isEventListenerObject = true;\n        }\n\n        if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n          return;\n        }\n\n        const passive = !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n        const options = copyEventListenerOptions(buildEventListenerOptions(arguments[2], passive));\n        const signal = options?.signal;\n\n        if (signal?.aborted) {\n          // the signal is an aborted one, just return without attaching the event listener.\n          return;\n        }\n\n        if (unpatchedEvents) {\n          // check unpatched list\n          for (let i = 0; i < unpatchedEvents.length; i++) {\n            if (eventName === unpatchedEvents[i]) {\n              if (passive) {\n                return nativeListener.call(target, eventName, delegate, options);\n              } else {\n                return nativeListener.apply(this, arguments);\n              }\n            }\n          }\n        }\n\n        const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n        const once = options && typeof options === 'object' ? options.once : false;\n        const zone = Zone.current;\n        let symbolEventNames = zoneSymbolEventNames[eventName];\n\n        if (!symbolEventNames) {\n          prepareEventNames(eventName, eventNameToString);\n          symbolEventNames = zoneSymbolEventNames[eventName];\n        }\n\n        const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n        let existingTasks = target[symbolEventName];\n        let isExisting = false;\n\n        if (existingTasks) {\n          // already have task registered\n          isExisting = true;\n\n          if (checkDuplicate) {\n            for (let i = 0; i < existingTasks.length; i++) {\n              if (compare(existingTasks[i], delegate)) {\n                // same callback, same capture, same event name, just return\n                return;\n              }\n            }\n          }\n        } else {\n          existingTasks = target[symbolEventName] = [];\n        }\n\n        let source;\n        const constructorName = target.constructor['name'];\n        const targetSource = globalSources[constructorName];\n\n        if (targetSource) {\n          source = targetSource[eventName];\n        }\n\n        if (!source) {\n          source = constructorName + addSource + (eventNameToString ? eventNameToString(eventName) : eventName);\n        } // In the code below, `options` should no longer be reassigned; instead, it\n        // should only be mutated. This is because we pass that object to the native\n        // `addEventListener`.\n        // It's generally recommended to use the same object reference for options.\n        // This ensures consistency and avoids potential issues.\n\n\n        taskData.options = options;\n\n        if (once) {\n          // When using `addEventListener` with the `once` option, we don't pass\n          // the `once` option directly to the native `addEventListener` method.\n          // Instead, we keep the `once` setting and handle it ourselves.\n          taskData.options.once = false;\n        }\n\n        taskData.target = target;\n        taskData.capture = capture;\n        taskData.eventName = eventName;\n        taskData.isExisting = isExisting;\n        const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined; // keep taskData into data to allow onScheduleEventTask to access the task information\n\n        if (data) {\n          data.taskData = taskData;\n        }\n\n        if (signal) {\n          // When using `addEventListener` with the `signal` option, we don't pass\n          // the `signal` option directly to the native `addEventListener` method.\n          // Instead, we keep the `signal` setting and handle it ourselves.\n          taskData.options.signal = undefined;\n        } // The `scheduleEventTask` function will ultimately call `customScheduleGlobal`,\n        // which in turn calls the native `addEventListener`. This is why `taskData.options`\n        // is updated before scheduling the task, as `customScheduleGlobal` uses\n        // `taskData.options` to pass it to the native `addEventListener`.\n\n\n        const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n\n        if (signal) {\n          // after task is scheduled, we need to store the signal back to task.options\n          taskData.options.signal = signal; // Wrapping `task` in a weak reference would not prevent memory leaks. Weak references are\n          // primarily used for preventing strong references cycles. `onAbort` is always reachable\n          // as it's an event listener, so its closure retains a strong reference to the `task`.\n\n          const onAbort = () => task.zone.cancelTask(task);\n\n          nativeListener.call(signal, 'abort', onAbort, {\n            once: true\n          }); // We need to remove the `abort` listener when the event listener is going to be removed,\n          // as it creates a closure that captures `task`. This closure retains a reference to the\n          // `task` object even after it goes out of scope, preventing `task` from being garbage\n          // collected.\n\n          task.removeAbortListener = () => signal.removeEventListener('abort', onAbort);\n        } // should clear taskData.target to avoid memory leak\n        // issue, https://github.com/angular/angular/issues/20442\n\n\n        taskData.target = null; // need to clear up taskData because it is a global object\n\n        if (data) {\n          data.taskData = null;\n        } // have to save those information to task in case\n        // application may call task.zone.cancelTask() directly\n\n\n        if (once) {\n          taskData.options.once = true;\n        }\n\n        if (typeof task.options !== 'boolean') {\n          // We should save the options on the task (if it's an object) because\n          // we'll be using `task.options` later when removing the event listener\n          // and passing it back to `removeEventListener`.\n          task.options = options;\n        }\n\n        task.target = target;\n        task.capture = capture;\n        task.eventName = eventName;\n\n        if (isEventListenerObject) {\n          // save original delegate for compare to check duplicate\n          task.originalDelegate = delegate;\n        }\n\n        if (!prepend) {\n          existingTasks.push(task);\n        } else {\n          existingTasks.unshift(task);\n        }\n\n        if (returnTarget) {\n          return target;\n        }\n      };\n    };\n\n    proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n\n    if (nativePrependEventListener) {\n      proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n    }\n\n    proto[REMOVE_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n\n      const options = arguments[2];\n      const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n      const delegate = arguments[1];\n\n      if (!delegate) {\n        return nativeRemoveEventListener.apply(this, arguments);\n      }\n\n      if (validateHandler && !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n        return;\n      }\n\n      const symbolEventNames = zoneSymbolEventNames[eventName];\n      let symbolEventName;\n\n      if (symbolEventNames) {\n        symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n      }\n\n      const existingTasks = symbolEventName && target[symbolEventName]; // `existingTasks` may not exist if the `addEventListener` was called before\n      // it was patched by zone.js. Please refer to the attached issue for\n      // clarification, particularly after the `if` condition, before calling\n      // the native `removeEventListener`.\n\n      if (existingTasks) {\n        for (let i = 0; i < existingTasks.length; i++) {\n          const existingTask = existingTasks[i];\n\n          if (compare(existingTask, delegate)) {\n            existingTasks.splice(i, 1); // set isRemoved to data for faster invokeTask check\n\n            existingTask.isRemoved = true;\n\n            if (existingTasks.length === 0) {\n              // all tasks for the eventName + capture have gone,\n              // remove globalZoneAwareCallback and remove the task cache from target\n              existingTask.allRemoved = true;\n              target[symbolEventName] = null; // in the target, we have an event listener which is added by on_property\n              // such as target.onclick = function() {}, so we need to clear this internal\n              // property too if all delegates with capture=false were removed\n              // https:// github.com/angular/angular/issues/31643\n              // https://github.com/angular/angular/issues/54581\n\n              if (!capture && typeof eventName === 'string') {\n                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                target[onPropertySymbol] = null;\n              }\n            } // In all other conditions, when `addEventListener` is called after being\n            // patched by zone.js, we would always find an event task on the `EventTarget`.\n            // This will trigger `cancelFn` on the `existingTask`, leading to `customCancelGlobal`,\n            // which ultimately removes an event listener and cleans up the abort listener\n            // (if an `AbortSignal` was provided when scheduling a task).\n\n\n            existingTask.zone.cancelTask(existingTask);\n\n            if (returnTarget) {\n              return target;\n            }\n\n            return;\n          }\n        }\n      } // https://github.com/angular/zone.js/issues/930\n      // We may encounter a situation where the `addEventListener` was\n      // called on the event target before zone.js is loaded, resulting\n      // in no task being stored on the event target due to its invocation\n      // of the native implementation. In this scenario, we simply need to\n      // invoke the native `removeEventListener`.\n\n\n      return nativeRemoveEventListener.apply(this, arguments);\n    };\n\n    proto[LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n\n      const listeners = [];\n      const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n\n      for (let i = 0; i < tasks.length; i++) {\n        const task = tasks[i];\n        let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n        listeners.push(delegate);\n      }\n\n      return listeners;\n    };\n\n    proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (!eventName) {\n        const keys = Object.keys(target);\n\n        for (let i = 0; i < keys.length; i++) {\n          const prop = keys[i];\n          const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n          let evtName = match && match[1]; // in nodejs EventEmitter, removeListener event is\n          // used for monitoring the removeListener call,\n          // so just keep removeListener eventListener until\n          // all other eventListeners are removed\n\n          if (evtName && evtName !== 'removeListener') {\n            this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n          }\n        } // remove removeListener listener finally\n\n\n        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n      } else {\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n\n        const symbolEventNames = zoneSymbolEventNames[eventName];\n\n        if (symbolEventNames) {\n          const symbolEventName = symbolEventNames[FALSE_STR];\n          const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n          const tasks = target[symbolEventName];\n          const captureTasks = target[symbolCaptureEventName];\n\n          if (tasks) {\n            const removeTasks = tasks.slice();\n\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n\n          if (captureTasks) {\n            const removeTasks = captureTasks.slice();\n\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n        }\n      }\n\n      if (returnTarget) {\n        return this;\n      }\n    }; // for native toString patch\n\n\n    attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n    attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n\n    if (nativeRemoveAllListeners) {\n      attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n    }\n\n    if (nativeListeners) {\n      attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n    }\n\n    return true;\n  }\n\n  let results = [];\n\n  for (let i = 0; i < apis.length; i++) {\n    results[i] = patchEventTargetMethods(apis[i], patchOptions);\n  }\n\n  return results;\n}\n\nfunction findEventTasks(target, eventName) {\n  if (!eventName) {\n    const foundTasks = [];\n\n    for (let prop in target) {\n      const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n      let evtName = match && match[1];\n\n      if (evtName && (!eventName || evtName === eventName)) {\n        const tasks = target[prop];\n\n        if (tasks) {\n          for (let i = 0; i < tasks.length; i++) {\n            foundTasks.push(tasks[i]);\n          }\n        }\n      }\n    }\n\n    return foundTasks;\n  }\n\n  let symbolEventName = zoneSymbolEventNames[eventName];\n\n  if (!symbolEventName) {\n    prepareEventNames(eventName);\n    symbolEventName = zoneSymbolEventNames[eventName];\n  }\n\n  const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n  const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n\n  if (!captureFalseTasks) {\n    return captureTrueTasks ? captureTrueTasks.slice() : [];\n  } else {\n    return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) : captureFalseTasks.slice();\n  }\n}\n\nfunction patchEventPrototype(global, api) {\n  const Event = global['Event'];\n\n  if (Event && Event.prototype) {\n    api.patchMethod(Event.prototype, 'stopImmediatePropagation', delegate => function (self, args) {\n      self[IMMEDIATE_PROPAGATION_SYMBOL] = true; // we need to call the native stopImmediatePropagation\n      // in case in some hybrid application, some part of\n      // application will be controlled by zone, some are not\n\n      delegate && delegate.apply(self, args);\n    });\n  }\n}\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n\n\nfunction patchQueueMicrotask(global, api) {\n  api.patchMethod(global, 'queueMicrotask', delegate => {\n    return function (self, args) {\n      Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n    };\n  });\n}\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n\n\nconst taskSymbol = zoneSymbol('zoneTask');\n\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n  let setNative = null;\n  let clearNative = null;\n  setName += nameSuffix;\n  cancelName += nameSuffix;\n  const tasksByHandleId = {};\n\n  function scheduleTask(task) {\n    const data = task.data;\n\n    data.args[0] = function () {\n      return task.invoke.apply(this, arguments);\n    };\n\n    const handleOrId = setNative.apply(window, data.args); // Whlist on Node.js when get can the ID by using `[Symbol.toPrimitive]()` we do\n    // to this so that we do not cause potentally leaks when using `setTimeout`\n    // since this can be periodic when using `.refresh`.\n\n    if (isNumber(handleOrId)) {\n      data.handleId = handleOrId;\n    } else {\n      data.handle = handleOrId; // On Node.js a timeout and interval can be restarted over and over again by using the `.refresh` method.\n\n      data.isRefreshable = isFunction(handleOrId.refresh);\n    }\n\n    return task;\n  }\n\n  function clearTask(task) {\n    const {\n      handle,\n      handleId\n    } = task.data;\n    return clearNative.call(window, handle ?? handleId);\n  }\n\n  setNative = patchMethod(window, setName, delegate => function (self, args) {\n    if (isFunction(args[0])) {\n      const options = {\n        isRefreshable: false,\n        isPeriodic: nameSuffix === 'Interval',\n        delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n        args: args\n      };\n      const callback = args[0];\n\n      args[0] = function timer() {\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          // issue-934, task will be cancelled\n          // even it is a periodic task such as\n          // setInterval\n          // https://github.com/angular/angular/issues/40387\n          // Cleanup tasksByHandleId should be handled before scheduleTask\n          // Since some zoneSpec may intercept and doesn't trigger\n          // scheduleFn(scheduleTask) provided here.\n          const {\n            handle,\n            handleId,\n            isPeriodic,\n            isRefreshable\n          } = options;\n\n          if (!isPeriodic && !isRefreshable) {\n            if (handleId) {\n              // in non-nodejs env, we remove timerId\n              // from local cache\n              delete tasksByHandleId[handleId];\n            } else if (handle) {\n              // Node returns complex objects as handleIds\n              // we remove task reference from timer object\n              handle[taskSymbol] = null;\n            }\n          }\n        }\n      };\n\n      const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n\n      if (!task) {\n        return task;\n      } // Node.js must additionally support the ref and unref functions.\n\n\n      const {\n        handleId,\n        handle,\n        isRefreshable,\n        isPeriodic\n      } = task.data;\n\n      if (handleId) {\n        // for non nodejs env, we save handleId: task\n        // mapping in local cache for clearTimeout\n        tasksByHandleId[handleId] = task;\n      } else if (handle) {\n        // for nodejs env, we save task\n        // reference in timerId Object for clearTimeout\n        handle[taskSymbol] = task;\n\n        if (isRefreshable && !isPeriodic) {\n          const originalRefresh = handle.refresh;\n\n          handle.refresh = function () {\n            const {\n              zone,\n              state\n            } = task;\n\n            if (state === 'notScheduled') {\n              task._state = 'scheduled';\n\n              zone._updateTaskCount(task, 1);\n            } else if (state === 'running') {\n              task._state = 'scheduling';\n            }\n\n            return originalRefresh.call(this);\n          };\n        }\n      }\n\n      return handle ?? handleId ?? task;\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(window, args);\n    }\n  });\n  clearNative = patchMethod(window, cancelName, delegate => function (self, args) {\n    const id = args[0];\n    let task;\n\n    if (isNumber(id)) {\n      // non nodejs env.\n      task = tasksByHandleId[id];\n      delete tasksByHandleId[id];\n    } else {\n      // nodejs env ?? other environments.\n      task = id?.[taskSymbol];\n\n      if (task) {\n        id[taskSymbol] = null;\n      } else {\n        task = id;\n      }\n    }\n\n    if (task?.type) {\n      if (task.cancelFn) {\n        // Do not cancel already canceled functions\n        task.zone.cancelTask(task);\n      }\n    } else {\n      // cause an error by calling it directly.\n      delegate.apply(window, args);\n    }\n  });\n}\n\nfunction patchCustomElements(_global, api) {\n  const {\n    isBrowser,\n    isMix\n  } = api.getGlobalObjects();\n\n  if (!isBrowser && !isMix || !_global['customElements'] || !('customElements' in _global)) {\n    return;\n  } // https://html.spec.whatwg.org/multipage/custom-elements.html#concept-custom-element-definition-lifecycle-callbacks\n\n\n  const callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback', 'formAssociatedCallback', 'formDisabledCallback', 'formResetCallback', 'formStateRestoreCallback'];\n  api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\nfunction eventTargetPatch(_global, api) {\n  if (Zone[api.symbol('patchEventTarget')]) {\n    // EventTarget is already patched.\n    return;\n  }\n\n  const {\n    eventNames,\n    zoneSymbolEventNames,\n    TRUE_STR,\n    FALSE_STR,\n    ZONE_SYMBOL_PREFIX\n  } = api.getGlobalObjects(); //  predefine all __zone_symbol__ + eventName + true/false string\n\n  for (let i = 0; i < eventNames.length; i++) {\n    const eventName = eventNames[i];\n    const falseEventName = eventName + FALSE_STR;\n    const trueEventName = eventName + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n  }\n\n  const EVENT_TARGET = _global['EventTarget'];\n\n  if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n    return;\n  }\n\n  api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n  return true;\n}\n\nfunction patchEvent(global, api) {\n  api.patchEventPrototype(global, api);\n}\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\n\n\nfunction filterProperties(target, onProperties, ignoreProperties) {\n  if (!ignoreProperties || ignoreProperties.length === 0) {\n    return onProperties;\n  }\n\n  const tip = ignoreProperties.filter(ip => ip.target === target);\n\n  if (tip.length === 0) {\n    return onProperties;\n  }\n\n  const targetIgnoreProperties = tip[0].ignoreProperties;\n  return onProperties.filter(op => targetIgnoreProperties.indexOf(op) === -1);\n}\n\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n  // check whether target is available, sometimes target will be undefined\n  // because different browser or some 3rd party plugin.\n  if (!target) {\n    return;\n  }\n\n  const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n  patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\n\n\nfunction getOnEventNames(target) {\n  return Object.getOwnPropertyNames(target).filter(name => name.startsWith('on') && name.length > 2).map(name => name.substring(2));\n}\n\nfunction propertyDescriptorPatch(api, _global) {\n  if (isNode && !isMix) {\n    return;\n  }\n\n  if (Zone[api.symbol('patchEvents')]) {\n    // events are already been patched by legacy patch.\n    return;\n  }\n\n  const ignoreProperties = _global['__Zone_ignore_on_properties']; // for browsers that we can patch the descriptor:  Chrome & Firefox\n\n  let patchTargets = [];\n\n  if (isBrowser) {\n    const internalWindow = window;\n    patchTargets = patchTargets.concat(['Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement', 'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker']);\n    const ignoreErrorProperties = []; // In older browsers like IE or Edge, event handler properties (e.g., `onclick`)\n    // may not be defined directly on the `window` object but on its prototype (`WindowPrototype`).\n    // To ensure complete coverage, we use the prototype when checking\n    // for and patching these properties.\n\n    patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n  }\n\n  patchTargets = patchTargets.concat(['XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest', 'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket']);\n\n  for (let i = 0; i < patchTargets.length; i++) {\n    const target = _global[patchTargets[i]];\n    target?.prototype && patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n  }\n}\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n\n\nfunction patchBrowser(Zone) {\n  Zone.__load_patch('legacy', global => {\n    const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n\n    if (legacyPatch) {\n      legacyPatch();\n    }\n  });\n\n  Zone.__load_patch('timers', global => {\n    const set = 'set';\n    const clear = 'clear';\n    patchTimer(global, set, clear, 'Timeout');\n    patchTimer(global, set, clear, 'Interval');\n    patchTimer(global, set, clear, 'Immediate');\n  });\n\n  Zone.__load_patch('requestAnimationFrame', global => {\n    patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n    patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n    patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n  });\n\n  Zone.__load_patch('blocking', (global, Zone) => {\n    const blockingMethods = ['alert', 'prompt', 'confirm'];\n\n    for (let i = 0; i < blockingMethods.length; i++) {\n      const name = blockingMethods[i];\n      patchMethod(global, name, (delegate, symbol, name) => {\n        return function (s, args) {\n          return Zone.current.run(delegate, global, args, name);\n        };\n      });\n    }\n  });\n\n  Zone.__load_patch('EventTarget', (global, Zone, api) => {\n    patchEvent(global, api);\n    eventTargetPatch(global, api); // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n\n    const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n\n    if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n      api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n    }\n  });\n\n  Zone.__load_patch('MutationObserver', (global, Zone, api) => {\n    patchClass('MutationObserver');\n    patchClass('WebKitMutationObserver');\n  });\n\n  Zone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n    patchClass('IntersectionObserver');\n  });\n\n  Zone.__load_patch('FileReader', (global, Zone, api) => {\n    patchClass('FileReader');\n  });\n\n  Zone.__load_patch('on_property', (global, Zone, api) => {\n    propertyDescriptorPatch(api, global);\n  });\n\n  Zone.__load_patch('customElements', (global, Zone, api) => {\n    patchCustomElements(global, api);\n  });\n\n  Zone.__load_patch('XHR', (global, Zone) => {\n    // Treat XMLHttpRequest as a macrotask.\n    patchXHR(global);\n    const XHR_TASK = zoneSymbol('xhrTask');\n    const XHR_SYNC = zoneSymbol('xhrSync');\n    const XHR_LISTENER = zoneSymbol('xhrListener');\n    const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n    const XHR_URL = zoneSymbol('xhrURL');\n    const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n\n    function patchXHR(window) {\n      const XMLHttpRequest = window['XMLHttpRequest'];\n\n      if (!XMLHttpRequest) {\n        // XMLHttpRequest is not available in service worker\n        return;\n      }\n\n      const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n\n      function findPendingTask(target) {\n        return target[XHR_TASK];\n      }\n\n      let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n      let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n\n      if (!oriAddListener) {\n        const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n\n        if (XMLHttpRequestEventTarget) {\n          const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n          oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n          oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        }\n      }\n\n      const READY_STATE_CHANGE = 'readystatechange';\n      const SCHEDULED = 'scheduled';\n\n      function scheduleTask(task) {\n        const data = task.data;\n        const target = data.target;\n        target[XHR_SCHEDULED] = false;\n        target[XHR_ERROR_BEFORE_SCHEDULED] = false; // remove existing event listener\n\n        const listener = target[XHR_LISTENER];\n\n        if (!oriAddListener) {\n          oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n          oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        }\n\n        if (listener) {\n          oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n        }\n\n        const newListener = target[XHR_LISTENER] = () => {\n          if (target.readyState === target.DONE) {\n            // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n            // readyState=4 multiple times, so we need to check task state here\n            if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n              // check whether the xhr has registered onload listener\n              // if that is the case, the task should invoke after all\n              // onload listeners finish.\n              // Also if the request failed without response (status = 0), the load event handler\n              // will not be triggered, in that case, we should also invoke the placeholder callback\n              // to close the XMLHttpRequest::send macroTask.\n              // https://github.com/angular/angular/issues/38795\n              const loadTasks = target[Zone.__symbol__('loadfalse')];\n\n              if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                const oriInvoke = task.invoke;\n\n                task.invoke = function () {\n                  // need to load the tasks again, because in other\n                  // load listener, they may remove themselves\n                  const loadTasks = target[Zone.__symbol__('loadfalse')];\n\n                  for (let i = 0; i < loadTasks.length; i++) {\n                    if (loadTasks[i] === task) {\n                      loadTasks.splice(i, 1);\n                    }\n                  }\n\n                  if (!data.aborted && task.state === SCHEDULED) {\n                    oriInvoke.call(task);\n                  }\n                };\n\n                loadTasks.push(task);\n              } else {\n                task.invoke();\n              }\n            } else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n              // error occurs when xhr.send()\n              target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n            }\n          }\n        };\n\n        oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n        const storedTask = target[XHR_TASK];\n\n        if (!storedTask) {\n          target[XHR_TASK] = task;\n        }\n\n        sendNative.apply(target, data.args);\n        target[XHR_SCHEDULED] = true;\n        return task;\n      }\n\n      function placeholderCallback() {}\n\n      function clearTask(task) {\n        const data = task.data; // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n        // to prevent it from firing. So instead, we store info for the event listener.\n\n        data.aborted = true;\n        return abortNative.apply(data.target, data.args);\n      }\n\n      const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n        self[XHR_SYNC] = args[2] == false;\n        self[XHR_URL] = args[1];\n        return openNative.apply(self, args);\n      });\n      const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n      const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n      const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n      const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n        if (Zone.current[fetchTaskScheduling] === true) {\n          // a fetch is scheduling, so we are using xhr to polyfill fetch\n          // and because we already schedule macroTask for fetch, we should\n          // not schedule a macroTask for xhr again\n          return sendNative.apply(self, args);\n        }\n\n        if (self[XHR_SYNC]) {\n          // if the XHR is sync there is no task to schedule, just execute the code.\n          return sendNative.apply(self, args);\n        } else {\n          const options = {\n            target: self,\n            url: self[XHR_URL],\n            isPeriodic: false,\n            args: args,\n            aborted: false\n          };\n          const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n\n          if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted && task.state === SCHEDULED) {\n            // xhr request throw error when send\n            // we should invoke task instead of leaving a scheduled\n            // pending macroTask\n            task.invoke();\n          }\n        }\n      });\n      const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n        const task = findPendingTask(self);\n\n        if (task && typeof task.type == 'string') {\n          // If the XHR has already completed, do nothing.\n          // If the XHR has already been aborted, do nothing.\n          // Fix #569, call abort multiple times before done will cause\n          // macroTask task count be negative number\n          if (task.cancelFn == null || task.data && task.data.aborted) {\n            return;\n          }\n\n          task.zone.cancelTask(task);\n        } else if (Zone.current[fetchTaskAborting] === true) {\n          // the abort is called from fetch polyfill, we need to call native abort of XHR.\n          return abortNative.apply(self, args);\n        } // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n        // task\n        // to cancel. Do nothing.\n\n      });\n    }\n  });\n\n  Zone.__load_patch('geolocation', global => {\n    /// GEO_LOCATION\n    if (global['navigator'] && global['navigator'].geolocation) {\n      patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n    }\n  });\n\n  Zone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n    // handle unhandled promise rejection\n    function findPromiseRejectionHandler(evtName) {\n      return function (e) {\n        const eventTasks = findEventTasks(global, evtName);\n        eventTasks.forEach(eventTask => {\n          // windows has added unhandledrejection event listener\n          // trigger the event listener\n          const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n\n          if (PromiseRejectionEvent) {\n            const evt = new PromiseRejectionEvent(evtName, {\n              promise: e.promise,\n              reason: e.rejection\n            });\n            eventTask.invoke(evt);\n          }\n        });\n      };\n    }\n\n    if (global['PromiseRejectionEvent']) {\n      Zone[zoneSymbol('unhandledPromiseRejectionHandler')] = findPromiseRejectionHandler('unhandledrejection');\n      Zone[zoneSymbol('rejectionHandledHandler')] = findPromiseRejectionHandler('rejectionhandled');\n    }\n  });\n\n  Zone.__load_patch('queueMicrotask', (global, Zone, api) => {\n    patchQueueMicrotask(global, api);\n  });\n}\n\nfunction patchPromise(Zone) {\n  Zone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n    const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    const ObjectDefineProperty = Object.defineProperty;\n\n    function readableObjectToString(obj) {\n      if (obj && obj.toString === Object.prototype.toString) {\n        const className = obj.constructor && obj.constructor.name;\n        return (className ? className : '') + ': ' + JSON.stringify(obj);\n      }\n\n      return obj ? obj.toString() : Object.prototype.toString.call(obj);\n    }\n\n    const __symbol__ = api.symbol;\n    const _uncaughtPromiseErrors = [];\n    const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] !== false;\n\n    const symbolPromise = __symbol__('Promise');\n\n    const symbolThen = __symbol__('then');\n\n    const creationTrace = '__creationTrace__';\n\n    api.onUnhandledError = e => {\n      if (api.showUncaughtError()) {\n        const rejection = e && e.rejection;\n\n        if (rejection) {\n          console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n        } else {\n          console.error(e);\n        }\n      }\n    };\n\n    api.microtaskDrainDone = () => {\n      while (_uncaughtPromiseErrors.length) {\n        const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n\n        try {\n          uncaughtPromiseError.zone.runGuarded(() => {\n            if (uncaughtPromiseError.throwOriginal) {\n              throw uncaughtPromiseError.rejection;\n            }\n\n            throw uncaughtPromiseError;\n          });\n        } catch (error) {\n          handleUnhandledRejection(error);\n        }\n      }\n    };\n\n    const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n\n    function handleUnhandledRejection(e) {\n      api.onUnhandledError(e);\n\n      try {\n        const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n\n        if (typeof handler === 'function') {\n          handler.call(this, e);\n        }\n      } catch (err) {}\n    }\n\n    function isThenable(value) {\n      return value && typeof value.then === 'function';\n    }\n\n    function forwardResolution(value) {\n      return value;\n    }\n\n    function forwardRejection(rejection) {\n      return ZoneAwarePromise.reject(rejection);\n    }\n\n    const symbolState = __symbol__('state');\n\n    const symbolValue = __symbol__('value');\n\n    const symbolFinally = __symbol__('finally');\n\n    const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n\n    const symbolParentPromiseState = __symbol__('parentPromiseState');\n\n    const source = 'Promise.then';\n    const UNRESOLVED = null;\n    const RESOLVED = true;\n    const REJECTED = false;\n    const REJECTED_NO_CATCH = 0;\n\n    function makeResolver(promise, state) {\n      return v => {\n        try {\n          resolvePromise(promise, state, v);\n        } catch (err) {\n          resolvePromise(promise, false, err);\n        } // Do not return value or you will break the Promise spec.\n\n      };\n    }\n\n    const once = function () {\n      let wasCalled = false;\n      return function wrapper(wrappedFunction) {\n        return function () {\n          if (wasCalled) {\n            return;\n          }\n\n          wasCalled = true;\n          wrappedFunction.apply(null, arguments);\n        };\n      };\n    };\n\n    const TYPE_ERROR = 'Promise resolved with itself';\n\n    const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace'); // Promise Resolution\n\n\n    function resolvePromise(promise, state, value) {\n      const onceWrapper = once();\n\n      if (promise === value) {\n        throw new TypeError(TYPE_ERROR);\n      }\n\n      if (promise[symbolState] === UNRESOLVED) {\n        // should only get value.then once based on promise spec.\n        let then = null;\n\n        try {\n          if (typeof value === 'object' || typeof value === 'function') {\n            then = value && value.then;\n          }\n        } catch (err) {\n          onceWrapper(() => {\n            resolvePromise(promise, false, err);\n          })();\n          return promise;\n        } // if (value instanceof ZoneAwarePromise) {\n\n\n        if (state !== REJECTED && value instanceof ZoneAwarePromise && value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) && value[symbolState] !== UNRESOLVED) {\n          clearRejectedNoCatch(value);\n          resolvePromise(promise, value[symbolState], value[symbolValue]);\n        } else if (state !== REJECTED && typeof then === 'function') {\n          try {\n            then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n          } catch (err) {\n            onceWrapper(() => {\n              resolvePromise(promise, false, err);\n            })();\n          }\n        } else {\n          promise[symbolState] = state;\n          const queue = promise[symbolValue];\n          promise[symbolValue] = value;\n\n          if (promise[symbolFinally] === symbolFinally) {\n            // the promise is generated by Promise.prototype.finally\n            if (state === RESOLVED) {\n              // the state is resolved, should ignore the value\n              // and use parent promise value\n              promise[symbolState] = promise[symbolParentPromiseState];\n              promise[symbolValue] = promise[symbolParentPromiseValue];\n            }\n          } // record task information in value when error occurs, so we can\n          // do some additional work such as render longStackTrace\n\n\n          if (state === REJECTED && value instanceof Error) {\n            // check if longStackTraceZone is here\n            const trace = Zone.currentTask && Zone.currentTask.data && Zone.currentTask.data[creationTrace];\n\n            if (trace) {\n              // only keep the long stack trace into error when in longStackTraceZone\n              ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                configurable: true,\n                enumerable: false,\n                writable: true,\n                value: trace\n              });\n            }\n          }\n\n          for (let i = 0; i < queue.length;) {\n            scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n          }\n\n          if (queue.length == 0 && state == REJECTED) {\n            promise[symbolState] = REJECTED_NO_CATCH;\n            let uncaughtPromiseError = value;\n\n            try {\n              // Here we throws a new Error to print more readable error log\n              // and if the value is not an error, zone.js builds an `Error`\n              // Object here to attach the stack information.\n              throw new Error('Uncaught (in promise): ' + readableObjectToString(value) + (value && value.stack ? '\\n' + value.stack : ''));\n            } catch (err) {\n              uncaughtPromiseError = err;\n            }\n\n            if (isDisableWrappingUncaughtPromiseRejection) {\n              // If disable wrapping uncaught promise reject\n              // use the value instead of wrapping it.\n              uncaughtPromiseError.throwOriginal = true;\n            }\n\n            uncaughtPromiseError.rejection = value;\n            uncaughtPromiseError.promise = promise;\n            uncaughtPromiseError.zone = Zone.current;\n            uncaughtPromiseError.task = Zone.currentTask;\n\n            _uncaughtPromiseErrors.push(uncaughtPromiseError);\n\n            api.scheduleMicroTask(); // to make sure that it is running\n          }\n        }\n      } // Resolving an already resolved promise is a noop.\n\n\n      return promise;\n    }\n\n    const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n\n    function clearRejectedNoCatch(promise) {\n      if (promise[symbolState] === REJECTED_NO_CATCH) {\n        // if the promise is rejected no catch status\n        // and queue.length > 0, means there is a error handler\n        // here to handle the rejected promise, we should trigger\n        // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n        // eventHandler\n        try {\n          const handler = Zone[REJECTION_HANDLED_HANDLER];\n\n          if (handler && typeof handler === 'function') {\n            handler.call(this, {\n              rejection: promise[symbolValue],\n              promise: promise\n            });\n          }\n        } catch (err) {}\n\n        promise[symbolState] = REJECTED;\n\n        for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n          if (promise === _uncaughtPromiseErrors[i].promise) {\n            _uncaughtPromiseErrors.splice(i, 1);\n          }\n        }\n      }\n    }\n\n    function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n      clearRejectedNoCatch(promise);\n      const promiseState = promise[symbolState];\n      const delegate = promiseState ? typeof onFulfilled === 'function' ? onFulfilled : forwardResolution : typeof onRejected === 'function' ? onRejected : forwardRejection;\n      zone.scheduleMicroTask(source, () => {\n        try {\n          const parentPromiseValue = promise[symbolValue];\n          const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n\n          if (isFinallyPromise) {\n            // if the promise is generated from finally call, keep parent promise's state and value\n            chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n            chainPromise[symbolParentPromiseState] = promiseState;\n          } // should not pass value to finally callback\n\n\n          const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ? [] : [parentPromiseValue]);\n          resolvePromise(chainPromise, true, value);\n        } catch (error) {\n          // if error occurs, should always return this error\n          resolvePromise(chainPromise, false, error);\n        }\n      }, chainPromise);\n    }\n\n    const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n\n    const noop = function () {};\n\n    const AggregateError = global.AggregateError;\n\n    class ZoneAwarePromise {\n      static toString() {\n        return ZONE_AWARE_PROMISE_TO_STRING;\n      }\n\n      static resolve(value) {\n        if (value instanceof ZoneAwarePromise) {\n          return value;\n        }\n\n        return resolvePromise(new this(null), RESOLVED, value);\n      }\n\n      static reject(error) {\n        return resolvePromise(new this(null), REJECTED, error);\n      }\n\n      static withResolvers() {\n        const result = {};\n        result.promise = new ZoneAwarePromise((res, rej) => {\n          result.resolve = res;\n          result.reject = rej;\n        });\n        return result;\n      }\n\n      static any(values) {\n        if (!values || typeof values[Symbol.iterator] !== 'function') {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n\n        const promises = [];\n        let count = 0;\n\n        try {\n          for (let v of values) {\n            count++;\n            promises.push(ZoneAwarePromise.resolve(v));\n          }\n        } catch (err) {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n\n        if (count === 0) {\n          return Promise.reject(new AggregateError([], 'All promises were rejected'));\n        }\n\n        let finished = false;\n        const errors = [];\n        return new ZoneAwarePromise((resolve, reject) => {\n          for (let i = 0; i < promises.length; i++) {\n            promises[i].then(v => {\n              if (finished) {\n                return;\n              }\n\n              finished = true;\n              resolve(v);\n            }, err => {\n              errors.push(err);\n              count--;\n\n              if (count === 0) {\n                finished = true;\n                reject(new AggregateError(errors, 'All promises were rejected'));\n              }\n            });\n          }\n        });\n      }\n\n      static race(values) {\n        let resolve;\n        let reject;\n        let promise = new this((res, rej) => {\n          resolve = res;\n          reject = rej;\n        });\n\n        function onResolve(value) {\n          resolve(value);\n        }\n\n        function onReject(error) {\n          reject(error);\n        }\n\n        for (let value of values) {\n          if (!isThenable(value)) {\n            value = this.resolve(value);\n          }\n\n          value.then(onResolve, onReject);\n        }\n\n        return promise;\n      }\n\n      static all(values) {\n        return ZoneAwarePromise.allWithCallback(values);\n      }\n\n      static allSettled(values) {\n        const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n        return P.allWithCallback(values, {\n          thenCallback: value => ({\n            status: 'fulfilled',\n            value\n          }),\n          errorCallback: err => ({\n            status: 'rejected',\n            reason: err\n          })\n        });\n      }\n\n      static allWithCallback(values, callback) {\n        let resolve;\n        let reject;\n        let promise = new this((res, rej) => {\n          resolve = res;\n          reject = rej;\n        }); // Start at 2 to prevent prematurely resolving if .then is called immediately.\n\n        let unresolvedCount = 2;\n        let valueIndex = 0;\n        const resolvedValues = [];\n\n        for (let value of values) {\n          if (!isThenable(value)) {\n            value = this.resolve(value);\n          }\n\n          const curValueIndex = valueIndex;\n\n          try {\n            value.then(value => {\n              resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n              unresolvedCount--;\n\n              if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n              }\n            }, err => {\n              if (!callback) {\n                reject(err);\n              } else {\n                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                unresolvedCount--;\n\n                if (unresolvedCount === 0) {\n                  resolve(resolvedValues);\n                }\n              }\n            });\n          } catch (thenErr) {\n            reject(thenErr);\n          }\n\n          unresolvedCount++;\n          valueIndex++;\n        } // Make the unresolvedCount zero-based again.\n\n\n        unresolvedCount -= 2;\n\n        if (unresolvedCount === 0) {\n          resolve(resolvedValues);\n        }\n\n        return promise;\n      }\n\n      constructor(executor) {\n        const promise = this;\n\n        if (!(promise instanceof ZoneAwarePromise)) {\n          throw new Error('Must be an instanceof Promise.');\n        }\n\n        promise[symbolState] = UNRESOLVED;\n        promise[symbolValue] = []; // queue;\n\n        try {\n          const onceWrapper = once();\n          executor && executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n        } catch (error) {\n          resolvePromise(promise, false, error);\n        }\n      }\n\n      get [Symbol.toStringTag]() {\n        return 'Promise';\n      }\n\n      get [Symbol.species]() {\n        return ZoneAwarePromise;\n      }\n\n      then(onFulfilled, onRejected) {\n        // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n        // may be an object without a prototype (created through `Object.create(null)`); thus\n        // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n        // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n        // object and copies promise properties into that object (within the `getOrCreateLoad`\n        // function). The zone.js then checks if the resolved value has the `then` method and\n        // invokes it with the `value` context. Otherwise, this will throw an error: `TypeError:\n        // Cannot read properties of undefined (reading 'Symbol(Symbol.species)')`.\n        let C = this.constructor?.[Symbol.species];\n\n        if (!C || typeof C !== 'function') {\n          C = this.constructor || ZoneAwarePromise;\n        }\n\n        const chainPromise = new C(noop);\n        const zone = Zone.current;\n\n        if (this[symbolState] == UNRESOLVED) {\n          this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n        } else {\n          scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n        }\n\n        return chainPromise;\n      }\n\n      catch(onRejected) {\n        return this.then(null, onRejected);\n      }\n\n      finally(onFinally) {\n        // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n        let C = this.constructor?.[Symbol.species];\n\n        if (!C || typeof C !== 'function') {\n          C = ZoneAwarePromise;\n        }\n\n        const chainPromise = new C(noop);\n        chainPromise[symbolFinally] = symbolFinally;\n        const zone = Zone.current;\n\n        if (this[symbolState] == UNRESOLVED) {\n          this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n        } else {\n          scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n        }\n\n        return chainPromise;\n      }\n\n    } // Protect against aggressive optimizers dropping seemingly unused properties.\n    // E.g. Closure Compiler in advanced mode.\n\n\n    ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n    ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n    ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n    ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n    const NativePromise = global[symbolPromise] = global['Promise'];\n    global['Promise'] = ZoneAwarePromise;\n\n    const symbolThenPatched = __symbol__('thenPatched');\n\n    function patchThen(Ctor) {\n      const proto = Ctor.prototype;\n      const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n\n      if (prop && (prop.writable === false || !prop.configurable)) {\n        // check Ctor.prototype.then propertyDescriptor is writable or not\n        // in meteor env, writable is false, we should ignore such case\n        return;\n      }\n\n      const originalThen = proto.then; // Keep a reference to the original method.\n\n      proto[symbolThen] = originalThen;\n\n      Ctor.prototype.then = function (onResolve, onReject) {\n        const wrapped = new ZoneAwarePromise((resolve, reject) => {\n          originalThen.call(this, resolve, reject);\n        });\n        return wrapped.then(onResolve, onReject);\n      };\n\n      Ctor[symbolThenPatched] = true;\n    }\n\n    api.patchThen = patchThen;\n\n    function zoneify(fn) {\n      return function (self, args) {\n        let resultPromise = fn.apply(self, args);\n\n        if (resultPromise instanceof ZoneAwarePromise) {\n          return resultPromise;\n        }\n\n        let ctor = resultPromise.constructor;\n\n        if (!ctor[symbolThenPatched]) {\n          patchThen(ctor);\n        }\n\n        return resultPromise;\n      };\n    }\n\n    if (NativePromise) {\n      patchThen(NativePromise);\n      patchMethod(global, 'fetch', delegate => zoneify(delegate));\n    } // This is not part of public API, but it is useful for tests, so we expose it.\n\n\n    Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n    return ZoneAwarePromise;\n  });\n}\n\nfunction patchToString(Zone) {\n  // override Function.prototype.toString to make zone.js patched function\n  // look like native function\n  Zone.__load_patch('toString', global => {\n    // patch Func.prototype.toString to let them look like native\n    const originalFunctionToString = Function.prototype.toString;\n    const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n    const PROMISE_SYMBOL = zoneSymbol('Promise');\n    const ERROR_SYMBOL = zoneSymbol('Error');\n\n    const newFunctionToString = function toString() {\n      if (typeof this === 'function') {\n        const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n\n        if (originalDelegate) {\n          if (typeof originalDelegate === 'function') {\n            return originalFunctionToString.call(originalDelegate);\n          } else {\n            return Object.prototype.toString.call(originalDelegate);\n          }\n        }\n\n        if (this === Promise) {\n          const nativePromise = global[PROMISE_SYMBOL];\n\n          if (nativePromise) {\n            return originalFunctionToString.call(nativePromise);\n          }\n        }\n\n        if (this === Error) {\n          const nativeError = global[ERROR_SYMBOL];\n\n          if (nativeError) {\n            return originalFunctionToString.call(nativeError);\n          }\n        }\n      }\n\n      return originalFunctionToString.call(this);\n    };\n\n    newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n    Function.prototype.toString = newFunctionToString; // patch Object.prototype.toString to let them look like native\n\n    const originalObjectToString = Object.prototype.toString;\n    const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n\n    Object.prototype.toString = function () {\n      if (typeof Promise === 'function' && this instanceof Promise) {\n        return PROMISE_OBJECT_TO_STRING;\n      }\n\n      return originalObjectToString.call(this);\n    };\n  });\n}\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n  const symbol = Zone.__symbol__(method);\n\n  if (target[symbol]) {\n    return;\n  }\n\n  const nativeDelegate = target[symbol] = target[method];\n\n  target[method] = function (name, opts, options) {\n    if (opts && opts.prototype) {\n      callbacks.forEach(function (callback) {\n        const source = `${targetName}.${method}::` + callback;\n        const prototype = opts.prototype; // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n        // `customElements.define`. We explicitly wrap the patching code into try-catch since\n        // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n        // make those properties non-writable. This means that patching callback will throw an error\n        // `cannot assign to read-only property`. See this code as an example:\n        // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n        // We don't want to stop the application rendering if we couldn't patch some\n        // callback, e.g. `attributeChangedCallback`.\n\n        try {\n          if (prototype.hasOwnProperty(callback)) {\n            const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n\n            if (descriptor && descriptor.value) {\n              descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n\n              api._redefineProperty(opts.prototype, callback, descriptor);\n            } else if (prototype[callback]) {\n              prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n            }\n          } else if (prototype[callback]) {\n            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n          }\n        } catch {// Note: we leave the catch block empty since there's no way to handle the error related\n          // to non-writable property.\n        }\n      });\n    }\n\n    return nativeDelegate.call(target, name, opts, options);\n  };\n\n  api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\nfunction patchUtil(Zone) {\n  Zone.__load_patch('util', (global, Zone, api) => {\n    // Collect native event names by looking at properties\n    // on the global namespace, e.g. 'onclick'.\n    const eventNames = getOnEventNames(global);\n    api.patchOnProperties = patchOnProperties;\n    api.patchMethod = patchMethod;\n    api.bindArguments = bindArguments;\n    api.patchMacroTask = patchMacroTask; // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS`\n    // to define which events will not be patched by `Zone.js`. In newer version (>=0.9.0), we\n    // change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep the name consistent with\n    // angular repo. The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be\n    // supported for backwards compatibility.\n\n    const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n\n    const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n\n    if (global[SYMBOL_UNPATCHED_EVENTS]) {\n      global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n    }\n\n    if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n      Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] = global[SYMBOL_BLACK_LISTED_EVENTS];\n    }\n\n    api.patchEventPrototype = patchEventPrototype;\n    api.patchEventTarget = patchEventTarget;\n    api.isIEOrEdge = isIEOrEdge;\n    api.ObjectDefineProperty = ObjectDefineProperty;\n    api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n    api.ObjectCreate = ObjectCreate;\n    api.ArraySlice = ArraySlice;\n    api.patchClass = patchClass;\n    api.wrapWithCurrentZone = wrapWithCurrentZone;\n    api.filterProperties = filterProperties;\n    api.attachOriginToPatched = attachOriginToPatched;\n    api._redefineProperty = Object.defineProperty;\n    api.patchCallbacks = patchCallbacks;\n\n    api.getGlobalObjects = () => ({\n      globalSources,\n      zoneSymbolEventNames,\n      eventNames,\n      isBrowser,\n      isMix,\n      isNode,\n      TRUE_STR,\n      FALSE_STR,\n      ZONE_SYMBOL_PREFIX,\n      ADD_EVENT_LISTENER_STR,\n      REMOVE_EVENT_LISTENER_STR\n    });\n  });\n}\n\nfunction patchCommon(Zone) {\n  patchPromise(Zone);\n  patchToString(Zone);\n  patchUtil(Zone);\n}\n\nconst Zone$1 = loadZone();\npatchCommon(Zone$1);\npatchBrowser(Zone$1);", "map": {"version": 3, "names": ["global", "globalThis", "__symbol__", "name", "symbolPrefix", "initZone", "performance", "mark", "performanceMeasure", "label", "ZoneImpl", "assertZonePatched", "patches", "Error", "root", "zone", "current", "parent", "_currentZoneFrame", "currentTask", "_currentTask", "__load_patch", "fn", "ignoreDuplicate", "hasOwnProperty", "checkDuplicate", "perfName", "_api", "_parent", "_name", "constructor", "zoneSpec", "_properties", "properties", "_zoneDelegate", "_ZoneDelegate", "get", "key", "getZoneWith", "fork", "wrap", "callback", "source", "_callback", "intercept", "runGuarded", "arguments", "run", "applyThis", "applyArgs", "invoke", "error", "handleError", "runTask", "task", "NO_ZONE", "zoneTask", "type", "data", "isPeriodic", "isRefreshable", "state", "notScheduled", "eventTask", "macroTask", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "running", "_transitionTo", "scheduled", "previousTask", "cancelFn", "undefined", "invokeTask", "unknown", "scheduling", "zoneDelegates", "_zoneDelegates", "_updateTaskCount", "scheduleTask", "newZone", "_zone", "err", "scheduleMicroTask", "customSchedule", "ZoneTask", "microTask", "scheduleMacroTask", "customCancel", "scheduleEventTask", "cancelTask", "canceling", "runCount", "count", "i", "length", "DELEGATE_ZS", "onHasTask", "delegate", "_", "target", "hasTaskState", "hasTask", "onScheduleTask", "onInvokeTask", "onCancelTask", "parentDelegate", "_parentDelegate", "_forkZS", "onFork", "_forkDlgt", "_forkCurrZone", "_interceptZS", "onIntercept", "_interceptDlgt", "_interceptCurrZone", "_invokeZS", "onInvoke", "_invokeDlgt", "_invokeCurrZone", "_handleErrorZS", "onHandleError", "_handleErrorDlgt", "_handleErrorCurrZone", "_scheduleTaskZS", "_scheduleTaskDlgt", "_scheduleTaskCurrZone", "_invokeTaskZS", "_invokeTaskDlgt", "_invokeTaskCurrZone", "_cancelTaskZS", "_cancelTaskDlgt", "_cancelTaskCurrZone", "_hasTaskZS", "_hasTaskDlgt", "_hasTaskDlgtOwner", "_hasTaskCurrZone", "zoneSpecHasTask", "parentHasTask", "targetZone", "apply", "returnTask", "push", "scheduleFn", "value", "isEmpty", "counts", "_taskCounts", "prev", "next", "change", "options", "self", "useG", "call", "args", "_numberOfNestedTaskFrames", "drainMicroTaskQueue", "_state", "cancelScheduleRequest", "toState", "fromState1", "fromState2", "toString", "handleId", "Object", "prototype", "toJSON", "symbolSetTimeout", "symbolPromise", "symbolThen", "_microTaskQueue", "_isDrainingMicrotaskQueue", "nativeMicroTaskQueuePromise", "nativeScheduleMicroTask", "func", "resolve", "nativeThen", "queue", "onUnhandledError", "microtaskDrainDone", "symbol", "currentZoneFrame", "noop", "showUncaughtError", "patchEventTarget", "patchOnProperties", "patchMethod", "bindArguments", "patchThen", "patchMacroTask", "patchEventPrototype", "isIEOrEdge", "getGlobalObjects", "ObjectDefineProperty", "ObjectGetOwnPropertyDescriptor", "ObjectCreate", "ArraySlice", "patchClass", "wrapWithCurrentZone", "filterProperties", "attachOriginToPatched", "_redefineProperty", "patchCallbacks", "loadZone", "getOwnPropertyDescriptor", "defineProperty", "ObjectGetPrototypeOf", "getPrototypeOf", "create", "Array", "slice", "ADD_EVENT_LISTENER_STR", "REMOVE_EVENT_LISTENER_STR", "ZONE_SYMBOL_ADD_EVENT_LISTENER", "ZONE_SYMBOL_REMOVE_EVENT_LISTENER", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "Zone", "scheduleMacroTaskWithCurrentZone", "zoneSymbol", "isWindowExists", "window", "internalWindow", "_global", "REMOVE_ATTRIBUTE", "patchPrototype", "fnNames", "prototypeDesc", "isPropertyWritable", "patched", "propertyDesc", "writable", "set", "isWebWorker", "WorkerGlobalScope", "isNode", "process", "<PERSON><PERSON><PERSON><PERSON>", "isMix", "zoneSymbolEventNames$1", "enableBeforeunloadSymbol", "wrapFn", "event", "eventNameSymbol", "listener", "result", "errorEvent", "message", "filename", "lineno", "colno", "preventDefault", "returnValue", "patchProperty", "obj", "prop", "desc", "enumerable", "configurable", "onPropPatchedSymbol", "originalDescGet", "originalDescSet", "eventName", "newValue", "previousValue", "removeEventListener", "addEventListener", "removeAttribute", "onProperties", "j", "originalInstanceKey", "className", "OriginalClass", "a", "instance", "patchFn", "proto", "<PERSON><PERSON><PERSON>", "patchDelegate", "funcName", "metaCreator", "setNative", "cbIdx", "meta", "original", "isDetectedIEOrEdge", "ieOrEdge", "ua", "navigator", "userAgent", "indexOf", "isFunction", "isNumber", "OPTIMIZED_ZONE_EVENT_TASK_DATA", "zoneSymbolEventNames", "globalSources", "EVENT_NAME_SYMBOL_REGX", "RegExp", "IMMEDIATE_PROPAGATION_SYMBOL", "prepareEventNames", "eventNameToString", "falseEventName", "trueEventName", "symbolCapture", "api", "apis", "patchOptions", "ADD_EVENT_LISTENER", "add", "REMOVE_EVENT_LISTENER", "rm", "LISTENERS_EVENT_LISTENER", "listeners", "REMOVE_ALL_LISTENERS_EVENT_LISTENER", "rmAll", "zoneSymbolAddEventListener", "ADD_EVENT_LISTENER_SOURCE", "PREPEND_EVENT_LISTENER", "PREPEND_EVENT_LISTENER_SOURCE", "isRemoved", "handleEvent", "originalDelegate", "once", "globalCallback", "context", "isCapture", "tasks", "errors", "copyTasks", "globalZoneAwareCallback", "globalZoneAwareCaptureCallback", "patchEventTargetMethods", "useGlobalCallback", "validate<PERSON><PERSON><PERSON>", "vh", "chkDup", "<PERSON><PERSON><PERSON><PERSON>", "rt", "taskData", "nativeAddEventListener", "nativeRemoveEventListener", "nativeListeners", "nativeRemoveAllListeners", "nativePrependEventListener", "prepend", "buildEventListenerOptions", "passive", "capture", "customScheduleGlobal", "isExisting", "customCancelGlobal", "symbolEventNames", "symbolEventName", "existingTasks", "existingTask", "splice", "removeAbortListener", "allRemoved", "customScheduleNonGlobal", "customSchedulePrepend", "customCancelNonGlobal", "compareTaskCallbackVsDelegate", "typeOfDelegate", "compare", "diff", "unpatchedEvents", "passiveEvents", "copyEventListenerOptions", "newOptions", "signal", "makeAddListener", "nativeListener", "addSource", "customScheduleFn", "customCancelFn", "transferEventName", "isEventListenerObject", "aborted", "constructorName", "targetSource", "onAbort", "unshift", "onPropertySymbol", "findEventTasks", "keys", "match", "exec", "evtName", "symbolCaptureEventName", "captureTasks", "removeTasks", "results", "foundTasks", "captureFalseTasks", "captureTrueTasks", "concat", "Event", "patchQueueMicrotask", "taskSymbol", "patchTimer", "setName", "cancelName", "nameSuffix", "clearNative", "tasksByHandleId", "handleOrId", "handle", "refresh", "clearTask", "delay", "timer", "originalRefresh", "id", "patchCustomElements", "callbacks", "customElements", "eventTargetPatch", "eventNames", "EVENT_TARGET", "patchEvent", "ignoreProperties", "tip", "filter", "ip", "targetIgnoreProperties", "op", "patchFilteredProperties", "filteredProperties", "getOnEventNames", "getOwnPropertyNames", "startsWith", "map", "substring", "propertyDescriptorPatch", "patchTargets", "ignoreErrorProperties", "patchBrowser", "legacyPatch", "clear", "blockingMethods", "s", "XMLHttpRequestEventTarget", "patchXHR", "XHR_TASK", "XHR_SYNC", "XHR_LISTENER", "XHR_SCHEDULED", "XHR_URL", "XHR_ERROR_BEFORE_SCHEDULED", "XMLHttpRequest", "XMLHttpRequestPrototype", "findPendingTask", "oriAddListener", "oriRemoveListener", "XMLHttpRequestEventTargetPrototype", "READY_STATE_CHANGE", "SCHEDULED", "newListener", "readyState", "DONE", "loadTasks", "status", "oriInvoke", "storedTask", "sendNative", "placeholder<PERSON><PERSON><PERSON>", "abortNative", "openNative", "XMLHTTPREQUEST_SOURCE", "fetchTaskAborting", "fetchTaskScheduling", "url", "geolocation", "findPromiseRejectionHandler", "e", "eventTasks", "for<PERSON>ach", "PromiseRejectionEvent", "evt", "promise", "reason", "rejection", "patchPromise", "readableObjectToString", "JSON", "stringify", "_uncaughtPromiseErrors", "isDisableWrappingUncaughtPromiseRejection", "creationTrace", "console", "stack", "uncaughtPromiseError", "shift", "throwOriginal", "handleUnhandledRejection", "UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL", "handler", "isThenable", "then", "forwardResolution", "forwardRejection", "ZoneAwarePromise", "reject", "symbolState", "symbolValue", "symbolFinally", "symbolParentPromiseValue", "symbolParentPromiseState", "UNRESOLVED", "RESOLVED", "REJECTED", "REJECTED_NO_CATCH", "makeResolver", "v", "resolvePromise", "wasCalled", "wrapper", "wrappedFunction", "TYPE_ERROR", "CURRENT_TASK_TRACE_SYMBOL", "onceWrapper", "TypeError", "clearRejectedNoCatch", "trace", "scheduleResolveOrReject", "REJECTION_HANDLED_HANDLER", "chainPromise", "onFulfilled", "onRejected", "promiseState", "parentPromiseValue", "isFinallyPromise", "ZONE_AWARE_PROMISE_TO_STRING", "AggregateError", "withResolvers", "res", "rej", "any", "values", "Symbol", "iterator", "Promise", "promises", "finished", "race", "onResolve", "onReject", "all", "allWithCallback", "allSettled", "P", "then<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "unresolvedCount", "valueIndex", "resolvedV<PERSON>ues", "curValueIndex", "thenErr", "executor", "toStringTag", "species", "C", "catch", "finally", "onFinally", "NativePromise", "symbolThenPatched", "Ctor", "originalThen", "wrapped", "zoneify", "resultPromise", "ctor", "patchToString", "originalFunctionToString", "Function", "ORIGINAL_DELEGATE_SYMBOL", "PROMISE_SYMBOL", "ERROR_SYMBOL", "newFunctionToString", "nativePromise", "nativeError", "originalObjectToString", "PROMISE_OBJECT_TO_STRING", "targetName", "method", "nativeDelegate", "opts", "descriptor", "patchUtil", "SYMBOL_BLACK_LISTED_EVENTS", "SYMBOL_UNPATCHED_EVENTS", "patchCommon", "Zone$1"], "sources": ["C:/Users/<USER>/Downloads/Projet MSKStore-02-07-2025/MSKStoreAng/MSKStoreAng/node_modules/zone.js/fesm2015/zone.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\nconst global = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n    const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    return symbolPrefix + name;\n}\nfunction initZone() {\n    const performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    class ZoneImpl {\n        static __symbol__ = __symbol__;\n        static assertZonePatched() {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        }\n        static get root() {\n            let zone = ZoneImpl.current;\n            while (zone.parent) {\n                zone = zone.parent;\n            }\n            return zone;\n        }\n        static get current() {\n            return _currentZoneFrame.zone;\n        }\n        static get currentTask() {\n            return _currentTask;\n        }\n        static __load_patch(name, fn, ignoreDuplicate = false) {\n            if (patches.hasOwnProperty(name)) {\n                // `checkDuplicate` option is defined from global variable\n                // so it works for all modules.\n                // `ignoreDuplicate` can work for the specified module\n                const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n                if (!ignoreDuplicate && checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                const perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, ZoneImpl, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        }\n        get parent() {\n            return this._parent;\n        }\n        get name() {\n            return this._name;\n        }\n        _parent;\n        _name;\n        _properties;\n        _zoneDelegate;\n        constructor(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = (zoneSpec && zoneSpec.properties) || {};\n            this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        get(key) {\n            const zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        }\n        getZoneWith(key) {\n            let current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        }\n        fork(zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        }\n        wrap(callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            const _callback = this._zoneDelegate.intercept(this, callback, source);\n            const zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        }\n        run(callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runGuarded(callback, applyThis = null, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runTask(task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            }\n            const zoneTask = task;\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            const { type, data: { isPeriodic = false, isRefreshable = false } = {} } = task;\n            if (task.state === notScheduled && (type === eventTask || type === macroTask)) {\n                return;\n            }\n            const reEntryGuard = task.state != running;\n            reEntryGuard && zoneTask._transitionTo(running, scheduled);\n            const previousTask = _currentTask;\n            _currentTask = zoneTask;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (type == macroTask && task.data && !isPeriodic && !isRefreshable) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, zoneTask, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                const state = task.state;\n                if (state !== notScheduled && state !== unknown) {\n                    if (type == eventTask || isPeriodic || (isRefreshable && state === scheduling)) {\n                        reEntryGuard && zoneTask._transitionTo(scheduled, running, scheduling);\n                    }\n                    else {\n                        const zoneDelegates = zoneTask._zoneDelegates;\n                        this._updateTaskCount(zoneTask, -1);\n                        reEntryGuard && zoneTask._transitionTo(notScheduled, running, notScheduled);\n                        if (isRefreshable) {\n                            zoneTask._zoneDelegates = zoneDelegates;\n                        }\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        }\n        scheduleTask(task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                let newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            const zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        }\n        scheduleMicroTask(source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        }\n        scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        }\n        scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        }\n        cancelTask(task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            if (task.state !== scheduled && task.state !== running) {\n                return;\n            }\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = -1;\n            return task;\n        }\n        _updateTaskCount(task, count) {\n            const zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (let i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        }\n    }\n    const DELEGATE_ZS = {\n        name: '',\n        onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n        onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n        onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n        onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task),\n    };\n    class _ZoneDelegate {\n        get zone() {\n            return this._zone;\n        }\n        _zone;\n        _taskCounts = {\n            'microTask': 0,\n            'macroTask': 0,\n            'eventTask': 0,\n        };\n        _parentDelegate;\n        _forkDlgt;\n        _forkZS;\n        _forkCurrZone;\n        _interceptDlgt;\n        _interceptZS;\n        _interceptCurrZone;\n        _invokeDlgt;\n        _invokeZS;\n        _invokeCurrZone;\n        _handleErrorDlgt;\n        _handleErrorZS;\n        _handleErrorCurrZone;\n        _scheduleTaskDlgt;\n        _scheduleTaskZS;\n        _scheduleTaskCurrZone;\n        _invokeTaskDlgt;\n        _invokeTaskZS;\n        _invokeTaskCurrZone;\n        _cancelTaskDlgt;\n        _cancelTaskZS;\n        _cancelTaskCurrZone;\n        _hasTaskDlgt;\n        _hasTaskDlgtOwner;\n        _hasTaskZS;\n        _hasTaskCurrZone;\n        constructor(zone, parentDelegate, zoneSpec) {\n            this._zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone =\n                zoneSpec && (zoneSpec.onFork ? this._zone : parentDelegate._forkCurrZone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this._zone : parentDelegate._interceptCurrZone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone =\n                zoneSpec && (zoneSpec.onInvoke ? this._zone : parentDelegate._invokeCurrZone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this._zone : parentDelegate._handleErrorCurrZone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt =\n                zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this._zone : parentDelegate._scheduleTaskCurrZone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this._zone : parentDelegate._invokeTaskCurrZone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this._zone : parentDelegate._cancelTaskCurrZone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = this._zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this._zone;\n                }\n            }\n        }\n        fork(targetZone, zoneSpec) {\n            return this._forkZS\n                ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec)\n                : new ZoneImpl(targetZone, zoneSpec);\n        }\n        intercept(targetZone, callback, source) {\n            return this._interceptZS\n                ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source)\n                : callback;\n        }\n        invoke(targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS\n                ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source)\n                : callback.apply(applyThis, applyArgs);\n        }\n        handleError(targetZone, error) {\n            return this._handleErrorZS\n                ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error)\n                : true;\n        }\n        scheduleTask(targetZone, task) {\n            let returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        }\n        invokeTask(targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS\n                ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs)\n                : task.callback.apply(applyThis, applyArgs);\n        }\n        cancelTask(targetZone, task) {\n            let value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        }\n        hasTask(targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        }\n        _updateTaskCount(type, count) {\n            const counts = this._taskCounts;\n            const prev = counts[type];\n            const next = (counts[type] = prev + count);\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                const isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type,\n                };\n                this.hasTask(this._zone, isEmpty);\n            }\n        }\n    }\n    class ZoneTask {\n        type;\n        source;\n        invoke;\n        callback;\n        data;\n        scheduleFn;\n        cancelFn;\n        _zone = null;\n        runCount = 0;\n        _zoneDelegates = null;\n        _state = 'notScheduled';\n        constructor(type, source, callback, options, scheduleFn, cancelFn) {\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            if (!callback) {\n                throw new Error('callback is not defined');\n            }\n            this.callback = callback;\n            const self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        static invokeTask(task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        }\n        get zone() {\n            return this._zone;\n        }\n        get state() {\n            return this._state;\n        }\n        cancelScheduleRequest() {\n            this._transitionTo(notScheduled, scheduling);\n        }\n        _transitionTo(toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? \" or '\" + fromState2 + \"'\" : ''}, was '${this._state}'.`);\n            }\n        }\n        toString() {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        }\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        toJSON() {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount,\n            };\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const symbolSetTimeout = __symbol__('setTimeout');\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    let _microTaskQueue = [];\n    let _isDrainingMicrotaskQueue = false;\n    let nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n        if (!nativeMicroTaskQueuePromise) {\n            if (global[symbolPromise]) {\n                nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n            }\n        }\n        if (nativeMicroTaskQueuePromise) {\n            let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n            if (!nativeThen) {\n                // native Promise is not patchable, we need to use `then` directly\n                // issue 1078\n                nativeThen = nativeMicroTaskQueuePromise['then'];\n            }\n            nativeThen.call(nativeMicroTaskQueuePromise, func);\n        }\n        else {\n            global[symbolSetTimeout](func, 0);\n        }\n    }\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            nativeScheduleMicroTask(drainMicroTaskQueue);\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                const queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (let i = 0; i < queue.length; i++) {\n                    const task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const NO_ZONE = { name: 'NO ZONE' };\n    const notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    const microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    const patches = {};\n    const _api = {\n        symbol: __symbol__,\n        currentZoneFrame: () => _currentZoneFrame,\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: () => !ZoneImpl[__symbol__('ignoreConsoleErrorUncaughtError')],\n        patchEventTarget: () => [],\n        patchOnProperties: noop,\n        patchMethod: () => noop,\n        bindArguments: () => [],\n        patchThen: () => noop,\n        patchMacroTask: () => noop,\n        patchEventPrototype: () => noop,\n        isIEOrEdge: () => false,\n        getGlobalObjects: () => undefined,\n        ObjectDefineProperty: () => noop,\n        ObjectGetOwnPropertyDescriptor: () => undefined,\n        ObjectCreate: () => undefined,\n        ArraySlice: () => [],\n        patchClass: () => noop,\n        wrapWithCurrentZone: () => noop,\n        filterProperties: () => [],\n        attachOriginToPatched: () => noop,\n        _redefineProperty: () => noop,\n        patchCallbacks: () => noop,\n        nativeScheduleMicroTask: nativeScheduleMicroTask,\n    };\n    let _currentZoneFrame = { parent: null, zone: new ZoneImpl(null, null) };\n    let _currentTask = null;\n    let _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    performanceMeasure('Zone', 'Zone');\n    return ZoneImpl;\n}\n\nfunction loadZone() {\n    // if global['Zone'] already exists (maybe zone.js was already loaded or\n    // some other lib also registered a global object named Zone), we may need\n    // to throw an error, but sometimes user may not want this error.\n    // For example,\n    // we have two web pages, page1 includes zone.js, page2 doesn't.\n    // and the 1st time user load page1 and page2, everything work fine,\n    // but when user load page2 again, error occurs because global['Zone'] already exists.\n    // so we add a flag to let user choose whether to throw this error or not.\n    // By default, if existing Zone is from zone.js, we will not throw the error.\n    const global = globalThis;\n    const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone'] && (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function')) {\n        throw new Error('Zone already loaded.');\n    }\n    // Initialize global `Zone` constant.\n    global['Zone'] ??= initZone();\n    return global['Zone'];\n}\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = __symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = __symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = __symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = __symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = (isWindowExists && internalWindow) || globalThis;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    const source = prototype.constructor['name'];\n    for (let i = 0; i < fnNames.length; i++) {\n        const name = fnNames[i];\n        const delegate = prototype[name];\n        if (delegate) {\n            const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n            if (!isPropertyWritable(prototypeDesc)) {\n                continue;\n            }\n            prototype[name] = ((delegate) => {\n                const patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = !('nw' in _global) &&\n    typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]' &&\n    !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst enableBeforeunloadSymbol = zoneSymbol('enable_beforeunload');\nconst wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    const target = this || event.target || _global;\n    const listener = target[eventNameSymbol];\n    let result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signature\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        const errorEvent = event;\n        result =\n            listener &&\n                listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (\n        // https://github.com/angular/angular/issues/47579\n        // https://www.w3.org/TR/2011/WD-html5-20110525/history.html#beforeunloadevent\n        // This is the only specific case we should check for. The spec defines that the\n        // `returnValue` attribute represents the message to show the user. When the event\n        // is created, this attribute must be set to the empty string.\n        event.type === 'beforeunload' &&\n            // To prevent any breaking changes resulting from this change, given that\n            // it was already causing a significant number of failures in G3, we have hidden\n            // that behavior behind a global configuration flag. Consumers can enable this\n            // flag explicitly if they want the `beforeunload` event to be handled as defined\n            // in the specification.\n            _global[enableBeforeunloadSymbol] &&\n            // The IDL event definition is `attribute DOMString returnValue`, so we check whether\n            // `typeof result` is a string.\n            typeof result === 'string') {\n            event.returnValue = result;\n        }\n        else if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    const originalDescGet = desc.get;\n    const originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    const eventName = prop.slice(2);\n    let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // In some versions of Windows, the `this` context may be undefined\n        // in on-property callbacks.\n        // To handle this edge case, we check if `this` is falsy and\n        // fallback to `_global` if needed.\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        const previousValue = target[eventNameSymbol];\n        if (typeof previousValue === 'function') {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // https://github.com/angular/zone.js/issues/978\n        // If an inline handler (like `onload`) was defined before zone.js was loaded,\n        // call the original descriptor's setter to clean it up.\n        originalDescSet?.call(target, null);\n        target[eventNameSymbol] = newValue;\n        if (typeof newValue === 'function') {\n            target.addEventListener(eventName, wrapFn, false);\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        const listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            let value = originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (let i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        const onProperties = [];\n        for (const prop in obj) {\n            if (prop.slice(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (let j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    const OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        const a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    const instance = new OriginalClass(function () { });\n    let prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    },\n                });\n            }\n        })(prop);\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction patchMethod(target, name, patchFn) {\n    let proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    const delegateName = zoneSymbol(name);\n    let delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            const patchDelegate = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    let setNative = null;\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, (delegate) => function (self, args) {\n        const meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    });\n}\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) { }\n    return ieOrEdge;\n}\nfunction isFunction(value) {\n    return typeof value === 'function';\n}\nfunction isNumber(value) {\n    return typeof value === 'number';\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true,\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n    const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n    const ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    const REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    const LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    const REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    const PREPEND_EVENT_LISTENER = 'prependListener';\n    const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    const invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        const delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = (event) => delegate.handleEvent(event);\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        // need to try/catch error here, otherwise, the error in one event listener\n        // will break the executions of the other event listeners. Also error will\n        // not remove the event listener when `once` options is true.\n        let error;\n        try {\n            task.invoke(task, target, [event]);\n        }\n        catch (err) {\n            error = err;\n        }\n        const options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n        }\n        return error;\n    };\n    function globalCallback(context, event, isCapture) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        const target = context || event.target || _global;\n        const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n        if (tasks) {\n            const errors = [];\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                const err = invokeTask(tasks[0], target, event);\n                err && errors.push(err);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                const copyTasks = tasks.slice();\n                for (let i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    const err = invokeTask(copyTasks[i], target, event);\n                    err && errors.push(err);\n                }\n            }\n            // Since there is only one error, we don't need to schedule microTask\n            // to throw the error.\n            if (errors.length === 1) {\n                throw errors[0];\n            }\n            else {\n                for (let i = 0; i < errors.length; i++) {\n                    const err = errors[i];\n                    api.nativeScheduleMicroTask(() => {\n                        throw err;\n                    });\n                }\n            }\n        }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    const globalZoneAwareCallback = function (event) {\n        return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    const globalZoneAwareCaptureCallback = function (event) {\n        return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        let useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        const validateHandler = patchOptions && patchOptions.vh;\n        let checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        let returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        let proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        const eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // We use a shared global `taskData` to pass data for `scheduleEventTask`,\n        // eliminating the need to create a new object solely for passing data.\n        // WARNING: This object has a static lifetime, meaning it is not created\n        // each time `addEventListener` is called. It is instantiated only once\n        // and captured by reference inside the `addEventListener` and\n        // `removeEventListener` functions. Do not add any new properties to this\n        // object, as doing so would necessitate maintaining the information\n        // between `addEventListener` calls.\n        const taskData = {};\n        const nativeAddEventListener = (proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER]);\n        const nativeRemoveEventListener = (proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER]);\n        const nativeListeners = (proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER]);\n        const nativeRemoveAllListeners = (proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER]);\n        let nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        /**\n         * This util function will build an option object with passive option\n         * to handle all possible input from the user.\n         */\n        function buildEventListenerOptions(options, passive) {\n            if (!passive) {\n                return options;\n            }\n            if (typeof options === 'boolean') {\n                return { capture: options, passive: true };\n            }\n            if (!options) {\n                return { passive: true };\n            }\n            if (typeof options === 'object' && options.passive !== false) {\n                return { ...options, passive: true };\n            }\n            return options;\n        }\n        const customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        /**\n         * In the context of events and listeners, this function will be\n         * called at the end by `cancelTask`, which, in turn, calls `task.cancelFn`.\n         * Cancelling a task is primarily used to remove event listeners from\n         * the task target.\n         */\n        const customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                const symbolEventNames = zoneSymbolEventNames[task.eventName];\n                let symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                const existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (let i = 0; i < existingTasks.length; i++) {\n                        const existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (task.removeAbortListener) {\n                                task.removeAbortListener();\n                                task.removeAbortListener = null;\n                            }\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        const customScheduleNonGlobal = function (task) {\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        const compareTaskCallbackVsDelegate = function (task, delegate) {\n            const typeOfDelegate = typeof delegate;\n            return ((typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate));\n        };\n        const compare = patchOptions?.diff || compareTaskCallbackVsDelegate;\n        const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n        const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n        function copyEventListenerOptions(options) {\n            if (typeof options === 'object' && options !== null) {\n                // We need to destructure the target `options` object since it may\n                // be frozen or sealed (possibly provided implicitly by a third-party\n                // library), or its properties may be readonly.\n                const newOptions = { ...options };\n                // The `signal` option was recently introduced, which caused regressions in\n                // third-party scenarios where `AbortController` was directly provided to\n                // `addEventListener` as options. For instance, in cases like\n                // `document.addEventListener('keydown', callback, abortControllerInstance)`,\n                // which is valid because `AbortController` includes a `signal` getter, spreading\n                // `{...options}` wouldn't copy the `signal`. Additionally, using `Object.create`\n                // isn't feasible since `AbortController` is a built-in object type, and attempting\n                // to create a new object directly with it as the prototype might result in\n                // unexpected behavior.\n                if (options.signal) {\n                    newOptions.signal = options.signal;\n                }\n                return newOptions;\n            }\n            return options;\n        }\n        const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n            return function () {\n                const target = this || _global;\n                let eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                let delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // To improve `addEventListener` performance, we will create the callback\n                // for the task later when the task is invoked.\n                let isEventListenerObject = false;\n                if (typeof delegate !== 'function') {\n                    // This checks whether the provided listener argument is an object with\n                    // a `handleEvent` method (since we can call `addEventListener` with a\n                    // function `event => ...` or with an object `{ handleEvent: event => ... }`).\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isEventListenerObject = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                const passive = !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                const options = copyEventListenerOptions(buildEventListenerOptions(arguments[2], passive));\n                const signal = options?.signal;\n                if (signal?.aborted) {\n                    // the signal is an aborted one, just return without attaching the event listener.\n                    return;\n                }\n                if (unpatchedEvents) {\n                    // check unpatched list\n                    for (let i = 0; i < unpatchedEvents.length; i++) {\n                        if (eventName === unpatchedEvents[i]) {\n                            if (passive) {\n                                return nativeListener.call(target, eventName, delegate, options);\n                            }\n                            else {\n                                return nativeListener.apply(this, arguments);\n                            }\n                        }\n                    }\n                }\n                const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                const once = options && typeof options === 'object' ? options.once : false;\n                const zone = Zone.current;\n                let symbolEventNames = zoneSymbolEventNames[eventName];\n                if (!symbolEventNames) {\n                    prepareEventNames(eventName, eventNameToString);\n                    symbolEventNames = zoneSymbolEventNames[eventName];\n                }\n                const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                let existingTasks = target[symbolEventName];\n                let isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (let i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                let source;\n                const constructorName = target.constructor['name'];\n                const targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source =\n                        constructorName +\n                            addSource +\n                            (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // In the code below, `options` should no longer be reassigned; instead, it\n                // should only be mutated. This is because we pass that object to the native\n                // `addEventListener`.\n                // It's generally recommended to use the same object reference for options.\n                // This ensures consistency and avoids potential issues.\n                taskData.options = options;\n                if (once) {\n                    // When using `addEventListener` with the `once` option, we don't pass\n                    // the `once` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `once` setting and handle it ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                if (signal) {\n                    // When using `addEventListener` with the `signal` option, we don't pass\n                    // the `signal` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `signal` setting and handle it ourselves.\n                    taskData.options.signal = undefined;\n                }\n                // The `scheduleEventTask` function will ultimately call `customScheduleGlobal`,\n                // which in turn calls the native `addEventListener`. This is why `taskData.options`\n                // is updated before scheduling the task, as `customScheduleGlobal` uses\n                // `taskData.options` to pass it to the native `addEventListener`.\n                const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                if (signal) {\n                    // after task is scheduled, we need to store the signal back to task.options\n                    taskData.options.signal = signal;\n                    // Wrapping `task` in a weak reference would not prevent memory leaks. Weak references are\n                    // primarily used for preventing strong references cycles. `onAbort` is always reachable\n                    // as it's an event listener, so its closure retains a strong reference to the `task`.\n                    const onAbort = () => task.zone.cancelTask(task);\n                    nativeListener.call(signal, 'abort', onAbort, { once: true });\n                    // We need to remove the `abort` listener when the event listener is going to be removed,\n                    // as it creates a closure that captures `task`. This closure retains a reference to the\n                    // `task` object even after it goes out of scope, preventing `task` from being garbage\n                    // collected.\n                    task.removeAbortListener = () => signal.removeEventListener('abort', onAbort);\n                }\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    taskData.options.once = true;\n                }\n                if (typeof task.options !== 'boolean') {\n                    // We should save the options on the task (if it's an object) because\n                    // we'll be using `task.options` later when removing the event listener\n                    // and passing it back to `removeEventListener`.\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isEventListenerObject) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const options = arguments[2];\n            const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n            const delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            const symbolEventNames = zoneSymbolEventNames[eventName];\n            let symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            const existingTasks = symbolEventName && target[symbolEventName];\n            // `existingTasks` may not exist if the `addEventListener` was called before\n            // it was patched by zone.js. Please refer to the attached issue for\n            // clarification, particularly after the `if` condition, before calling\n            // the native `removeEventListener`.\n            if (existingTasks) {\n                for (let i = 0; i < existingTasks.length; i++) {\n                    const existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                            // in the target, we have an event listener which is added by on_property\n                            // such as target.onclick = function() {}, so we need to clear this internal\n                            // property too if all delegates with capture=false were removed\n                            // https:// github.com/angular/angular/issues/31643\n                            // https://github.com/angular/angular/issues/54581\n                            if (!capture && typeof eventName === 'string') {\n                                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                target[onPropertySymbol] = null;\n                            }\n                        }\n                        // In all other conditions, when `addEventListener` is called after being\n                        // patched by zone.js, we would always find an event task on the `EventTarget`.\n                        // This will trigger `cancelFn` on the `existingTask`, leading to `customCancelGlobal`,\n                        // which ultimately removes an event listener and cleans up the abort listener\n                        // (if an `AbortSignal` was provided when scheduling a task).\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // https://github.com/angular/zone.js/issues/930\n            // We may encounter a situation where the `addEventListener` was\n            // called on the event target before zone.js is loaded, resulting\n            // in no task being stored on the event target due to its invocation\n            // of the native implementation. In this scenario, we simply need to\n            // invoke the native `removeEventListener`.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const listeners = [];\n            const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (let i = 0; i < tasks.length; i++) {\n                const task = tasks[i];\n                let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (!eventName) {\n                const keys = Object.keys(target);\n                for (let i = 0; i < keys.length; i++) {\n                    const prop = keys[i];\n                    const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    let evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                const symbolEventNames = zoneSymbolEventNames[eventName];\n                if (symbolEventNames) {\n                    const symbolEventName = symbolEventNames[FALSE_STR];\n                    const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    const tasks = target[symbolEventName];\n                    const captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        const removeTasks = tasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        const removeTasks = captureTasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    let results = [];\n    for (let i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    if (!eventName) {\n        const foundTasks = [];\n        for (let prop in target) {\n            const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            let evtName = match && match[1];\n            if (evtName && (!eventName || evtName === eventName)) {\n                const tasks = target[prop];\n                if (tasks) {\n                    for (let i = 0; i < tasks.length; i++) {\n                        foundTasks.push(tasks[i]);\n                    }\n                }\n            }\n        }\n        return foundTasks;\n    }\n    let symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n        prepareEventNames(eventName);\n        symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n        return captureTrueTasks ? captureTrueTasks.slice() : [];\n    }\n    else {\n        return captureTrueTasks\n            ? captureFalseTasks.concat(captureTrueTasks)\n            : captureFalseTasks.slice();\n    }\n}\nfunction patchEventPrototype(global, api) {\n    const Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', (delegate) => function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        });\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n    api.patchMethod(global, 'queueMicrotask', (delegate) => {\n        return function (self, args) {\n            Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n        };\n    });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    let setNative = null;\n    let clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    const tasksByHandleId = {};\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[0] = function () {\n            return task.invoke.apply(this, arguments);\n        };\n        const handleOrId = setNative.apply(window, data.args);\n        // Whlist on Node.js when get can the ID by using `[Symbol.toPrimitive]()` we do\n        // to this so that we do not cause potentally leaks when using `setTimeout`\n        // since this can be periodic when using `.refresh`.\n        if (isNumber(handleOrId)) {\n            data.handleId = handleOrId;\n        }\n        else {\n            data.handle = handleOrId;\n            // On Node.js a timeout and interval can be restarted over and over again by using the `.refresh` method.\n            data.isRefreshable = isFunction(handleOrId.refresh);\n        }\n        return task;\n    }\n    function clearTask(task) {\n        const { handle, handleId } = task.data;\n        return clearNative.call(window, handle ?? handleId);\n    }\n    setNative = patchMethod(window, setName, (delegate) => function (self, args) {\n        if (isFunction(args[0])) {\n            const options = {\n                isRefreshable: false,\n                isPeriodic: nameSuffix === 'Interval',\n                delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n                args: args,\n            };\n            const callback = args[0];\n            args[0] = function timer() {\n                try {\n                    return callback.apply(this, arguments);\n                }\n                finally {\n                    // issue-934, task will be cancelled\n                    // even it is a periodic task such as\n                    // setInterval\n                    // https://github.com/angular/angular/issues/40387\n                    // Cleanup tasksByHandleId should be handled before scheduleTask\n                    // Since some zoneSpec may intercept and doesn't trigger\n                    // scheduleFn(scheduleTask) provided here.\n                    const { handle, handleId, isPeriodic, isRefreshable } = options;\n                    if (!isPeriodic && !isRefreshable) {\n                        if (handleId) {\n                            // in non-nodejs env, we remove timerId\n                            // from local cache\n                            delete tasksByHandleId[handleId];\n                        }\n                        else if (handle) {\n                            // Node returns complex objects as handleIds\n                            // we remove task reference from timer object\n                            handle[taskSymbol] = null;\n                        }\n                    }\n                }\n            };\n            const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n            if (!task) {\n                return task;\n            }\n            // Node.js must additionally support the ref and unref functions.\n            const { handleId, handle, isRefreshable, isPeriodic } = task.data;\n            if (handleId) {\n                // for non nodejs env, we save handleId: task\n                // mapping in local cache for clearTimeout\n                tasksByHandleId[handleId] = task;\n            }\n            else if (handle) {\n                // for nodejs env, we save task\n                // reference in timerId Object for clearTimeout\n                handle[taskSymbol] = task;\n                if (isRefreshable && !isPeriodic) {\n                    const originalRefresh = handle.refresh;\n                    handle.refresh = function () {\n                        const { zone, state } = task;\n                        if (state === 'notScheduled') {\n                            task._state = 'scheduled';\n                            zone._updateTaskCount(task, 1);\n                        }\n                        else if (state === 'running') {\n                            task._state = 'scheduling';\n                        }\n                        return originalRefresh.call(this);\n                    };\n                }\n            }\n            return handle ?? handleId ?? task;\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(window, args);\n        }\n    });\n    clearNative = patchMethod(window, cancelName, (delegate) => function (self, args) {\n        const id = args[0];\n        let task;\n        if (isNumber(id)) {\n            // non nodejs env.\n            task = tasksByHandleId[id];\n            delete tasksByHandleId[id];\n        }\n        else {\n            // nodejs env ?? other environments.\n            task = id?.[taskSymbol];\n            if (task) {\n                id[taskSymbol] = null;\n            }\n            else {\n                task = id;\n            }\n        }\n        if (task?.type) {\n            if (task.cancelFn) {\n                // Do not cancel already canceled functions\n                task.zone.cancelTask(task);\n            }\n        }\n        else {\n            // cause an error by calling it directly.\n            delegate.apply(window, args);\n        }\n    });\n}\n\nfunction patchCustomElements(_global, api) {\n    const { isBrowser, isMix } = api.getGlobalObjects();\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    // https://html.spec.whatwg.org/multipage/custom-elements.html#concept-custom-element-definition-lifecycle-callbacks\n    const callbacks = [\n        'connectedCallback',\n        'disconnectedCallback',\n        'adoptedCallback',\n        'attributeChangedCallback',\n        'formAssociatedCallback',\n        'formDisabledCallback',\n        'formResetCallback',\n        'formStateRestoreCallback',\n    ];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    const { eventNames, zoneSymbolEventNames, TRUE_STR, FALSE_STR, ZONE_SYMBOL_PREFIX } = api.getGlobalObjects();\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (let i = 0; i < eventNames.length; i++) {\n        const eventName = eventNames[i];\n        const falseEventName = eventName + FALSE_STR;\n        const trueEventName = eventName + TRUE_STR;\n        const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    const EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    const tip = ignoreProperties.filter((ip) => ip.target === target);\n    if (tip.length === 0) {\n        return onProperties;\n    }\n    const targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter((op) => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target)\n        .filter((name) => name.startsWith('on') && name.length > 2)\n        .map((name) => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    const ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    let patchTargets = [];\n    if (isBrowser) {\n        const internalWindow = window;\n        patchTargets = patchTargets.concat([\n            'Document',\n            'SVGElement',\n            'Element',\n            'HTMLElement',\n            'HTMLBodyElement',\n            'HTMLMediaElement',\n            'HTMLFrameSetElement',\n            'HTMLFrameElement',\n            'HTMLIFrameElement',\n            'HTMLMarqueeElement',\n            'Worker',\n        ]);\n        const ignoreErrorProperties = [];\n        // In older browsers like IE or Edge, event handler properties (e.g., `onclick`)\n        // may not be defined directly on the `window` object but on its prototype (`WindowPrototype`).\n        // To ensure complete coverage, we use the prototype when checking\n        // for and patching these properties.\n        patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    }\n    patchTargets = patchTargets.concat([\n        'XMLHttpRequest',\n        'XMLHttpRequestEventTarget',\n        'IDBIndex',\n        'IDBRequest',\n        'IDBOpenDBRequest',\n        'IDBDatabase',\n        'IDBTransaction',\n        'IDBCursor',\n        'WebSocket',\n    ]);\n    for (let i = 0; i < patchTargets.length; i++) {\n        const target = _global[patchTargets[i]];\n        target?.prototype &&\n            patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchBrowser(Zone) {\n    Zone.__load_patch('legacy', (global) => {\n        const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n        if (legacyPatch) {\n            legacyPatch();\n        }\n    });\n    Zone.__load_patch('timers', (global) => {\n        const set = 'set';\n        const clear = 'clear';\n        patchTimer(global, set, clear, 'Timeout');\n        patchTimer(global, set, clear, 'Interval');\n        patchTimer(global, set, clear, 'Immediate');\n    });\n    Zone.__load_patch('requestAnimationFrame', (global) => {\n        patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n        patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n        patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n    });\n    Zone.__load_patch('blocking', (global, Zone) => {\n        const blockingMethods = ['alert', 'prompt', 'confirm'];\n        for (let i = 0; i < blockingMethods.length; i++) {\n            const name = blockingMethods[i];\n            patchMethod(global, name, (delegate, symbol, name) => {\n                return function (s, args) {\n                    return Zone.current.run(delegate, global, args, name);\n                };\n            });\n        }\n    });\n    Zone.__load_patch('EventTarget', (global, Zone, api) => {\n        patchEvent(global, api);\n        eventTargetPatch(global, api);\n        // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n        const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n        if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n            api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n        }\n    });\n    Zone.__load_patch('MutationObserver', (global, Zone, api) => {\n        patchClass('MutationObserver');\n        patchClass('WebKitMutationObserver');\n    });\n    Zone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n        patchClass('IntersectionObserver');\n    });\n    Zone.__load_patch('FileReader', (global, Zone, api) => {\n        patchClass('FileReader');\n    });\n    Zone.__load_patch('on_property', (global, Zone, api) => {\n        propertyDescriptorPatch(api, global);\n    });\n    Zone.__load_patch('customElements', (global, Zone, api) => {\n        patchCustomElements(global, api);\n    });\n    Zone.__load_patch('XHR', (global, Zone) => {\n        // Treat XMLHttpRequest as a macrotask.\n        patchXHR(global);\n        const XHR_TASK = zoneSymbol('xhrTask');\n        const XHR_SYNC = zoneSymbol('xhrSync');\n        const XHR_LISTENER = zoneSymbol('xhrListener');\n        const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n        const XHR_URL = zoneSymbol('xhrURL');\n        const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n        function patchXHR(window) {\n            const XMLHttpRequest = window['XMLHttpRequest'];\n            if (!XMLHttpRequest) {\n                // XMLHttpRequest is not available in service worker\n                return;\n            }\n            const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n            function findPendingTask(target) {\n                return target[XHR_TASK];\n            }\n            let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n            let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            if (!oriAddListener) {\n                const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n                if (XMLHttpRequestEventTarget) {\n                    const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n                    oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n            }\n            const READY_STATE_CHANGE = 'readystatechange';\n            const SCHEDULED = 'scheduled';\n            function scheduleTask(task) {\n                const data = task.data;\n                const target = data.target;\n                target[XHR_SCHEDULED] = false;\n                target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n                // remove existing event listener\n                const listener = target[XHR_LISTENER];\n                if (!oriAddListener) {\n                    oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n                if (listener) {\n                    oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n                }\n                const newListener = (target[XHR_LISTENER] = () => {\n                    if (target.readyState === target.DONE) {\n                        // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                        // readyState=4 multiple times, so we need to check task state here\n                        if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                            // check whether the xhr has registered onload listener\n                            // if that is the case, the task should invoke after all\n                            // onload listeners finish.\n                            // Also if the request failed without response (status = 0), the load event handler\n                            // will not be triggered, in that case, we should also invoke the placeholder callback\n                            // to close the XMLHttpRequest::send macroTask.\n                            // https://github.com/angular/angular/issues/38795\n                            const loadTasks = target[Zone.__symbol__('loadfalse')];\n                            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                                const oriInvoke = task.invoke;\n                                task.invoke = function () {\n                                    // need to load the tasks again, because in other\n                                    // load listener, they may remove themselves\n                                    const loadTasks = target[Zone.__symbol__('loadfalse')];\n                                    for (let i = 0; i < loadTasks.length; i++) {\n                                        if (loadTasks[i] === task) {\n                                            loadTasks.splice(i, 1);\n                                        }\n                                    }\n                                    if (!data.aborted && task.state === SCHEDULED) {\n                                        oriInvoke.call(task);\n                                    }\n                                };\n                                loadTasks.push(task);\n                            }\n                            else {\n                                task.invoke();\n                            }\n                        }\n                        else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                            // error occurs when xhr.send()\n                            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                        }\n                    }\n                });\n                oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n                const storedTask = target[XHR_TASK];\n                if (!storedTask) {\n                    target[XHR_TASK] = task;\n                }\n                sendNative.apply(target, data.args);\n                target[XHR_SCHEDULED] = true;\n                return task;\n            }\n            function placeholderCallback() { }\n            function clearTask(task) {\n                const data = task.data;\n                // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n                // to prevent it from firing. So instead, we store info for the event listener.\n                data.aborted = true;\n                return abortNative.apply(data.target, data.args);\n            }\n            const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n                self[XHR_SYNC] = args[2] == false;\n                self[XHR_URL] = args[1];\n                return openNative.apply(self, args);\n            });\n            const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n            const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n            const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n            const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n                if (Zone.current[fetchTaskScheduling] === true) {\n                    // a fetch is scheduling, so we are using xhr to polyfill fetch\n                    // and because we already schedule macroTask for fetch, we should\n                    // not schedule a macroTask for xhr again\n                    return sendNative.apply(self, args);\n                }\n                if (self[XHR_SYNC]) {\n                    // if the XHR is sync there is no task to schedule, just execute the code.\n                    return sendNative.apply(self, args);\n                }\n                else {\n                    const options = {\n                        target: self,\n                        url: self[XHR_URL],\n                        isPeriodic: false,\n                        args: args,\n                        aborted: false,\n                    };\n                    const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                    if (self &&\n                        self[XHR_ERROR_BEFORE_SCHEDULED] === true &&\n                        !options.aborted &&\n                        task.state === SCHEDULED) {\n                        // xhr request throw error when send\n                        // we should invoke task instead of leaving a scheduled\n                        // pending macroTask\n                        task.invoke();\n                    }\n                }\n            });\n            const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n                const task = findPendingTask(self);\n                if (task && typeof task.type == 'string') {\n                    // If the XHR has already completed, do nothing.\n                    // If the XHR has already been aborted, do nothing.\n                    // Fix #569, call abort multiple times before done will cause\n                    // macroTask task count be negative number\n                    if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                        return;\n                    }\n                    task.zone.cancelTask(task);\n                }\n                else if (Zone.current[fetchTaskAborting] === true) {\n                    // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                    return abortNative.apply(self, args);\n                }\n                // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n                // task\n                // to cancel. Do nothing.\n            });\n        }\n    });\n    Zone.__load_patch('geolocation', (global) => {\n        /// GEO_LOCATION\n        if (global['navigator'] && global['navigator'].geolocation) {\n            patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n        }\n    });\n    Zone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n        // handle unhandled promise rejection\n        function findPromiseRejectionHandler(evtName) {\n            return function (e) {\n                const eventTasks = findEventTasks(global, evtName);\n                eventTasks.forEach((eventTask) => {\n                    // windows has added unhandledrejection event listener\n                    // trigger the event listener\n                    const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                    if (PromiseRejectionEvent) {\n                        const evt = new PromiseRejectionEvent(evtName, {\n                            promise: e.promise,\n                            reason: e.rejection,\n                        });\n                        eventTask.invoke(evt);\n                    }\n                });\n            };\n        }\n        if (global['PromiseRejectionEvent']) {\n            Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n                findPromiseRejectionHandler('unhandledrejection');\n            Zone[zoneSymbol('rejectionHandledHandler')] =\n                findPromiseRejectionHandler('rejectionhandled');\n        }\n    });\n    Zone.__load_patch('queueMicrotask', (global, Zone, api) => {\n        patchQueueMicrotask(global, api);\n    });\n}\n\nfunction patchPromise(Zone) {\n    Zone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n        const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n        const ObjectDefineProperty = Object.defineProperty;\n        function readableObjectToString(obj) {\n            if (obj && obj.toString === Object.prototype.toString) {\n                const className = obj.constructor && obj.constructor.name;\n                return (className ? className : '') + ': ' + JSON.stringify(obj);\n            }\n            return obj ? obj.toString() : Object.prototype.toString.call(obj);\n        }\n        const __symbol__ = api.symbol;\n        const _uncaughtPromiseErrors = [];\n        const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] !== false;\n        const symbolPromise = __symbol__('Promise');\n        const symbolThen = __symbol__('then');\n        const creationTrace = '__creationTrace__';\n        api.onUnhandledError = (e) => {\n            if (api.showUncaughtError()) {\n                const rejection = e && e.rejection;\n                if (rejection) {\n                    console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n                }\n                else {\n                    console.error(e);\n                }\n            }\n        };\n        api.microtaskDrainDone = () => {\n            while (_uncaughtPromiseErrors.length) {\n                const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n                try {\n                    uncaughtPromiseError.zone.runGuarded(() => {\n                        if (uncaughtPromiseError.throwOriginal) {\n                            throw uncaughtPromiseError.rejection;\n                        }\n                        throw uncaughtPromiseError;\n                    });\n                }\n                catch (error) {\n                    handleUnhandledRejection(error);\n                }\n            }\n        };\n        const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n        function handleUnhandledRejection(e) {\n            api.onUnhandledError(e);\n            try {\n                const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n                if (typeof handler === 'function') {\n                    handler.call(this, e);\n                }\n            }\n            catch (err) { }\n        }\n        function isThenable(value) {\n            return value && typeof value.then === 'function';\n        }\n        function forwardResolution(value) {\n            return value;\n        }\n        function forwardRejection(rejection) {\n            return ZoneAwarePromise.reject(rejection);\n        }\n        const symbolState = __symbol__('state');\n        const symbolValue = __symbol__('value');\n        const symbolFinally = __symbol__('finally');\n        const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n        const symbolParentPromiseState = __symbol__('parentPromiseState');\n        const source = 'Promise.then';\n        const UNRESOLVED = null;\n        const RESOLVED = true;\n        const REJECTED = false;\n        const REJECTED_NO_CATCH = 0;\n        function makeResolver(promise, state) {\n            return (v) => {\n                try {\n                    resolvePromise(promise, state, v);\n                }\n                catch (err) {\n                    resolvePromise(promise, false, err);\n                }\n                // Do not return value or you will break the Promise spec.\n            };\n        }\n        const once = function () {\n            let wasCalled = false;\n            return function wrapper(wrappedFunction) {\n                return function () {\n                    if (wasCalled) {\n                        return;\n                    }\n                    wasCalled = true;\n                    wrappedFunction.apply(null, arguments);\n                };\n            };\n        };\n        const TYPE_ERROR = 'Promise resolved with itself';\n        const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n        // Promise Resolution\n        function resolvePromise(promise, state, value) {\n            const onceWrapper = once();\n            if (promise === value) {\n                throw new TypeError(TYPE_ERROR);\n            }\n            if (promise[symbolState] === UNRESOLVED) {\n                // should only get value.then once based on promise spec.\n                let then = null;\n                try {\n                    if (typeof value === 'object' || typeof value === 'function') {\n                        then = value && value.then;\n                    }\n                }\n                catch (err) {\n                    onceWrapper(() => {\n                        resolvePromise(promise, false, err);\n                    })();\n                    return promise;\n                }\n                // if (value instanceof ZoneAwarePromise) {\n                if (state !== REJECTED &&\n                    value instanceof ZoneAwarePromise &&\n                    value.hasOwnProperty(symbolState) &&\n                    value.hasOwnProperty(symbolValue) &&\n                    value[symbolState] !== UNRESOLVED) {\n                    clearRejectedNoCatch(value);\n                    resolvePromise(promise, value[symbolState], value[symbolValue]);\n                }\n                else if (state !== REJECTED && typeof then === 'function') {\n                    try {\n                        then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                    }\n                    catch (err) {\n                        onceWrapper(() => {\n                            resolvePromise(promise, false, err);\n                        })();\n                    }\n                }\n                else {\n                    promise[symbolState] = state;\n                    const queue = promise[symbolValue];\n                    promise[symbolValue] = value;\n                    if (promise[symbolFinally] === symbolFinally) {\n                        // the promise is generated by Promise.prototype.finally\n                        if (state === RESOLVED) {\n                            // the state is resolved, should ignore the value\n                            // and use parent promise value\n                            promise[symbolState] = promise[symbolParentPromiseState];\n                            promise[symbolValue] = promise[symbolParentPromiseValue];\n                        }\n                    }\n                    // record task information in value when error occurs, so we can\n                    // do some additional work such as render longStackTrace\n                    if (state === REJECTED && value instanceof Error) {\n                        // check if longStackTraceZone is here\n                        const trace = Zone.currentTask &&\n                            Zone.currentTask.data &&\n                            Zone.currentTask.data[creationTrace];\n                        if (trace) {\n                            // only keep the long stack trace into error when in longStackTraceZone\n                            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                                configurable: true,\n                                enumerable: false,\n                                writable: true,\n                                value: trace,\n                            });\n                        }\n                    }\n                    for (let i = 0; i < queue.length;) {\n                        scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                    }\n                    if (queue.length == 0 && state == REJECTED) {\n                        promise[symbolState] = REJECTED_NO_CATCH;\n                        let uncaughtPromiseError = value;\n                        try {\n                            // Here we throws a new Error to print more readable error log\n                            // and if the value is not an error, zone.js builds an `Error`\n                            // Object here to attach the stack information.\n                            throw new Error('Uncaught (in promise): ' +\n                                readableObjectToString(value) +\n                                (value && value.stack ? '\\n' + value.stack : ''));\n                        }\n                        catch (err) {\n                            uncaughtPromiseError = err;\n                        }\n                        if (isDisableWrappingUncaughtPromiseRejection) {\n                            // If disable wrapping uncaught promise reject\n                            // use the value instead of wrapping it.\n                            uncaughtPromiseError.throwOriginal = true;\n                        }\n                        uncaughtPromiseError.rejection = value;\n                        uncaughtPromiseError.promise = promise;\n                        uncaughtPromiseError.zone = Zone.current;\n                        uncaughtPromiseError.task = Zone.currentTask;\n                        _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                        api.scheduleMicroTask(); // to make sure that it is running\n                    }\n                }\n            }\n            // Resolving an already resolved promise is a noop.\n            return promise;\n        }\n        const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n        function clearRejectedNoCatch(promise) {\n            if (promise[symbolState] === REJECTED_NO_CATCH) {\n                // if the promise is rejected no catch status\n                // and queue.length > 0, means there is a error handler\n                // here to handle the rejected promise, we should trigger\n                // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n                // eventHandler\n                try {\n                    const handler = Zone[REJECTION_HANDLED_HANDLER];\n                    if (handler && typeof handler === 'function') {\n                        handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                    }\n                }\n                catch (err) { }\n                promise[symbolState] = REJECTED;\n                for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                    if (promise === _uncaughtPromiseErrors[i].promise) {\n                        _uncaughtPromiseErrors.splice(i, 1);\n                    }\n                }\n            }\n        }\n        function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n            clearRejectedNoCatch(promise);\n            const promiseState = promise[symbolState];\n            const delegate = promiseState\n                ? typeof onFulfilled === 'function'\n                    ? onFulfilled\n                    : forwardResolution\n                : typeof onRejected === 'function'\n                    ? onRejected\n                    : forwardRejection;\n            zone.scheduleMicroTask(source, () => {\n                try {\n                    const parentPromiseValue = promise[symbolValue];\n                    const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                    if (isFinallyPromise) {\n                        // if the promise is generated from finally call, keep parent promise's state and value\n                        chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                        chainPromise[symbolParentPromiseState] = promiseState;\n                    }\n                    // should not pass value to finally callback\n                    const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution\n                        ? []\n                        : [parentPromiseValue]);\n                    resolvePromise(chainPromise, true, value);\n                }\n                catch (error) {\n                    // if error occurs, should always return this error\n                    resolvePromise(chainPromise, false, error);\n                }\n            }, chainPromise);\n        }\n        const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n        const noop = function () { };\n        const AggregateError = global.AggregateError;\n        class ZoneAwarePromise {\n            static toString() {\n                return ZONE_AWARE_PROMISE_TO_STRING;\n            }\n            static resolve(value) {\n                if (value instanceof ZoneAwarePromise) {\n                    return value;\n                }\n                return resolvePromise(new this(null), RESOLVED, value);\n            }\n            static reject(error) {\n                return resolvePromise(new this(null), REJECTED, error);\n            }\n            static withResolvers() {\n                const result = {};\n                result.promise = new ZoneAwarePromise((res, rej) => {\n                    result.resolve = res;\n                    result.reject = rej;\n                });\n                return result;\n            }\n            static any(values) {\n                if (!values || typeof values[Symbol.iterator] !== 'function') {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                const promises = [];\n                let count = 0;\n                try {\n                    for (let v of values) {\n                        count++;\n                        promises.push(ZoneAwarePromise.resolve(v));\n                    }\n                }\n                catch (err) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                if (count === 0) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                let finished = false;\n                const errors = [];\n                return new ZoneAwarePromise((resolve, reject) => {\n                    for (let i = 0; i < promises.length; i++) {\n                        promises[i].then((v) => {\n                            if (finished) {\n                                return;\n                            }\n                            finished = true;\n                            resolve(v);\n                        }, (err) => {\n                            errors.push(err);\n                            count--;\n                            if (count === 0) {\n                                finished = true;\n                                reject(new AggregateError(errors, 'All promises were rejected'));\n                            }\n                        });\n                    }\n                });\n            }\n            static race(values) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                function onResolve(value) {\n                    resolve(value);\n                }\n                function onReject(error) {\n                    reject(error);\n                }\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    value.then(onResolve, onReject);\n                }\n                return promise;\n            }\n            static all(values) {\n                return ZoneAwarePromise.allWithCallback(values);\n            }\n            static allSettled(values) {\n                const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n                return P.allWithCallback(values, {\n                    thenCallback: (value) => ({ status: 'fulfilled', value }),\n                    errorCallback: (err) => ({ status: 'rejected', reason: err }),\n                });\n            }\n            static allWithCallback(values, callback) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                // Start at 2 to prevent prematurely resolving if .then is called immediately.\n                let unresolvedCount = 2;\n                let valueIndex = 0;\n                const resolvedValues = [];\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    const curValueIndex = valueIndex;\n                    try {\n                        value.then((value) => {\n                            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }, (err) => {\n                            if (!callback) {\n                                reject(err);\n                            }\n                            else {\n                                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                                unresolvedCount--;\n                                if (unresolvedCount === 0) {\n                                    resolve(resolvedValues);\n                                }\n                            }\n                        });\n                    }\n                    catch (thenErr) {\n                        reject(thenErr);\n                    }\n                    unresolvedCount++;\n                    valueIndex++;\n                }\n                // Make the unresolvedCount zero-based again.\n                unresolvedCount -= 2;\n                if (unresolvedCount === 0) {\n                    resolve(resolvedValues);\n                }\n                return promise;\n            }\n            constructor(executor) {\n                const promise = this;\n                if (!(promise instanceof ZoneAwarePromise)) {\n                    throw new Error('Must be an instanceof Promise.');\n                }\n                promise[symbolState] = UNRESOLVED;\n                promise[symbolValue] = []; // queue;\n                try {\n                    const onceWrapper = once();\n                    executor &&\n                        executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n                }\n                catch (error) {\n                    resolvePromise(promise, false, error);\n                }\n            }\n            get [Symbol.toStringTag]() {\n                return 'Promise';\n            }\n            get [Symbol.species]() {\n                return ZoneAwarePromise;\n            }\n            then(onFulfilled, onRejected) {\n                // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n                // may be an object without a prototype (created through `Object.create(null)`); thus\n                // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n                // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n                // object and copies promise properties into that object (within the `getOrCreateLoad`\n                // function). The zone.js then checks if the resolved value has the `then` method and\n                // invokes it with the `value` context. Otherwise, this will throw an error: `TypeError:\n                // Cannot read properties of undefined (reading 'Symbol(Symbol.species)')`.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = this.constructor || ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n                }\n                return chainPromise;\n            }\n            catch(onRejected) {\n                return this.then(null, onRejected);\n            }\n            finally(onFinally) {\n                // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                chainPromise[symbolFinally] = symbolFinally;\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n                }\n                return chainPromise;\n            }\n        }\n        // Protect against aggressive optimizers dropping seemingly unused properties.\n        // E.g. Closure Compiler in advanced mode.\n        ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n        ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n        ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n        ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n        const NativePromise = (global[symbolPromise] = global['Promise']);\n        global['Promise'] = ZoneAwarePromise;\n        const symbolThenPatched = __symbol__('thenPatched');\n        function patchThen(Ctor) {\n            const proto = Ctor.prototype;\n            const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n            if (prop && (prop.writable === false || !prop.configurable)) {\n                // check Ctor.prototype.then propertyDescriptor is writable or not\n                // in meteor env, writable is false, we should ignore such case\n                return;\n            }\n            const originalThen = proto.then;\n            // Keep a reference to the original method.\n            proto[symbolThen] = originalThen;\n            Ctor.prototype.then = function (onResolve, onReject) {\n                const wrapped = new ZoneAwarePromise((resolve, reject) => {\n                    originalThen.call(this, resolve, reject);\n                });\n                return wrapped.then(onResolve, onReject);\n            };\n            Ctor[symbolThenPatched] = true;\n        }\n        api.patchThen = patchThen;\n        function zoneify(fn) {\n            return function (self, args) {\n                let resultPromise = fn.apply(self, args);\n                if (resultPromise instanceof ZoneAwarePromise) {\n                    return resultPromise;\n                }\n                let ctor = resultPromise.constructor;\n                if (!ctor[symbolThenPatched]) {\n                    patchThen(ctor);\n                }\n                return resultPromise;\n            };\n        }\n        if (NativePromise) {\n            patchThen(NativePromise);\n            patchMethod(global, 'fetch', (delegate) => zoneify(delegate));\n        }\n        // This is not part of public API, but it is useful for tests, so we expose it.\n        Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n        return ZoneAwarePromise;\n    });\n}\n\nfunction patchToString(Zone) {\n    // override Function.prototype.toString to make zone.js patched function\n    // look like native function\n    Zone.__load_patch('toString', (global) => {\n        // patch Func.prototype.toString to let them look like native\n        const originalFunctionToString = Function.prototype.toString;\n        const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n        const PROMISE_SYMBOL = zoneSymbol('Promise');\n        const ERROR_SYMBOL = zoneSymbol('Error');\n        const newFunctionToString = function toString() {\n            if (typeof this === 'function') {\n                const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n                if (originalDelegate) {\n                    if (typeof originalDelegate === 'function') {\n                        return originalFunctionToString.call(originalDelegate);\n                    }\n                    else {\n                        return Object.prototype.toString.call(originalDelegate);\n                    }\n                }\n                if (this === Promise) {\n                    const nativePromise = global[PROMISE_SYMBOL];\n                    if (nativePromise) {\n                        return originalFunctionToString.call(nativePromise);\n                    }\n                }\n                if (this === Error) {\n                    const nativeError = global[ERROR_SYMBOL];\n                    if (nativeError) {\n                        return originalFunctionToString.call(nativeError);\n                    }\n                }\n            }\n            return originalFunctionToString.call(this);\n        };\n        newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n        Function.prototype.toString = newFunctionToString;\n        // patch Object.prototype.toString to let them look like native\n        const originalObjectToString = Object.prototype.toString;\n        const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n        Object.prototype.toString = function () {\n            if (typeof Promise === 'function' && this instanceof Promise) {\n                return PROMISE_OBJECT_TO_STRING;\n            }\n            return originalObjectToString.call(this);\n        };\n    });\n}\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    const symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    const nativeDelegate = (target[symbol] = target[method]);\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                const source = `${targetName}.${method}::` + callback;\n                const prototype = opts.prototype;\n                // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                // make those properties non-writable. This means that patching callback will throw an error\n                // `cannot assign to read-only property`. See this code as an example:\n                // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                // We don't want to stop the application rendering if we couldn't patch some\n                // callback, e.g. `attributeChangedCallback`.\n                try {\n                    if (prototype.hasOwnProperty(callback)) {\n                        const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                        if (descriptor && descriptor.value) {\n                            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                            api._redefineProperty(opts.prototype, callback, descriptor);\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                catch {\n                    // Note: we leave the catch block empty since there's no way to handle the error related\n                    // to non-writable property.\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\nfunction patchUtil(Zone) {\n    Zone.__load_patch('util', (global, Zone, api) => {\n        // Collect native event names by looking at properties\n        // on the global namespace, e.g. 'onclick'.\n        const eventNames = getOnEventNames(global);\n        api.patchOnProperties = patchOnProperties;\n        api.patchMethod = patchMethod;\n        api.bindArguments = bindArguments;\n        api.patchMacroTask = patchMacroTask;\n        // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS`\n        // to define which events will not be patched by `Zone.js`. In newer version (>=0.9.0), we\n        // change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep the name consistent with\n        // angular repo. The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be\n        // supported for backwards compatibility.\n        const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n        const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n        if (global[SYMBOL_UNPATCHED_EVENTS]) {\n            global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n        }\n        if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n            Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n                global[SYMBOL_BLACK_LISTED_EVENTS];\n        }\n        api.patchEventPrototype = patchEventPrototype;\n        api.patchEventTarget = patchEventTarget;\n        api.isIEOrEdge = isIEOrEdge;\n        api.ObjectDefineProperty = ObjectDefineProperty;\n        api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n        api.ObjectCreate = ObjectCreate;\n        api.ArraySlice = ArraySlice;\n        api.patchClass = patchClass;\n        api.wrapWithCurrentZone = wrapWithCurrentZone;\n        api.filterProperties = filterProperties;\n        api.attachOriginToPatched = attachOriginToPatched;\n        api._redefineProperty = Object.defineProperty;\n        api.patchCallbacks = patchCallbacks;\n        api.getGlobalObjects = () => ({\n            globalSources,\n            zoneSymbolEventNames,\n            eventNames,\n            isBrowser,\n            isMix,\n            isNode,\n            TRUE_STR,\n            FALSE_STR,\n            ZONE_SYMBOL_PREFIX,\n            ADD_EVENT_LISTENER_STR,\n            REMOVE_EVENT_LISTENER_STR,\n        });\n    });\n}\n\nfunction patchCommon(Zone) {\n    patchPromise(Zone);\n    patchToString(Zone);\n    patchUtil(Zone);\n}\n\nconst Zone$1 = loadZone();\npatchCommon(Zone$1);\npatchBrowser(Zone$1);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;;;AACA,MAAMA,MAAM,GAAGC,UAAf,C,CACA;AACA;;AACA,SAASC,UAAT,CAAoBC,IAApB,EAA0B;EACtB,MAAMC,YAAY,GAAGJ,MAAM,CAAC,sBAAD,CAAN,IAAkC,iBAAvD;EACA,OAAOI,YAAY,GAAGD,IAAtB;AACH;;AACD,SAASE,QAAT,GAAoB;EAChB,MAAMC,WAAW,GAAGN,MAAM,CAAC,aAAD,CAA1B;;EACA,SAASO,IAAT,CAAcJ,IAAd,EAAoB;IAChBG,WAAW,IAAIA,WAAW,CAAC,MAAD,CAA1B,IAAsCA,WAAW,CAAC,MAAD,CAAX,CAAoBH,IAApB,CAAtC;EACH;;EACD,SAASK,kBAAT,CAA4BL,IAA5B,EAAkCM,KAAlC,EAAyC;IACrCH,WAAW,IAAIA,WAAW,CAAC,SAAD,CAA1B,IAAyCA,WAAW,CAAC,SAAD,CAAX,CAAuBH,IAAvB,EAA6BM,KAA7B,CAAzC;EACH;;EACDF,IAAI,CAAC,MAAD,CAAJ;;EACA,MAAMG,QAAN,CAAe;IAEa,OAAjBC,iBAAiB,GAAG;MACvB,IAAIX,MAAM,CAAC,SAAD,CAAN,KAAsBY,OAAO,CAAC,kBAAD,CAAjC,EAAuD;QACnD,MAAM,IAAIC,KAAJ,CAAU,0EACZ,yBADY,GAEZ,+DAFY,GAGZ,kFAHY,GAIZ,sDAJE,CAAN;MAKH;IACJ;;IACc,WAAJC,IAAI,GAAG;MACd,IAAIC,IAAI,GAAGL,QAAQ,CAACM,OAApB;;MACA,OAAOD,IAAI,CAACE,MAAZ,EAAoB;QAChBF,IAAI,GAAGA,IAAI,CAACE,MAAZ;MACH;;MACD,OAAOF,IAAP;IACH;;IACiB,WAAPC,OAAO,GAAG;MACjB,OAAOE,iBAAiB,CAACH,IAAzB;IACH;;IACqB,WAAXI,WAAW,GAAG;MACrB,OAAOC,YAAP;IACH;;IACkB,OAAZC,YAAY,CAAClB,IAAD,EAAOmB,EAAP,EAAWC,eAAe,GAAG,KAA7B,EAAoC;MACnD,IAAIX,OAAO,CAACY,cAAR,CAAuBrB,IAAvB,CAAJ,EAAkC;QAC9B;QACA;QACA;QACA,MAAMsB,cAAc,GAAGzB,MAAM,CAACE,UAAU,CAAC,yBAAD,CAAX,CAAN,KAAkD,IAAzE;;QACA,IAAI,CAACqB,eAAD,IAAoBE,cAAxB,EAAwC;UACpC,MAAMZ,KAAK,CAAC,2BAA2BV,IAA5B,CAAX;QACH;MACJ,CARD,MASK,IAAI,CAACH,MAAM,CAAC,oBAAoBG,IAArB,CAAX,EAAuC;QACxC,MAAMuB,QAAQ,GAAG,UAAUvB,IAA3B;QACAI,IAAI,CAACmB,QAAD,CAAJ;QACAd,OAAO,CAACT,IAAD,CAAP,GAAgBmB,EAAE,CAACtB,MAAD,EAASU,QAAT,EAAmBiB,IAAnB,CAAlB;QACAnB,kBAAkB,CAACkB,QAAD,EAAWA,QAAX,CAAlB;MACH;IACJ;;IACS,IAANT,MAAM,GAAG;MACT,OAAO,KAAKW,OAAZ;IACH;;IACO,IAAJzB,IAAI,GAAG;MACP,OAAO,KAAK0B,KAAZ;IACH;;IAKDC,WAAW,CAACb,MAAD,EAASc,QAAT,EAAmB;MAAA;;MAAA;;MAAA;;MAAA;;MAC1B,KAAKH,OAAL,GAAeX,MAAf;MACA,KAAKY,KAAL,GAAaE,QAAQ,GAAGA,QAAQ,CAAC5B,IAAT,IAAiB,SAApB,GAAgC,QAArD;MACA,KAAK6B,WAAL,GAAoBD,QAAQ,IAAIA,QAAQ,CAACE,UAAtB,IAAqC,EAAxD;MACA,KAAKC,aAAL,GAAqB,IAAIC,aAAJ,CAAkB,IAAlB,EAAwB,KAAKP,OAAL,IAAgB,KAAKA,OAAL,CAAaM,aAArD,EAAoEH,QAApE,CAArB;IACH;;IACDK,GAAG,CAACC,GAAD,EAAM;MACL,MAAMtB,IAAI,GAAG,KAAKuB,WAAL,CAAiBD,GAAjB,CAAb;MACA,IAAItB,IAAJ,EACI,OAAOA,IAAI,CAACiB,WAAL,CAAiBK,GAAjB,CAAP;IACP;;IACDC,WAAW,CAACD,GAAD,EAAM;MACb,IAAIrB,OAAO,GAAG,IAAd;;MACA,OAAOA,OAAP,EAAgB;QACZ,IAAIA,OAAO,CAACgB,WAAR,CAAoBR,cAApB,CAAmCa,GAAnC,CAAJ,EAA6C;UACzC,OAAOrB,OAAP;QACH;;QACDA,OAAO,GAAGA,OAAO,CAACY,OAAlB;MACH;;MACD,OAAO,IAAP;IACH;;IACDW,IAAI,CAACR,QAAD,EAAW;MACX,IAAI,CAACA,QAAL,EACI,MAAM,IAAIlB,KAAJ,CAAU,oBAAV,CAAN;MACJ,OAAO,KAAKqB,aAAL,CAAmBK,IAAnB,CAAwB,IAAxB,EAA8BR,QAA9B,CAAP;IACH;;IACDS,IAAI,CAACC,QAAD,EAAWC,MAAX,EAAmB;MACnB,IAAI,OAAOD,QAAP,KAAoB,UAAxB,EAAoC;QAChC,MAAM,IAAI5B,KAAJ,CAAU,6BAA6B4B,QAAvC,CAAN;MACH;;MACD,MAAME,SAAS,GAAG,KAAKT,aAAL,CAAmBU,SAAnB,CAA6B,IAA7B,EAAmCH,QAAnC,EAA6CC,MAA7C,CAAlB;;MACA,MAAM3B,IAAI,GAAG,IAAb;MACA,OAAO,YAAY;QACf,OAAOA,IAAI,CAAC8B,UAAL,CAAgBF,SAAhB,EAA2B,IAA3B,EAAiCG,SAAjC,EAA4CJ,MAA5C,CAAP;MACH,CAFD;IAGH;;IACDK,GAAG,CAACN,QAAD,EAAWO,SAAX,EAAsBC,SAAtB,EAAiCP,MAAjC,EAAyC;MACxCxB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAV;QAA6BH,IAAI,EAAE;MAAnC,CAApB;;MACA,IAAI;QACA,OAAO,KAAKmB,aAAL,CAAmBgB,MAAnB,CAA0B,IAA1B,EAAgCT,QAAhC,EAA0CO,SAA1C,EAAqDC,SAArD,EAAgEP,MAAhE,CAAP;MACH,CAFD,SAGQ;QACJxB,iBAAiB,GAAGA,iBAAiB,CAACD,MAAtC;MACH;IACJ;;IACD4B,UAAU,CAACJ,QAAD,EAAWO,SAAS,GAAG,IAAvB,EAA6BC,SAA7B,EAAwCP,MAAxC,EAAgD;MACtDxB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAV;QAA6BH,IAAI,EAAE;MAAnC,CAApB;;MACA,IAAI;QACA,IAAI;UACA,OAAO,KAAKmB,aAAL,CAAmBgB,MAAnB,CAA0B,IAA1B,EAAgCT,QAAhC,EAA0CO,SAA1C,EAAqDC,SAArD,EAAgEP,MAAhE,CAAP;QACH,CAFD,CAGA,OAAOS,KAAP,EAAc;UACV,IAAI,KAAKjB,aAAL,CAAmBkB,WAAnB,CAA+B,IAA/B,EAAqCD,KAArC,CAAJ,EAAiD;YAC7C,MAAMA,KAAN;UACH;QACJ;MACJ,CATD,SAUQ;QACJjC,iBAAiB,GAAGA,iBAAiB,CAACD,MAAtC;MACH;IACJ;;IACDoC,OAAO,CAACC,IAAD,EAAON,SAAP,EAAkBC,SAAlB,EAA6B;MAChC,IAAIK,IAAI,CAACvC,IAAL,IAAa,IAAjB,EAAuB;QACnB,MAAM,IAAIF,KAAJ,CAAU,gEACZ,CAACyC,IAAI,CAACvC,IAAL,IAAawC,OAAd,EAAuBpD,IADX,GAEZ,eAFY,GAGZ,KAAKA,IAHO,GAIZ,GAJE,CAAN;MAKH;;MACD,MAAMqD,QAAQ,GAAGF,IAAjB,CARgC,CAShC;MACA;MACA;;MACA,MAAM;QAAEG,IAAF;QAAQC,IAAI,EAAE;UAAEC,UAAU,GAAG,KAAf;UAAsBC,aAAa,GAAG;QAAtC,IAAgD;MAA9D,IAAqEN,IAA3E;;MACA,IAAIA,IAAI,CAACO,KAAL,KAAeC,YAAf,KAAgCL,IAAI,KAAKM,SAAT,IAAsBN,IAAI,KAAKO,SAA/D,CAAJ,EAA+E;QAC3E;MACH;;MACD,MAAMC,YAAY,GAAGX,IAAI,CAACO,KAAL,IAAcK,OAAnC;MACAD,YAAY,IAAIT,QAAQ,CAACW,aAAT,CAAuBD,OAAvB,EAAgCE,SAAhC,CAAhB;MACA,MAAMC,YAAY,GAAGjD,YAArB;MACAA,YAAY,GAAGoC,QAAf;MACAtC,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAV;QAA6BH,IAAI,EAAE;MAAnC,CAApB;;MACA,IAAI;QACA,IAAI0C,IAAI,IAAIO,SAAR,IAAqBV,IAAI,CAACI,IAA1B,IAAkC,CAACC,UAAnC,IAAiD,CAACC,aAAtD,EAAqE;UACjEN,IAAI,CAACgB,QAAL,GAAgBC,SAAhB;QACH;;QACD,IAAI;UACA,OAAO,KAAKrC,aAAL,CAAmBsC,UAAnB,CAA8B,IAA9B,EAAoChB,QAApC,EAA8CR,SAA9C,EAAyDC,SAAzD,CAAP;QACH,CAFD,CAGA,OAAOE,KAAP,EAAc;UACV,IAAI,KAAKjB,aAAL,CAAmBkB,WAAnB,CAA+B,IAA/B,EAAqCD,KAArC,CAAJ,EAAiD;YAC7C,MAAMA,KAAN;UACH;QACJ;MACJ,CAZD,SAaQ;QACJ;QACA;QACA,MAAMU,KAAK,GAAGP,IAAI,CAACO,KAAnB;;QACA,IAAIA,KAAK,KAAKC,YAAV,IAA0BD,KAAK,KAAKY,OAAxC,EAAiD;UAC7C,IAAIhB,IAAI,IAAIM,SAAR,IAAqBJ,UAArB,IAAoCC,aAAa,IAAIC,KAAK,KAAKa,UAAnE,EAAgF;YAC5ET,YAAY,IAAIT,QAAQ,CAACW,aAAT,CAAuBC,SAAvB,EAAkCF,OAAlC,EAA2CQ,UAA3C,CAAhB;UACH,CAFD,MAGK;YACD,MAAMC,aAAa,GAAGnB,QAAQ,CAACoB,cAA/B;;YACA,KAAKC,gBAAL,CAAsBrB,QAAtB,EAAgC,CAAC,CAAjC;;YACAS,YAAY,IAAIT,QAAQ,CAACW,aAAT,CAAuBL,YAAvB,EAAqCI,OAArC,EAA8CJ,YAA9C,CAAhB;;YACA,IAAIF,aAAJ,EAAmB;cACfJ,QAAQ,CAACoB,cAAT,GAA0BD,aAA1B;YACH;UACJ;QACJ;;QACDzD,iBAAiB,GAAGA,iBAAiB,CAACD,MAAtC;QACAG,YAAY,GAAGiD,YAAf;MACH;IACJ;;IACDS,YAAY,CAACxB,IAAD,EAAO;MACf,IAAIA,IAAI,CAACvC,IAAL,IAAauC,IAAI,CAACvC,IAAL,KAAc,IAA/B,EAAqC;QACjC;QACA;QACA,IAAIgE,OAAO,GAAG,IAAd;;QACA,OAAOA,OAAP,EAAgB;UACZ,IAAIA,OAAO,KAAKzB,IAAI,CAACvC,IAArB,EAA2B;YACvB,MAAMF,KAAK,CAAE,8BAA6B,KAAKV,IAAK,8CAA6CmD,IAAI,CAACvC,IAAL,CAAUZ,IAAK,EAArG,CAAX;UACH;;UACD4E,OAAO,GAAGA,OAAO,CAAC9D,MAAlB;QACH;MACJ;;MACDqC,IAAI,CAACa,aAAL,CAAmBO,UAAnB,EAA+BZ,YAA/B;;MACA,MAAMa,aAAa,GAAG,EAAtB;MACArB,IAAI,CAACsB,cAAL,GAAsBD,aAAtB;MACArB,IAAI,CAAC0B,KAAL,GAAa,IAAb;;MACA,IAAI;QACA1B,IAAI,GAAG,KAAKpB,aAAL,CAAmB4C,YAAnB,CAAgC,IAAhC,EAAsCxB,IAAtC,CAAP;MACH,CAFD,CAGA,OAAO2B,GAAP,EAAY;QACR;QACA;QACA3B,IAAI,CAACa,aAAL,CAAmBM,OAAnB,EAA4BC,UAA5B,EAAwCZ,YAAxC,EAHQ,CAIR;;;QACA,KAAK5B,aAAL,CAAmBkB,WAAnB,CAA+B,IAA/B,EAAqC6B,GAArC;;QACA,MAAMA,GAAN;MACH;;MACD,IAAI3B,IAAI,CAACsB,cAAL,KAAwBD,aAA5B,EAA2C;QACvC;QACA,KAAKE,gBAAL,CAAsBvB,IAAtB,EAA4B,CAA5B;MACH;;MACD,IAAIA,IAAI,CAACO,KAAL,IAAca,UAAlB,EAA8B;QAC1BpB,IAAI,CAACa,aAAL,CAAmBC,SAAnB,EAA8BM,UAA9B;MACH;;MACD,OAAOpB,IAAP;IACH;;IACD4B,iBAAiB,CAACxC,MAAD,EAASD,QAAT,EAAmBiB,IAAnB,EAAyByB,cAAzB,EAAyC;MACtD,OAAO,KAAKL,YAAL,CAAkB,IAAIM,QAAJ,CAAaC,SAAb,EAAwB3C,MAAxB,EAAgCD,QAAhC,EAA0CiB,IAA1C,EAAgDyB,cAAhD,EAAgEZ,SAAhE,CAAlB,CAAP;IACH;;IACDe,iBAAiB,CAAC5C,MAAD,EAASD,QAAT,EAAmBiB,IAAnB,EAAyByB,cAAzB,EAAyCI,YAAzC,EAAuD;MACpE,OAAO,KAAKT,YAAL,CAAkB,IAAIM,QAAJ,CAAapB,SAAb,EAAwBtB,MAAxB,EAAgCD,QAAhC,EAA0CiB,IAA1C,EAAgDyB,cAAhD,EAAgEI,YAAhE,CAAlB,CAAP;IACH;;IACDC,iBAAiB,CAAC9C,MAAD,EAASD,QAAT,EAAmBiB,IAAnB,EAAyByB,cAAzB,EAAyCI,YAAzC,EAAuD;MACpE,OAAO,KAAKT,YAAL,CAAkB,IAAIM,QAAJ,CAAarB,SAAb,EAAwBrB,MAAxB,EAAgCD,QAAhC,EAA0CiB,IAA1C,EAAgDyB,cAAhD,EAAgEI,YAAhE,CAAlB,CAAP;IACH;;IACDE,UAAU,CAACnC,IAAD,EAAO;MACb,IAAIA,IAAI,CAACvC,IAAL,IAAa,IAAjB,EACI,MAAM,IAAIF,KAAJ,CAAU,sEACZ,CAACyC,IAAI,CAACvC,IAAL,IAAawC,OAAd,EAAuBpD,IADX,GAEZ,eAFY,GAGZ,KAAKA,IAHO,GAIZ,GAJE,CAAN;;MAKJ,IAAImD,IAAI,CAACO,KAAL,KAAeO,SAAf,IAA4Bd,IAAI,CAACO,KAAL,KAAeK,OAA/C,EAAwD;QACpD;MACH;;MACDZ,IAAI,CAACa,aAAL,CAAmBuB,SAAnB,EAA8BtB,SAA9B,EAAyCF,OAAzC;;MACA,IAAI;QACA,KAAKhC,aAAL,CAAmBuD,UAAnB,CAA8B,IAA9B,EAAoCnC,IAApC;MACH,CAFD,CAGA,OAAO2B,GAAP,EAAY;QACR;QACA3B,IAAI,CAACa,aAAL,CAAmBM,OAAnB,EAA4BiB,SAA5B;;QACA,KAAKxD,aAAL,CAAmBkB,WAAnB,CAA+B,IAA/B,EAAqC6B,GAArC;;QACA,MAAMA,GAAN;MACH;;MACD,KAAKJ,gBAAL,CAAsBvB,IAAtB,EAA4B,CAAC,CAA7B;;MACAA,IAAI,CAACa,aAAL,CAAmBL,YAAnB,EAAiC4B,SAAjC;;MACApC,IAAI,CAACqC,QAAL,GAAgB,CAAC,CAAjB;MACA,OAAOrC,IAAP;IACH;;IACDuB,gBAAgB,CAACvB,IAAD,EAAOsC,KAAP,EAAc;MAC1B,MAAMjB,aAAa,GAAGrB,IAAI,CAACsB,cAA3B;;MACA,IAAIgB,KAAK,IAAI,CAAC,CAAd,EAAiB;QACbtC,IAAI,CAACsB,cAAL,GAAsB,IAAtB;MACH;;MACD,KAAK,IAAIiB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,aAAa,CAACmB,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;QAC3ClB,aAAa,CAACkB,CAAD,CAAb,CAAiBhB,gBAAjB,CAAkCvB,IAAI,CAACG,IAAvC,EAA6CmC,KAA7C;MACH;IACJ;;EArPU;;EATC,gBASVlF,QATU,gBAUQR,UAVR;;EAgQhB,MAAM6F,WAAW,GAAG;IAChB5F,IAAI,EAAE,EADU;IAEhB6F,SAAS,EAAE,CAACC,QAAD,EAAWC,CAAX,EAAcC,MAAd,EAAsBC,YAAtB,KAAuCH,QAAQ,CAACI,OAAT,CAAiBF,MAAjB,EAAyBC,YAAzB,CAFlC;IAGhBE,cAAc,EAAE,CAACL,QAAD,EAAWC,CAAX,EAAcC,MAAd,EAAsB7C,IAAtB,KAA+B2C,QAAQ,CAACnB,YAAT,CAAsBqB,MAAtB,EAA8B7C,IAA9B,CAH/B;IAIhBiD,YAAY,EAAE,CAACN,QAAD,EAAWC,CAAX,EAAcC,MAAd,EAAsB7C,IAAtB,EAA4BN,SAA5B,EAAuCC,SAAvC,KAAqDgD,QAAQ,CAACzB,UAAT,CAAoB2B,MAApB,EAA4B7C,IAA5B,EAAkCN,SAAlC,EAA6CC,SAA7C,CAJnD;IAKhBuD,YAAY,EAAE,CAACP,QAAD,EAAWC,CAAX,EAAcC,MAAd,EAAsB7C,IAAtB,KAA+B2C,QAAQ,CAACR,UAAT,CAAoBU,MAApB,EAA4B7C,IAA5B;EAL7B,CAApB;;EAOA,MAAMnB,aAAN,CAAoB;IACR,IAAJpB,IAAI,GAAG;MACP,OAAO,KAAKiE,KAAZ;IACH;;IAiCDlD,WAAW,CAACf,IAAD,EAAO0F,cAAP,EAAuB1E,QAAvB,EAAiC;MAAA;;MAAA,qCA/B9B;QACV,aAAa,CADH;QAEV,aAAa,CAFH;QAGV,aAAa;MAHH,CA+B8B;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MACxC,KAAKiD,KAAL,GAAajE,IAAb;MACA,KAAK2F,eAAL,GAAuBD,cAAvB;MACA,KAAKE,OAAL,GAAe5E,QAAQ,KAAKA,QAAQ,IAAIA,QAAQ,CAAC6E,MAArB,GAA8B7E,QAA9B,GAAyC0E,cAAc,CAACE,OAA7D,CAAvB;MACA,KAAKE,SAAL,GAAiB9E,QAAQ,KAAKA,QAAQ,CAAC6E,MAAT,GAAkBH,cAAlB,GAAmCA,cAAc,CAACI,SAAvD,CAAzB;MACA,KAAKC,aAAL,GACI/E,QAAQ,KAAKA,QAAQ,CAAC6E,MAAT,GAAkB,KAAK5B,KAAvB,GAA+ByB,cAAc,CAACK,aAAnD,CADZ;MAEA,KAAKC,YAAL,GACIhF,QAAQ,KAAKA,QAAQ,CAACiF,WAAT,GAAuBjF,QAAvB,GAAkC0E,cAAc,CAACM,YAAtD,CADZ;MAEA,KAAKE,cAAL,GACIlF,QAAQ,KAAKA,QAAQ,CAACiF,WAAT,GAAuBP,cAAvB,GAAwCA,cAAc,CAACQ,cAA5D,CADZ;MAEA,KAAKC,kBAAL,GACInF,QAAQ,KAAKA,QAAQ,CAACiF,WAAT,GAAuB,KAAKhC,KAA5B,GAAoCyB,cAAc,CAACS,kBAAxD,CADZ;MAEA,KAAKC,SAAL,GAAiBpF,QAAQ,KAAKA,QAAQ,CAACqF,QAAT,GAAoBrF,QAApB,GAA+B0E,cAAc,CAACU,SAAnD,CAAzB;MACA,KAAKE,WAAL,GACItF,QAAQ,KAAKA,QAAQ,CAACqF,QAAT,GAAoBX,cAApB,GAAqCA,cAAc,CAACY,WAAzD,CADZ;MAEA,KAAKC,eAAL,GACIvF,QAAQ,KAAKA,QAAQ,CAACqF,QAAT,GAAoB,KAAKpC,KAAzB,GAAiCyB,cAAc,CAACa,eAArD,CADZ;MAEA,KAAKC,cAAL,GACIxF,QAAQ,KAAKA,QAAQ,CAACyF,aAAT,GAAyBzF,QAAzB,GAAoC0E,cAAc,CAACc,cAAxD,CADZ;MAEA,KAAKE,gBAAL,GACI1F,QAAQ,KAAKA,QAAQ,CAACyF,aAAT,GAAyBf,cAAzB,GAA0CA,cAAc,CAACgB,gBAA9D,CADZ;MAEA,KAAKC,oBAAL,GACI3F,QAAQ,KAAKA,QAAQ,CAACyF,aAAT,GAAyB,KAAKxC,KAA9B,GAAsCyB,cAAc,CAACiB,oBAA1D,CADZ;MAEA,KAAKC,eAAL,GACI5F,QAAQ,KAAKA,QAAQ,CAACuE,cAAT,GAA0BvE,QAA1B,GAAqC0E,cAAc,CAACkB,eAAzD,CADZ;MAEA,KAAKC,iBAAL,GACI7F,QAAQ,KAAKA,QAAQ,CAACuE,cAAT,GAA0BG,cAA1B,GAA2CA,cAAc,CAACmB,iBAA/D,CADZ;MAEA,KAAKC,qBAAL,GACI9F,QAAQ,KAAKA,QAAQ,CAACuE,cAAT,GAA0B,KAAKtB,KAA/B,GAAuCyB,cAAc,CAACoB,qBAA3D,CADZ;MAEA,KAAKC,aAAL,GACI/F,QAAQ,KAAKA,QAAQ,CAACwE,YAAT,GAAwBxE,QAAxB,GAAmC0E,cAAc,CAACqB,aAAvD,CADZ;MAEA,KAAKC,eAAL,GACIhG,QAAQ,KAAKA,QAAQ,CAACwE,YAAT,GAAwBE,cAAxB,GAAyCA,cAAc,CAACsB,eAA7D,CADZ;MAEA,KAAKC,mBAAL,GACIjG,QAAQ,KAAKA,QAAQ,CAACwE,YAAT,GAAwB,KAAKvB,KAA7B,GAAqCyB,cAAc,CAACuB,mBAAzD,CADZ;MAEA,KAAKC,aAAL,GACIlG,QAAQ,KAAKA,QAAQ,CAACyE,YAAT,GAAwBzE,QAAxB,GAAmC0E,cAAc,CAACwB,aAAvD,CADZ;MAEA,KAAKC,eAAL,GACInG,QAAQ,KAAKA,QAAQ,CAACyE,YAAT,GAAwBC,cAAxB,GAAyCA,cAAc,CAACyB,eAA7D,CADZ;MAEA,KAAKC,mBAAL,GACIpG,QAAQ,KAAKA,QAAQ,CAACyE,YAAT,GAAwB,KAAKxB,KAA7B,GAAqCyB,cAAc,CAAC0B,mBAAzD,CADZ;MAEA,KAAKC,UAAL,GAAkB,IAAlB;MACA,KAAKC,YAAL,GAAoB,IAApB;MACA,KAAKC,iBAAL,GAAyB,IAAzB;MACA,KAAKC,gBAAL,GAAwB,IAAxB;MACA,MAAMC,eAAe,GAAGzG,QAAQ,IAAIA,QAAQ,CAACiE,SAA7C;MACA,MAAMyC,aAAa,GAAGhC,cAAc,IAAIA,cAAc,CAAC2B,UAAvD;;MACA,IAAII,eAAe,IAAIC,aAAvB,EAAsC;QAClC;QACA;QACA,KAAKL,UAAL,GAAkBI,eAAe,GAAGzG,QAAH,GAAcgE,WAA/C;QACA,KAAKsC,YAAL,GAAoB5B,cAApB;QACA,KAAK6B,iBAAL,GAAyB,IAAzB;QACA,KAAKC,gBAAL,GAAwB,KAAKvD,KAA7B;;QACA,IAAI,CAACjD,QAAQ,CAACuE,cAAd,EAA8B;UAC1B,KAAKqB,eAAL,GAAuB5B,WAAvB;UACA,KAAK6B,iBAAL,GAAyBnB,cAAzB;UACA,KAAKoB,qBAAL,GAA6B,KAAK7C,KAAlC;QACH;;QACD,IAAI,CAACjD,QAAQ,CAACwE,YAAd,EAA4B;UACxB,KAAKuB,aAAL,GAAqB/B,WAArB;UACA,KAAKgC,eAAL,GAAuBtB,cAAvB;UACA,KAAKuB,mBAAL,GAA2B,KAAKhD,KAAhC;QACH;;QACD,IAAI,CAACjD,QAAQ,CAACyE,YAAd,EAA4B;UACxB,KAAKyB,aAAL,GAAqBlC,WAArB;UACA,KAAKmC,eAAL,GAAuBzB,cAAvB;UACA,KAAK0B,mBAAL,GAA2B,KAAKnD,KAAhC;QACH;MACJ;IACJ;;IACDzC,IAAI,CAACmG,UAAD,EAAa3G,QAAb,EAAuB;MACvB,OAAO,KAAK4E,OAAL,GACD,KAAKA,OAAL,CAAaC,MAAb,CAAoB,KAAKC,SAAzB,EAAoC,KAAK9F,IAAzC,EAA+C2H,UAA/C,EAA2D3G,QAA3D,CADC,GAED,IAAIrB,QAAJ,CAAagI,UAAb,EAAyB3G,QAAzB,CAFN;IAGH;;IACDa,SAAS,CAAC8F,UAAD,EAAajG,QAAb,EAAuBC,MAAvB,EAA+B;MACpC,OAAO,KAAKqE,YAAL,GACD,KAAKA,YAAL,CAAkBC,WAAlB,CAA8B,KAAKC,cAAnC,EAAmD,KAAKC,kBAAxD,EAA4EwB,UAA5E,EAAwFjG,QAAxF,EAAkGC,MAAlG,CADC,GAEDD,QAFN;IAGH;;IACDS,MAAM,CAACwF,UAAD,EAAajG,QAAb,EAAuBO,SAAvB,EAAkCC,SAAlC,EAA6CP,MAA7C,EAAqD;MACvD,OAAO,KAAKyE,SAAL,GACD,KAAKA,SAAL,CAAeC,QAAf,CAAwB,KAAKC,WAA7B,EAA0C,KAAKC,eAA/C,EAAgEoB,UAAhE,EAA4EjG,QAA5E,EAAsFO,SAAtF,EAAiGC,SAAjG,EAA4GP,MAA5G,CADC,GAEDD,QAAQ,CAACkG,KAAT,CAAe3F,SAAf,EAA0BC,SAA1B,CAFN;IAGH;;IACDG,WAAW,CAACsF,UAAD,EAAavF,KAAb,EAAoB;MAC3B,OAAO,KAAKoE,cAAL,GACD,KAAKA,cAAL,CAAoBC,aAApB,CAAkC,KAAKC,gBAAvC,EAAyD,KAAKC,oBAA9D,EAAoFgB,UAApF,EAAgGvF,KAAhG,CADC,GAED,IAFN;IAGH;;IACD2B,YAAY,CAAC4D,UAAD,EAAapF,IAAb,EAAmB;MAC3B,IAAIsF,UAAU,GAAGtF,IAAjB;;MACA,IAAI,KAAKqE,eAAT,EAA0B;QACtB,IAAI,KAAKS,UAAT,EAAqB;UACjBQ,UAAU,CAAChE,cAAX,CAA0BiE,IAA1B,CAA+B,KAAKP,iBAApC;QACH;;QACDM,UAAU,GAAG,KAAKjB,eAAL,CAAqBrB,cAArB,CAAoC,KAAKsB,iBAAzC,EAA4D,KAAKC,qBAAjE,EAAwFa,UAAxF,EAAoGpF,IAApG,CAAb;QACA,IAAI,CAACsF,UAAL,EACIA,UAAU,GAAGtF,IAAb;MACP,CAPD,MAQK;QACD,IAAIA,IAAI,CAACwF,UAAT,EAAqB;UACjBxF,IAAI,CAACwF,UAAL,CAAgBxF,IAAhB;QACH,CAFD,MAGK,IAAIA,IAAI,CAACG,IAAL,IAAa4B,SAAjB,EAA4B;UAC7BH,iBAAiB,CAAC5B,IAAD,CAAjB;QACH,CAFI,MAGA;UACD,MAAM,IAAIzC,KAAJ,CAAU,6BAAV,CAAN;QACH;MACJ;;MACD,OAAO+H,UAAP;IACH;;IACDpE,UAAU,CAACkE,UAAD,EAAapF,IAAb,EAAmBN,SAAnB,EAA8BC,SAA9B,EAAyC;MAC/C,OAAO,KAAK6E,aAAL,GACD,KAAKA,aAAL,CAAmBvB,YAAnB,CAAgC,KAAKwB,eAArC,EAAsD,KAAKC,mBAA3D,EAAgFU,UAAhF,EAA4FpF,IAA5F,EAAkGN,SAAlG,EAA6GC,SAA7G,CADC,GAEDK,IAAI,CAACb,QAAL,CAAckG,KAAd,CAAoB3F,SAApB,EAA+BC,SAA/B,CAFN;IAGH;;IACDwC,UAAU,CAACiD,UAAD,EAAapF,IAAb,EAAmB;MACzB,IAAIyF,KAAJ;;MACA,IAAI,KAAKd,aAAT,EAAwB;QACpBc,KAAK,GAAG,KAAKd,aAAL,CAAmBzB,YAAnB,CAAgC,KAAK0B,eAArC,EAAsD,KAAKC,mBAA3D,EAAgFO,UAAhF,EAA4FpF,IAA5F,CAAR;MACH,CAFD,MAGK;QACD,IAAI,CAACA,IAAI,CAACgB,QAAV,EAAoB;UAChB,MAAMzD,KAAK,CAAC,wBAAD,CAAX;QACH;;QACDkI,KAAK,GAAGzF,IAAI,CAACgB,QAAL,CAAchB,IAAd,CAAR;MACH;;MACD,OAAOyF,KAAP;IACH;;IACD1C,OAAO,CAACqC,UAAD,EAAaM,OAAb,EAAsB;MACzB;MACA;MACA,IAAI;QACA,KAAKZ,UAAL,IACI,KAAKA,UAAL,CAAgBpC,SAAhB,CAA0B,KAAKqC,YAA/B,EAA6C,KAAKE,gBAAlD,EAAoEG,UAApE,EAAgFM,OAAhF,CADJ;MAEH,CAHD,CAIA,OAAO/D,GAAP,EAAY;QACR,KAAK7B,WAAL,CAAiBsF,UAAjB,EAA6BzD,GAA7B;MACH;IACJ;;IACDJ,gBAAgB,CAACpB,IAAD,EAAOmC,KAAP,EAAc;MAC1B,MAAMqD,MAAM,GAAG,KAAKC,WAApB;MACA,MAAMC,IAAI,GAAGF,MAAM,CAACxF,IAAD,CAAnB;MACA,MAAM2F,IAAI,GAAIH,MAAM,CAACxF,IAAD,CAAN,GAAe0F,IAAI,GAAGvD,KAApC;;MACA,IAAIwD,IAAI,GAAG,CAAX,EAAc;QACV,MAAM,IAAIvI,KAAJ,CAAU,0CAAV,CAAN;MACH;;MACD,IAAIsI,IAAI,IAAI,CAAR,IAAaC,IAAI,IAAI,CAAzB,EAA4B;QACxB,MAAMJ,OAAO,GAAG;UACZ3D,SAAS,EAAE4D,MAAM,CAAC,WAAD,CAAN,GAAsB,CADrB;UAEZjF,SAAS,EAAEiF,MAAM,CAAC,WAAD,CAAN,GAAsB,CAFrB;UAGZlF,SAAS,EAAEkF,MAAM,CAAC,WAAD,CAAN,GAAsB,CAHrB;UAIZI,MAAM,EAAE5F;QAJI,CAAhB;QAMA,KAAK4C,OAAL,CAAa,KAAKrB,KAAlB,EAAyBgE,OAAzB;MACH;IACJ;;EApMe;;EAsMpB,MAAM5D,QAAN,CAAe;IAYXtD,WAAW,CAAC2B,IAAD,EAAOf,MAAP,EAAeD,QAAf,EAAyB6G,OAAzB,EAAkCR,UAAlC,EAA8CxE,QAA9C,EAAwD;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA;;MAAA,+BAJ3D,IAI2D;;MAAA,kCAHxD,CAGwD;;MAAA,wCAFlD,IAEkD;;MAAA,gCAD1D,cAC0D;;MAC/D,KAAKb,IAAL,GAAYA,IAAZ;MACA,KAAKf,MAAL,GAAcA,MAAd;MACA,KAAKgB,IAAL,GAAY4F,OAAZ;MACA,KAAKR,UAAL,GAAkBA,UAAlB;MACA,KAAKxE,QAAL,GAAgBA,QAAhB;;MACA,IAAI,CAAC7B,QAAL,EAAe;QACX,MAAM,IAAI5B,KAAJ,CAAU,yBAAV,CAAN;MACH;;MACD,KAAK4B,QAAL,GAAgBA,QAAhB;MACA,MAAM8G,IAAI,GAAG,IAAb,CAV+D,CAW/D;;MACA,IAAI9F,IAAI,KAAKM,SAAT,IAAsBuF,OAAtB,IAAiCA,OAAO,CAACE,IAA7C,EAAmD;QAC/C,KAAKtG,MAAL,GAAckC,QAAQ,CAACZ,UAAvB;MACH,CAFD,MAGK;QACD,KAAKtB,MAAL,GAAc,YAAY;UACtB,OAAOkC,QAAQ,CAACZ,UAAT,CAAoBiF,IAApB,CAAyBzJ,MAAzB,EAAiCuJ,IAAjC,EAAuC,IAAvC,EAA6CzG,SAA7C,CAAP;QACH,CAFD;MAGH;IACJ;;IACgB,OAAV0B,UAAU,CAAClB,IAAD,EAAO6C,MAAP,EAAeuD,IAAf,EAAqB;MAClC,IAAI,CAACpG,IAAL,EAAW;QACPA,IAAI,GAAG,IAAP;MACH;;MACDqG,yBAAyB;;MACzB,IAAI;QACArG,IAAI,CAACqC,QAAL;QACA,OAAOrC,IAAI,CAACvC,IAAL,CAAUsC,OAAV,CAAkBC,IAAlB,EAAwB6C,MAAxB,EAAgCuD,IAAhC,CAAP;MACH,CAHD,SAIQ;QACJ,IAAIC,yBAAyB,IAAI,CAAjC,EAAoC;UAChCC,mBAAmB;QACtB;;QACDD,yBAAyB;MAC5B;IACJ;;IACO,IAAJ5I,IAAI,GAAG;MACP,OAAO,KAAKiE,KAAZ;IACH;;IACQ,IAALnB,KAAK,GAAG;MACR,OAAO,KAAKgG,MAAZ;IACH;;IACDC,qBAAqB,GAAG;MACpB,KAAK3F,aAAL,CAAmBL,YAAnB,EAAiCY,UAAjC;IACH;;IACDP,aAAa,CAAC4F,OAAD,EAAUC,UAAV,EAAsBC,UAAtB,EAAkC;MAC3C,IAAI,KAAKJ,MAAL,KAAgBG,UAAhB,IAA8B,KAAKH,MAAL,KAAgBI,UAAlD,EAA8D;QAC1D,KAAKJ,MAAL,GAAcE,OAAd;;QACA,IAAIA,OAAO,IAAIjG,YAAf,EAA6B;UACzB,KAAKc,cAAL,GAAsB,IAAtB;QACH;MACJ,CALD,MAMK;QACD,MAAM,IAAI/D,KAAJ,CAAW,GAAE,KAAK4C,IAAK,KAAI,KAAKf,MAAO,6BAA4BqH,OAAQ,uBAAsBC,UAAW,IAAGC,UAAU,GAAG,UAAUA,UAAV,GAAuB,GAA1B,GAAgC,EAAG,UAAS,KAAKJ,MAAO,IAAjL,CAAN;MACH;IACJ;;IACDK,QAAQ,GAAG;MACP,IAAI,KAAKxG,IAAL,IAAa,OAAO,KAAKA,IAAL,CAAUyG,QAAjB,KAA8B,WAA/C,EAA4D;QACxD,OAAO,KAAKzG,IAAL,CAAUyG,QAAV,CAAmBD,QAAnB,EAAP;MACH,CAFD,MAGK;QACD,OAAOE,MAAM,CAACC,SAAP,CAAiBH,QAAjB,CAA0BT,IAA1B,CAA+B,IAA/B,CAAP;MACH;IACJ,CA5EU,CA6EX;IACA;;;IACAa,MAAM,GAAG;MACL,OAAO;QACH7G,IAAI,EAAE,KAAKA,IADR;QAEHI,KAAK,EAAE,KAAKA,KAFT;QAGHnB,MAAM,EAAE,KAAKA,MAHV;QAIH3B,IAAI,EAAE,KAAKA,IAAL,CAAUZ,IAJb;QAKHwF,QAAQ,EAAE,KAAKA;MALZ,CAAP;IAOH;;EAvFU,CA7cC,CAsiBhB;EACA;EACA;EACA;EACA;;;EACA,MAAM4E,gBAAgB,GAAGrK,UAAU,CAAC,YAAD,CAAnC;;EACA,MAAMsK,aAAa,GAAGtK,UAAU,CAAC,SAAD,CAAhC;;EACA,MAAMuK,UAAU,GAAGvK,UAAU,CAAC,MAAD,CAA7B;;EACA,IAAIwK,eAAe,GAAG,EAAtB;EACA,IAAIC,yBAAyB,GAAG,KAAhC;EACA,IAAIC,2BAAJ;;EACA,SAASC,uBAAT,CAAiCC,IAAjC,EAAuC;IACnC,IAAI,CAACF,2BAAL,EAAkC;MAC9B,IAAI5K,MAAM,CAACwK,aAAD,CAAV,EAA2B;QACvBI,2BAA2B,GAAG5K,MAAM,CAACwK,aAAD,CAAN,CAAsBO,OAAtB,CAA8B,CAA9B,CAA9B;MACH;IACJ;;IACD,IAAIH,2BAAJ,EAAiC;MAC7B,IAAII,UAAU,GAAGJ,2BAA2B,CAACH,UAAD,CAA5C;;MACA,IAAI,CAACO,UAAL,EAAiB;QACb;QACA;QACAA,UAAU,GAAGJ,2BAA2B,CAAC,MAAD,CAAxC;MACH;;MACDI,UAAU,CAACvB,IAAX,CAAgBmB,2BAAhB,EAA6CE,IAA7C;IACH,CARD,MASK;MACD9K,MAAM,CAACuK,gBAAD,CAAN,CAAyBO,IAAzB,EAA+B,CAA/B;IACH;EACJ;;EACD,SAAS5F,iBAAT,CAA2B5B,IAA3B,EAAiC;IAC7B;IACA;IACA,IAAIqG,yBAAyB,KAAK,CAA9B,IAAmCe,eAAe,CAAC5E,MAAhB,KAA2B,CAAlE,EAAqE;MACjE;MACA+E,uBAAuB,CAACjB,mBAAD,CAAvB;IACH;;IACDtG,IAAI,IAAIoH,eAAe,CAAC7B,IAAhB,CAAqBvF,IAArB,CAAR;EACH;;EACD,SAASsG,mBAAT,GAA+B;IAC3B,IAAI,CAACe,yBAAL,EAAgC;MAC5BA,yBAAyB,GAAG,IAA5B;;MACA,OAAOD,eAAe,CAAC5E,MAAvB,EAA+B;QAC3B,MAAMmF,KAAK,GAAGP,eAAd;QACAA,eAAe,GAAG,EAAlB;;QACA,KAAK,IAAI7E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoF,KAAK,CAACnF,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;UACnC,MAAMvC,IAAI,GAAG2H,KAAK,CAACpF,CAAD,CAAlB;;UACA,IAAI;YACAvC,IAAI,CAACvC,IAAL,CAAUsC,OAAV,CAAkBC,IAAlB,EAAwB,IAAxB,EAA8B,IAA9B;UACH,CAFD,CAGA,OAAOH,KAAP,EAAc;YACVxB,IAAI,CAACuJ,gBAAL,CAAsB/H,KAAtB;UACH;QACJ;MACJ;;MACDxB,IAAI,CAACwJ,kBAAL;;MACAR,yBAAyB,GAAG,KAA5B;IACH;EACJ,CAhmBe,CAimBhB;EACA;EACA;EACA;EACA;;;EACA,MAAMpH,OAAO,GAAG;IAAEpD,IAAI,EAAE;EAAR,CAAhB;EACA,MAAM2D,YAAY,GAAG,cAArB;EAAA,MAAqCY,UAAU,GAAG,YAAlD;EAAA,MAAgEN,SAAS,GAAG,WAA5E;EAAA,MAAyFF,OAAO,GAAG,SAAnG;EAAA,MAA8GwB,SAAS,GAAG,WAA1H;EAAA,MAAuIjB,OAAO,GAAG,SAAjJ;EACA,MAAMY,SAAS,GAAG,WAAlB;EAAA,MAA+BrB,SAAS,GAAG,WAA3C;EAAA,MAAwDD,SAAS,GAAG,WAApE;EACA,MAAMnD,OAAO,GAAG,EAAhB;EACA,MAAMe,IAAI,GAAG;IACTyJ,MAAM,EAAElL,UADC;IAETmL,gBAAgB,EAAE,MAAMnK,iBAFf;IAGTgK,gBAAgB,EAAEI,IAHT;IAITH,kBAAkB,EAAEG,IAJX;IAKTpG,iBAAiB,EAAEA,iBALV;IAMTqG,iBAAiB,EAAE,MAAM,CAAC7K,QAAQ,CAACR,UAAU,CAAC,iCAAD,CAAX,CANzB;IAOTsL,gBAAgB,EAAE,MAAM,EAPf;IAQTC,iBAAiB,EAAEH,IARV;IASTI,WAAW,EAAE,MAAMJ,IATV;IAUTK,aAAa,EAAE,MAAM,EAVZ;IAWTC,SAAS,EAAE,MAAMN,IAXR;IAYTO,cAAc,EAAE,MAAMP,IAZb;IAaTQ,mBAAmB,EAAE,MAAMR,IAblB;IAcTS,UAAU,EAAE,MAAM,KAdT;IAeTC,gBAAgB,EAAE,MAAMzH,SAff;IAgBT0H,oBAAoB,EAAE,MAAMX,IAhBnB;IAiBTY,8BAA8B,EAAE,MAAM3H,SAjB7B;IAkBT4H,YAAY,EAAE,MAAM5H,SAlBX;IAmBT6H,UAAU,EAAE,MAAM,EAnBT;IAoBTC,UAAU,EAAE,MAAMf,IApBT;IAqBTgB,mBAAmB,EAAE,MAAMhB,IArBlB;IAsBTiB,gBAAgB,EAAE,MAAM,EAtBf;IAuBTC,qBAAqB,EAAE,MAAMlB,IAvBpB;IAwBTmB,iBAAiB,EAAE,MAAMnB,IAxBhB;IAyBToB,cAAc,EAAE,MAAMpB,IAzBb;IA0BTT,uBAAuB,EAAEA;EA1BhB,CAAb;EA4BA,IAAI3J,iBAAiB,GAAG;IAAED,MAAM,EAAE,IAAV;IAAgBF,IAAI,EAAE,IAAIL,QAAJ,CAAa,IAAb,EAAmB,IAAnB;EAAtB,CAAxB;EACA,IAAIU,YAAY,GAAG,IAAnB;EACA,IAAIuI,yBAAyB,GAAG,CAAhC;;EACA,SAAS2B,IAAT,GAAgB,CAAG;;EACnB9K,kBAAkB,CAAC,MAAD,EAAS,MAAT,CAAlB;EACA,OAAOE,QAAP;AACH;;AAED,SAASiM,QAAT,GAAoB;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM3M,MAAM,GAAGC,UAAf;EACA,MAAMwB,cAAc,GAAGzB,MAAM,CAACE,UAAU,CAAC,yBAAD,CAAX,CAAN,KAAkD,IAAzE;;EACA,IAAIF,MAAM,CAAC,MAAD,CAAN,KAAmByB,cAAc,IAAI,OAAOzB,MAAM,CAAC,MAAD,CAAN,CAAeE,UAAtB,KAAqC,UAA1E,CAAJ,EAA2F;IACvF,MAAM,IAAIW,KAAJ,CAAU,sBAAV,CAAN;EACH,CAde,CAehB;;;EACAb,MAAM,CAAC,MAAD,CAAN,KAAmBK,QAAQ,EAA3B;EACA,OAAOL,MAAM,CAAC,MAAD,CAAb;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMkM,8BAA8B,GAAG9B,MAAM,CAACwC,wBAA9C;AACA;;AACA,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAApC;AACA;;AACA,MAAMC,oBAAoB,GAAG1C,MAAM,CAAC2C,cAApC;AACA;;AACA,MAAMZ,YAAY,GAAG/B,MAAM,CAAC4C,MAA5B;AACA;;AACA,MAAMZ,UAAU,GAAGa,KAAK,CAAC5C,SAAN,CAAgB6C,KAAnC;AACA;;AACA,MAAMC,sBAAsB,GAAG,kBAA/B;AACA;;AACA,MAAMC,yBAAyB,GAAG,qBAAlC;AACA;;AACA,MAAMC,8BAA8B,GAAGnN,UAAU,CAACiN,sBAAD,CAAjD;AACA;;;AACA,MAAMG,iCAAiC,GAAGpN,UAAU,CAACkN,yBAAD,CAApD;AACA;;;AACA,MAAMG,QAAQ,GAAG,MAAjB;AACA;;AACA,MAAMC,SAAS,GAAG,OAAlB;AACA;;AACA,MAAMC,kBAAkB,GAAGvN,UAAU,CAAC,EAAD,CAArC;;AACA,SAASoM,mBAAT,CAA6B7J,QAA7B,EAAuCC,MAAvC,EAA+C;EAC3C,OAAOgL,IAAI,CAAC1M,OAAL,CAAawB,IAAb,CAAkBC,QAAlB,EAA4BC,MAA5B,CAAP;AACH;;AACD,SAASiL,gCAAT,CAA0CjL,MAA1C,EAAkDD,QAAlD,EAA4DiB,IAA5D,EAAkEyB,cAAlE,EAAkFI,YAAlF,EAAgG;EAC5F,OAAOmI,IAAI,CAAC1M,OAAL,CAAasE,iBAAb,CAA+B5C,MAA/B,EAAuCD,QAAvC,EAAiDiB,IAAjD,EAAuDyB,cAAvD,EAAuEI,YAAvE,CAAP;AACH;;AACD,MAAMqI,UAAU,GAAG1N,UAAnB;AACA,MAAM2N,cAAc,GAAG,OAAOC,MAAP,KAAkB,WAAzC;AACA,MAAMC,cAAc,GAAGF,cAAc,GAAGC,MAAH,GAAYvJ,SAAjD;;AACA,MAAMyJ,OAAO,GAAIH,cAAc,IAAIE,cAAnB,IAAsC9N,UAAtD;;AACA,MAAMgO,gBAAgB,GAAG,iBAAzB;;AACA,SAAStC,aAAT,CAAuBjC,IAAvB,EAA6BhH,MAA7B,EAAqC;EACjC,KAAK,IAAImD,CAAC,GAAG6D,IAAI,CAAC5D,MAAL,GAAc,CAA3B,EAA8BD,CAAC,IAAI,CAAnC,EAAsCA,CAAC,EAAvC,EAA2C;IACvC,IAAI,OAAO6D,IAAI,CAAC7D,CAAD,CAAX,KAAmB,UAAvB,EAAmC;MAC/B6D,IAAI,CAAC7D,CAAD,CAAJ,GAAUyG,mBAAmB,CAAC5C,IAAI,CAAC7D,CAAD,CAAL,EAAUnD,MAAM,GAAG,GAAT,GAAemD,CAAzB,CAA7B;IACH;EACJ;;EACD,OAAO6D,IAAP;AACH;;AACD,SAASwE,cAAT,CAAwB7D,SAAxB,EAAmC8D,OAAnC,EAA4C;EACxC,MAAMzL,MAAM,GAAG2H,SAAS,CAACvI,WAAV,CAAsB,MAAtB,CAAf;;EACA,KAAK,IAAI+D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsI,OAAO,CAACrI,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;IACrC,MAAM1F,IAAI,GAAGgO,OAAO,CAACtI,CAAD,CAApB;IACA,MAAMI,QAAQ,GAAGoE,SAAS,CAAClK,IAAD,CAA1B;;IACA,IAAI8F,QAAJ,EAAc;MACV,MAAMmI,aAAa,GAAGlC,8BAA8B,CAAC7B,SAAD,EAAYlK,IAAZ,CAApD;;MACA,IAAI,CAACkO,kBAAkB,CAACD,aAAD,CAAvB,EAAwC;QACpC;MACH;;MACD/D,SAAS,CAAClK,IAAD,CAAT,GAAkB,CAAE8F,QAAD,IAAc;QAC7B,MAAMqI,OAAO,GAAG,YAAY;UACxB,OAAOrI,QAAQ,CAAC0C,KAAT,CAAe,IAAf,EAAqBgD,aAAa,CAAC7I,SAAD,EAAYJ,MAAM,GAAG,GAAT,GAAevC,IAA3B,CAAlC,CAAP;QACH,CAFD;;QAGAqM,qBAAqB,CAAC8B,OAAD,EAAUrI,QAAV,CAArB;QACA,OAAOqI,OAAP;MACH,CANiB,EAMfrI,QANe,CAAlB;IAOH;EACJ;AACJ;;AACD,SAASoI,kBAAT,CAA4BE,YAA5B,EAA0C;EACtC,IAAI,CAACA,YAAL,EAAmB;IACf,OAAO,IAAP;EACH;;EACD,IAAIA,YAAY,CAACC,QAAb,KAA0B,KAA9B,EAAqC;IACjC,OAAO,KAAP;EACH;;EACD,OAAO,EAAE,OAAOD,YAAY,CAACnM,GAApB,KAA4B,UAA5B,IAA0C,OAAOmM,YAAY,CAACE,GAApB,KAA4B,WAAxE,CAAP;AACH;;AACD,MAAMC,WAAW,GAAG,OAAOC,iBAAP,KAA6B,WAA7B,IAA4CpF,IAAI,YAAYoF,iBAAhF,C,CACA;AACA;;AACA,MAAMC,MAAM,GAAG,EAAE,QAAQZ,OAAV,KACX,OAAOA,OAAO,CAACa,OAAf,KAA2B,WADhB,IAEXb,OAAO,CAACa,OAAR,CAAgB3E,QAAhB,OAA+B,kBAFnC;AAGA,MAAM4E,SAAS,GAAG,CAACF,MAAD,IAAW,CAACF,WAAZ,IAA2B,CAAC,EAAEb,cAAc,IAAIE,cAAc,CAAC,aAAD,CAAlC,CAA9C,C,CACA;AACA;AACA;;AACA,MAAMgB,KAAK,GAAG,OAAOf,OAAO,CAACa,OAAf,KAA2B,WAA3B,IACVb,OAAO,CAACa,OAAR,CAAgB3E,QAAhB,OAA+B,kBADrB,IAEV,CAACwE,WAFS,IAGV,CAAC,EAAEb,cAAc,IAAIE,cAAc,CAAC,aAAD,CAAlC,CAHL;AAIA,MAAMiB,sBAAsB,GAAG,EAA/B;AACA,MAAMC,wBAAwB,GAAGrB,UAAU,CAAC,qBAAD,CAA3C;;AACA,MAAMsB,MAAM,GAAG,UAAUC,KAAV,EAAiB;EAC5B;EACA;EACAA,KAAK,GAAGA,KAAK,IAAInB,OAAO,CAACmB,KAAzB;;EACA,IAAI,CAACA,KAAL,EAAY;IACR;EACH;;EACD,IAAIC,eAAe,GAAGJ,sBAAsB,CAACG,KAAK,CAAC1L,IAAP,CAA5C;;EACA,IAAI,CAAC2L,eAAL,EAAsB;IAClBA,eAAe,GAAGJ,sBAAsB,CAACG,KAAK,CAAC1L,IAAP,CAAtB,GAAqCmK,UAAU,CAAC,gBAAgBuB,KAAK,CAAC1L,IAAvB,CAAjE;EACH;;EACD,MAAM0C,MAAM,GAAG,QAAQgJ,KAAK,CAAChJ,MAAd,IAAwB6H,OAAvC;EACA,MAAMqB,QAAQ,GAAGlJ,MAAM,CAACiJ,eAAD,CAAvB;EACA,IAAIE,MAAJ;;EACA,IAAIR,SAAS,IAAI3I,MAAM,KAAK4H,cAAxB,IAA0CoB,KAAK,CAAC1L,IAAN,KAAe,OAA7D,EAAsE;IAClE;IACA;IACA;IACA,MAAM8L,UAAU,GAAGJ,KAAnB;IACAG,MAAM,GACFD,QAAQ,IACJA,QAAQ,CAAC5F,IAAT,CAAc,IAAd,EAAoB8F,UAAU,CAACC,OAA/B,EAAwCD,UAAU,CAACE,QAAnD,EAA6DF,UAAU,CAACG,MAAxE,EAAgFH,UAAU,CAACI,KAA3F,EAAkGJ,UAAU,CAACpM,KAA7G,CAFR;;IAGA,IAAImM,MAAM,KAAK,IAAf,EAAqB;MACjBH,KAAK,CAACS,cAAN;IACH;EACJ,CAXD,MAYK;IACDN,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAAC1G,KAAT,CAAe,IAAf,EAAqB7F,SAArB,CAArB;;IACA,KACA;IACA;IACA;IACA;IACA;IACAqM,KAAK,CAAC1L,IAAN,KAAe,cAAf,IACI;IACA;IACA;IACA;IACA;IACAuK,OAAO,CAACiB,wBAAD,CANX,IAOI;IACA;IACA,OAAOK,MAAP,KAAkB,QAftB,EAegC;MAC5BH,KAAK,CAACU,WAAN,GAAoBP,MAApB;IACH,CAjBD,MAkBK,IAAIA,MAAM,IAAI/K,SAAV,IAAuB,CAAC+K,MAA5B,EAAoC;MACrCH,KAAK,CAACS,cAAN;IACH;EACJ;;EACD,OAAON,MAAP;AACH,CAnDD;;AAoDA,SAASQ,aAAT,CAAuBC,GAAvB,EAA4BC,IAA5B,EAAkC3F,SAAlC,EAA6C;EACzC,IAAI4F,IAAI,GAAG/D,8BAA8B,CAAC6D,GAAD,EAAMC,IAAN,CAAzC;;EACA,IAAI,CAACC,IAAD,IAAS5F,SAAb,EAAwB;IACpB;IACA,MAAM+D,aAAa,GAAGlC,8BAA8B,CAAC7B,SAAD,EAAY2F,IAAZ,CAApD;;IACA,IAAI5B,aAAJ,EAAmB;MACf6B,IAAI,GAAG;QAAEC,UAAU,EAAE,IAAd;QAAoBC,YAAY,EAAE;MAAlC,CAAP;IACH;EACJ,CARwC,CASzC;EACA;;;EACA,IAAI,CAACF,IAAD,IAAS,CAACA,IAAI,CAACE,YAAnB,EAAiC;IAC7B;EACH;;EACD,MAAMC,mBAAmB,GAAGxC,UAAU,CAAC,OAAOoC,IAAP,GAAc,SAAf,CAAtC;;EACA,IAAID,GAAG,CAACvO,cAAJ,CAAmB4O,mBAAnB,KAA2CL,GAAG,CAACK,mBAAD,CAAlD,EAAyE;IACrE;EACH,CAjBwC,CAkBzC;EACA;EACA;EACA;EACA;;;EACA,OAAOH,IAAI,CAACzB,QAAZ;EACA,OAAOyB,IAAI,CAAClH,KAAZ;EACA,MAAMsH,eAAe,GAAGJ,IAAI,CAAC7N,GAA7B;EACA,MAAMkO,eAAe,GAAGL,IAAI,CAACxB,GAA7B,CA1ByC,CA2BzC;;EACA,MAAM8B,SAAS,GAAGP,IAAI,CAAC9C,KAAL,CAAW,CAAX,CAAlB;EACA,IAAIkC,eAAe,GAAGJ,sBAAsB,CAACuB,SAAD,CAA5C;;EACA,IAAI,CAACnB,eAAL,EAAsB;IAClBA,eAAe,GAAGJ,sBAAsB,CAACuB,SAAD,CAAtB,GAAoC3C,UAAU,CAAC,gBAAgB2C,SAAjB,CAAhE;EACH;;EACDN,IAAI,CAACxB,GAAL,GAAW,UAAU+B,QAAV,EAAoB;IAC3B;IACA;IACA;IACA;IACA,IAAIrK,MAAM,GAAG,IAAb;;IACA,IAAI,CAACA,MAAD,IAAW4J,GAAG,KAAK/B,OAAvB,EAAgC;MAC5B7H,MAAM,GAAG6H,OAAT;IACH;;IACD,IAAI,CAAC7H,MAAL,EAAa;MACT;IACH;;IACD,MAAMsK,aAAa,GAAGtK,MAAM,CAACiJ,eAAD,CAA5B;;IACA,IAAI,OAAOqB,aAAP,KAAyB,UAA7B,EAAyC;MACrCtK,MAAM,CAACuK,mBAAP,CAA2BH,SAA3B,EAAsCrB,MAAtC;IACH,CAf0B,CAgB3B;IACA;IACA;;;IACAoB,eAAe,EAAE7G,IAAjB,CAAsBtD,MAAtB,EAA8B,IAA9B;IACAA,MAAM,CAACiJ,eAAD,CAAN,GAA0BoB,QAA1B;;IACA,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;MAChCrK,MAAM,CAACwK,gBAAP,CAAwBJ,SAAxB,EAAmCrB,MAAnC,EAA2C,KAA3C;IACH;EACJ,CAxBD,CAjCyC,CA0DzC;EACA;;;EACAe,IAAI,CAAC7N,GAAL,GAAW,YAAY;IACnB;IACA;IACA,IAAI+D,MAAM,GAAG,IAAb;;IACA,IAAI,CAACA,MAAD,IAAW4J,GAAG,KAAK/B,OAAvB,EAAgC;MAC5B7H,MAAM,GAAG6H,OAAT;IACH;;IACD,IAAI,CAAC7H,MAAL,EAAa;MACT,OAAO,IAAP;IACH;;IACD,MAAMkJ,QAAQ,GAAGlJ,MAAM,CAACiJ,eAAD,CAAvB;;IACA,IAAIC,QAAJ,EAAc;MACV,OAAOA,QAAP;IACH,CAFD,MAGK,IAAIgB,eAAJ,EAAqB;MACtB;MACA;MACA;MACA;MACA;MACA;MACA,IAAItH,KAAK,GAAGsH,eAAe,CAAC5G,IAAhB,CAAqB,IAArB,CAAZ;;MACA,IAAIV,KAAJ,EAAW;QACPkH,IAAI,CAACxB,GAAL,CAAShF,IAAT,CAAc,IAAd,EAAoBV,KAApB;;QACA,IAAI,OAAO5C,MAAM,CAAC8H,gBAAD,CAAb,KAAoC,UAAxC,EAAoD;UAChD9H,MAAM,CAACyK,eAAP,CAAuBZ,IAAvB;QACH;;QACD,OAAOjH,KAAP;MACH;IACJ;;IACD,OAAO,IAAP;EACH,CA/BD;;EAgCAkD,oBAAoB,CAAC8D,GAAD,EAAMC,IAAN,EAAYC,IAAZ,CAApB;EACAF,GAAG,CAACK,mBAAD,CAAH,GAA2B,IAA3B;AACH;;AACD,SAAS3E,iBAAT,CAA2BsE,GAA3B,EAAgC9N,UAAhC,EAA4CoI,SAA5C,EAAuD;EACnD,IAAIpI,UAAJ,EAAgB;IACZ,KAAK,IAAI4D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG5D,UAAU,CAAC6D,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;MACxCiK,aAAa,CAACC,GAAD,EAAM,OAAO9N,UAAU,CAAC4D,CAAD,CAAvB,EAA4BwE,SAA5B,CAAb;IACH;EACJ,CAJD,MAKK;IACD,MAAMwG,YAAY,GAAG,EAArB;;IACA,KAAK,MAAMb,IAAX,IAAmBD,GAAnB,EAAwB;MACpB,IAAIC,IAAI,CAAC9C,KAAL,CAAW,CAAX,EAAc,CAAd,KAAoB,IAAxB,EAA8B;QAC1B2D,YAAY,CAAChI,IAAb,CAAkBmH,IAAlB;MACH;IACJ;;IACD,KAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,YAAY,CAAC/K,MAAjC,EAAyCgL,CAAC,EAA1C,EAA8C;MAC1ChB,aAAa,CAACC,GAAD,EAAMc,YAAY,CAACC,CAAD,CAAlB,EAAuBzG,SAAvB,CAAb;IACH;EACJ;AACJ;;AACD,MAAM0G,mBAAmB,GAAGnD,UAAU,CAAC,kBAAD,CAAtC,C,CACA;;AACA,SAASvB,UAAT,CAAoB2E,SAApB,EAA+B;EAC3B,MAAMC,aAAa,GAAGjD,OAAO,CAACgD,SAAD,CAA7B;EACA,IAAI,CAACC,aAAL,EACI,OAHuB,CAI3B;;EACAjD,OAAO,CAACJ,UAAU,CAACoD,SAAD,CAAX,CAAP,GAAiCC,aAAjC;;EACAjD,OAAO,CAACgD,SAAD,CAAP,GAAqB,YAAY;IAC7B,MAAME,CAAC,GAAGvF,aAAa,CAAC7I,SAAD,EAAYkO,SAAZ,CAAvB;;IACA,QAAQE,CAAC,CAACpL,MAAV;MACI,KAAK,CAAL;QACI,KAAKiL,mBAAL,IAA4B,IAAIE,aAAJ,EAA5B;QACA;;MACJ,KAAK,CAAL;QACI,KAAKF,mBAAL,IAA4B,IAAIE,aAAJ,CAAkBC,CAAC,CAAC,CAAD,CAAnB,CAA5B;QACA;;MACJ,KAAK,CAAL;QACI,KAAKH,mBAAL,IAA4B,IAAIE,aAAJ,CAAkBC,CAAC,CAAC,CAAD,CAAnB,EAAwBA,CAAC,CAAC,CAAD,CAAzB,CAA5B;QACA;;MACJ,KAAK,CAAL;QACI,KAAKH,mBAAL,IAA4B,IAAIE,aAAJ,CAAkBC,CAAC,CAAC,CAAD,CAAnB,EAAwBA,CAAC,CAAC,CAAD,CAAzB,EAA8BA,CAAC,CAAC,CAAD,CAA/B,CAA5B;QACA;;MACJ,KAAK,CAAL;QACI,KAAKH,mBAAL,IAA4B,IAAIE,aAAJ,CAAkBC,CAAC,CAAC,CAAD,CAAnB,EAAwBA,CAAC,CAAC,CAAD,CAAzB,EAA8BA,CAAC,CAAC,CAAD,CAA/B,EAAoCA,CAAC,CAAC,CAAD,CAArC,CAA5B;QACA;;MACJ;QACI,MAAM,IAAIrQ,KAAJ,CAAU,oBAAV,CAAN;IAjBR;EAmBH,CArBD,CAN2B,CA4B3B;;;EACA2L,qBAAqB,CAACwB,OAAO,CAACgD,SAAD,CAAR,EAAqBC,aAArB,CAArB;EACA,MAAME,QAAQ,GAAG,IAAIF,aAAJ,CAAkB,YAAY,CAAG,CAAjC,CAAjB;EACA,IAAIjB,IAAJ;;EACA,KAAKA,IAAL,IAAamB,QAAb,EAAuB;IACnB;IACA,IAAIH,SAAS,KAAK,gBAAd,IAAkChB,IAAI,KAAK,cAA/C,EACI;;IACJ,CAAC,UAAUA,IAAV,EAAgB;MACb,IAAI,OAAOmB,QAAQ,CAACnB,IAAD,CAAf,KAA0B,UAA9B,EAA0C;QACtChC,OAAO,CAACgD,SAAD,CAAP,CAAmB3G,SAAnB,CAA6B2F,IAA7B,IAAqC,YAAY;UAC7C,OAAO,KAAKe,mBAAL,EAA0Bf,IAA1B,EAAgCrH,KAAhC,CAAsC,KAAKoI,mBAAL,CAAtC,EAAiEjO,SAAjE,CAAP;QACH,CAFD;MAGH,CAJD,MAKK;QACDmJ,oBAAoB,CAAC+B,OAAO,CAACgD,SAAD,CAAP,CAAmB3G,SAApB,EAA+B2F,IAA/B,EAAqC;UACrDvB,GAAG,EAAE,UAAUnN,EAAV,EAAc;YACf,IAAI,OAAOA,EAAP,KAAc,UAAlB,EAA8B;cAC1B,KAAKyP,mBAAL,EAA0Bf,IAA1B,IAAkC1D,mBAAmB,CAAChL,EAAD,EAAK0P,SAAS,GAAG,GAAZ,GAAkBhB,IAAvB,CAArD,CAD0B,CAE1B;cACA;cACA;;cACAxD,qBAAqB,CAAC,KAAKuE,mBAAL,EAA0Bf,IAA1B,CAAD,EAAkC1O,EAAlC,CAArB;YACH,CAND,MAOK;cACD,KAAKyP,mBAAL,EAA0Bf,IAA1B,IAAkC1O,EAAlC;YACH;UACJ,CAZoD;UAarDc,GAAG,EAAE,YAAY;YACb,OAAO,KAAK2O,mBAAL,EAA0Bf,IAA1B,CAAP;UACH;QAfoD,CAArC,CAApB;MAiBH;IACJ,CAzBD,EAyBGA,IAzBH;EA0BH;;EACD,KAAKA,IAAL,IAAaiB,aAAb,EAA4B;IACxB,IAAIjB,IAAI,KAAK,WAAT,IAAwBiB,aAAa,CAACzP,cAAd,CAA6BwO,IAA7B,CAA5B,EAAgE;MAC5DhC,OAAO,CAACgD,SAAD,CAAP,CAAmBhB,IAAnB,IAA2BiB,aAAa,CAACjB,IAAD,CAAxC;IACH;EACJ;AACJ;;AACD,SAAStE,WAAT,CAAqBvF,MAArB,EAA6BhG,IAA7B,EAAmCiR,OAAnC,EAA4C;EACxC,IAAIC,KAAK,GAAGlL,MAAZ;;EACA,OAAOkL,KAAK,IAAI,CAACA,KAAK,CAAC7P,cAAN,CAAqBrB,IAArB,CAAjB,EAA6C;IACzCkR,KAAK,GAAGvE,oBAAoB,CAACuE,KAAD,CAA5B;EACH;;EACD,IAAI,CAACA,KAAD,IAAUlL,MAAM,CAAChG,IAAD,CAApB,EAA4B;IACxB;IACAkR,KAAK,GAAGlL,MAAR;EACH;;EACD,MAAMmL,YAAY,GAAG1D,UAAU,CAACzN,IAAD,CAA/B;EACA,IAAI8F,QAAQ,GAAG,IAAf;;EACA,IAAIoL,KAAK,KAAK,EAAEpL,QAAQ,GAAGoL,KAAK,CAACC,YAAD,CAAlB,KAAqC,CAACD,KAAK,CAAC7P,cAAN,CAAqB8P,YAArB,CAA3C,CAAT,EAAyF;IACrFrL,QAAQ,GAAGoL,KAAK,CAACC,YAAD,CAAL,GAAsBD,KAAK,CAAClR,IAAD,CAAtC,CADqF,CAErF;IACA;;IACA,MAAM8P,IAAI,GAAGoB,KAAK,IAAInF,8BAA8B,CAACmF,KAAD,EAAQlR,IAAR,CAApD;;IACA,IAAIkO,kBAAkB,CAAC4B,IAAD,CAAtB,EAA8B;MAC1B,MAAMsB,aAAa,GAAGH,OAAO,CAACnL,QAAD,EAAWqL,YAAX,EAAyBnR,IAAzB,CAA7B;;MACAkR,KAAK,CAAClR,IAAD,CAAL,GAAc,YAAY;QACtB,OAAOoR,aAAa,CAAC,IAAD,EAAOzO,SAAP,CAApB;MACH,CAFD;;MAGA0J,qBAAqB,CAAC6E,KAAK,CAAClR,IAAD,CAAN,EAAc8F,QAAd,CAArB;IACH;EACJ;;EACD,OAAOA,QAAP;AACH,C,CACD;;;AACA,SAAS4F,cAAT,CAAwBkE,GAAxB,EAA6ByB,QAA7B,EAAuCC,WAAvC,EAAoD;EAChD,IAAIC,SAAS,GAAG,IAAhB;;EACA,SAAS5M,YAAT,CAAsBxB,IAAtB,EAA4B;IACxB,MAAMI,IAAI,GAAGJ,IAAI,CAACI,IAAlB;;IACAA,IAAI,CAACgG,IAAL,CAAUhG,IAAI,CAACiO,KAAf,IAAwB,YAAY;MAChCrO,IAAI,CAACJ,MAAL,CAAYyF,KAAZ,CAAkB,IAAlB,EAAwB7F,SAAxB;IACH,CAFD;;IAGA4O,SAAS,CAAC/I,KAAV,CAAgBjF,IAAI,CAACyC,MAArB,EAA6BzC,IAAI,CAACgG,IAAlC;IACA,OAAOpG,IAAP;EACH;;EACDoO,SAAS,GAAGhG,WAAW,CAACqE,GAAD,EAAMyB,QAAN,EAAiBvL,QAAD,IAAc,UAAUsD,IAAV,EAAgBG,IAAhB,EAAsB;IACvE,MAAMkI,IAAI,GAAGH,WAAW,CAAClI,IAAD,EAAOG,IAAP,CAAxB;;IACA,IAAIkI,IAAI,CAACD,KAAL,IAAc,CAAd,IAAmB,OAAOjI,IAAI,CAACkI,IAAI,CAACD,KAAN,CAAX,KAA4B,UAAnD,EAA+D;MAC3D,OAAOhE,gCAAgC,CAACiE,IAAI,CAACzR,IAAN,EAAYuJ,IAAI,CAACkI,IAAI,CAACD,KAAN,CAAhB,EAA8BC,IAA9B,EAAoC9M,YAApC,CAAvC;IACH,CAFD,MAGK;MACD;MACA,OAAOmB,QAAQ,CAAC0C,KAAT,CAAeY,IAAf,EAAqBG,IAArB,CAAP;IACH;EACJ,CATsB,CAAvB;AAUH;;AACD,SAAS8C,qBAAT,CAA+B8B,OAA/B,EAAwCuD,QAAxC,EAAkD;EAC9CvD,OAAO,CAACV,UAAU,CAAC,kBAAD,CAAX,CAAP,GAA0CiE,QAA1C;AACH;;AACD,IAAIC,kBAAkB,GAAG,KAAzB;AACA,IAAIC,QAAQ,GAAG,KAAf;;AACA,SAAShG,UAAT,GAAsB;EAClB,IAAI+F,kBAAJ,EAAwB;IACpB,OAAOC,QAAP;EACH;;EACDD,kBAAkB,GAAG,IAArB;;EACA,IAAI;IACA,MAAME,EAAE,GAAGjE,cAAc,CAACkE,SAAf,CAAyBC,SAApC;;IACA,IAAIF,EAAE,CAACG,OAAH,CAAW,OAAX,MAAwB,CAAC,CAAzB,IAA8BH,EAAE,CAACG,OAAH,CAAW,UAAX,MAA2B,CAAC,CAA1D,IAA+DH,EAAE,CAACG,OAAH,CAAW,OAAX,MAAwB,CAAC,CAA5F,EAA+F;MAC3FJ,QAAQ,GAAG,IAAX;IACH;EACJ,CALD,CAMA,OAAO5O,KAAP,EAAc,CAAG;;EACjB,OAAO4O,QAAP;AACH;;AACD,SAASK,UAAT,CAAoBrJ,KAApB,EAA2B;EACvB,OAAO,OAAOA,KAAP,KAAiB,UAAxB;AACH;;AACD,SAASsJ,QAAT,CAAkBtJ,KAAlB,EAAyB;EACrB,OAAO,OAAOA,KAAP,KAAiB,QAAxB;AACH;AAED;AACA;AACA;AACA;AACA;;;AACA,MAAMuJ,8BAA8B,GAAG;EACnC9I,IAAI,EAAE;AAD6B,CAAvC;AAGA,MAAM+I,oBAAoB,GAAG,EAA7B;AACA,MAAMC,aAAa,GAAG,EAAtB;AACA,MAAMC,sBAAsB,GAAG,IAAIC,MAAJ,CAAW,MAAMjF,kBAAN,GAA2B,qBAAtC,CAA/B;AACA,MAAMkF,4BAA4B,GAAG/E,UAAU,CAAC,oBAAD,CAA/C;;AACA,SAASgF,iBAAT,CAA2BrC,SAA3B,EAAsCsC,iBAAtC,EAAyD;EACrD,MAAMC,cAAc,GAAG,CAACD,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAD,CAApB,GAAkCA,SAApD,IAAiE/C,SAAxF;EACA,MAAMuF,aAAa,GAAG,CAACF,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAD,CAApB,GAAkCA,SAApD,IAAiEhD,QAAvF;EACA,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAGqF,cAApC;EACA,MAAME,aAAa,GAAGvF,kBAAkB,GAAGsF,aAA3C;EACAR,oBAAoB,CAAChC,SAAD,CAApB,GAAkC,EAAlC;EACAgC,oBAAoB,CAAChC,SAAD,CAApB,CAAgC/C,SAAhC,IAA6CpC,MAA7C;EACAmH,oBAAoB,CAAChC,SAAD,CAApB,CAAgChD,QAAhC,IAA4CyF,aAA5C;AACH;;AACD,SAASxH,gBAAT,CAA0BwC,OAA1B,EAAmCiF,GAAnC,EAAwCC,IAAxC,EAA8CC,YAA9C,EAA4D;EACxD,MAAMC,kBAAkB,GAAID,YAAY,IAAIA,YAAY,CAACE,GAA9B,IAAsClG,sBAAjE;EACA,MAAMmG,qBAAqB,GAAIH,YAAY,IAAIA,YAAY,CAACI,EAA9B,IAAqCnG,yBAAnE;EACA,MAAMoG,wBAAwB,GAAIL,YAAY,IAAIA,YAAY,CAACM,SAA9B,IAA4C,gBAA7E;EACA,MAAMC,mCAAmC,GAAIP,YAAY,IAAIA,YAAY,CAACQ,KAA9B,IAAwC,oBAApF;EACA,MAAMC,0BAA0B,GAAGhG,UAAU,CAACwF,kBAAD,CAA7C;EACA,MAAMS,yBAAyB,GAAG,MAAMT,kBAAN,GAA2B,GAA7D;EACA,MAAMU,sBAAsB,GAAG,iBAA/B;EACA,MAAMC,6BAA6B,GAAG,MAAMD,sBAAN,GAA+B,GAArE;;EACA,MAAMtP,UAAU,GAAG,UAAUlB,IAAV,EAAgB6C,MAAhB,EAAwBgJ,KAAxB,EAA+B;IAC9C;IACA;IACA,IAAI7L,IAAI,CAAC0Q,SAAT,EAAoB;MAChB;IACH;;IACD,MAAM/N,QAAQ,GAAG3C,IAAI,CAACb,QAAtB;;IACA,IAAI,OAAOwD,QAAP,KAAoB,QAApB,IAAgCA,QAAQ,CAACgO,WAA7C,EAA0D;MACtD;MACA3Q,IAAI,CAACb,QAAL,GAAiB0M,KAAD,IAAWlJ,QAAQ,CAACgO,WAAT,CAAqB9E,KAArB,CAA3B;;MACA7L,IAAI,CAAC4Q,gBAAL,GAAwBjO,QAAxB;IACH,CAX6C,CAY9C;IACA;IACA;IACA;;;IACA,IAAI9C,KAAJ;;IACA,IAAI;MACAG,IAAI,CAACJ,MAAL,CAAYI,IAAZ,EAAkB6C,MAAlB,EAA0B,CAACgJ,KAAD,CAA1B;IACH,CAFD,CAGA,OAAOlK,GAAP,EAAY;MACR9B,KAAK,GAAG8B,GAAR;IACH;;IACD,MAAMqE,OAAO,GAAGhG,IAAI,CAACgG,OAArB;;IACA,IAAIA,OAAO,IAAI,OAAOA,OAAP,KAAmB,QAA9B,IAA0CA,OAAO,CAAC6K,IAAtD,EAA4D;MACxD;MACA;MACA;MACA,MAAMlO,QAAQ,GAAG3C,IAAI,CAAC4Q,gBAAL,GAAwB5Q,IAAI,CAAC4Q,gBAA7B,GAAgD5Q,IAAI,CAACb,QAAtE;MACA0D,MAAM,CAACmN,qBAAD,CAAN,CAA8B7J,IAA9B,CAAmCtD,MAAnC,EAA2CgJ,KAAK,CAAC1L,IAAjD,EAAuDwC,QAAvD,EAAiEqD,OAAjE;IACH;;IACD,OAAOnG,KAAP;EACH,CAhCD;;EAiCA,SAASiR,cAAT,CAAwBC,OAAxB,EAAiClF,KAAjC,EAAwCmF,SAAxC,EAAmD;IAC/C;IACA;IACAnF,KAAK,GAAGA,KAAK,IAAInB,OAAO,CAACmB,KAAzB;;IACA,IAAI,CAACA,KAAL,EAAY;MACR;IACH,CAN8C,CAO/C;IACA;;;IACA,MAAMhJ,MAAM,GAAGkO,OAAO,IAAIlF,KAAK,CAAChJ,MAAjB,IAA2B6H,OAA1C;IACA,MAAMuG,KAAK,GAAGpO,MAAM,CAACoM,oBAAoB,CAACpD,KAAK,CAAC1L,IAAP,CAApB,CAAiC6Q,SAAS,GAAG/G,QAAH,GAAcC,SAAxD,CAAD,CAApB;;IACA,IAAI+G,KAAJ,EAAW;MACP,MAAMC,MAAM,GAAG,EAAf,CADO,CAEP;MACA;;MACA,IAAID,KAAK,CAACzO,MAAN,KAAiB,CAArB,EAAwB;QACpB,MAAMb,GAAG,GAAGT,UAAU,CAAC+P,KAAK,CAAC,CAAD,CAAN,EAAWpO,MAAX,EAAmBgJ,KAAnB,CAAtB;QACAlK,GAAG,IAAIuP,MAAM,CAAC3L,IAAP,CAAY5D,GAAZ,CAAP;MACH,CAHD,MAIK;QACD;QACA;QACA;QACA,MAAMwP,SAAS,GAAGF,KAAK,CAACrH,KAAN,EAAlB;;QACA,KAAK,IAAIrH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4O,SAAS,CAAC3O,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;UACvC,IAAIsJ,KAAK,IAAIA,KAAK,CAACwD,4BAAD,CAAL,KAAwC,IAArD,EAA2D;YACvD;UACH;;UACD,MAAM1N,GAAG,GAAGT,UAAU,CAACiQ,SAAS,CAAC5O,CAAD,CAAV,EAAeM,MAAf,EAAuBgJ,KAAvB,CAAtB;UACAlK,GAAG,IAAIuP,MAAM,CAAC3L,IAAP,CAAY5D,GAAZ,CAAP;QACH;MACJ,CApBM,CAqBP;MACA;;;MACA,IAAIuP,MAAM,CAAC1O,MAAP,KAAkB,CAAtB,EAAyB;QACrB,MAAM0O,MAAM,CAAC,CAAD,CAAZ;MACH,CAFD,MAGK;QACD,KAAK,IAAI3O,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2O,MAAM,CAAC1O,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;UACpC,MAAMZ,GAAG,GAAGuP,MAAM,CAAC3O,CAAD,CAAlB;UACAoN,GAAG,CAACpI,uBAAJ,CAA4B,MAAM;YAC9B,MAAM5F,GAAN;UACH,CAFD;QAGH;MACJ;IACJ;EACJ,CAxFuD,CAyFxD;;;EACA,MAAMyP,uBAAuB,GAAG,UAAUvF,KAAV,EAAiB;IAC7C,OAAOiF,cAAc,CAAC,IAAD,EAAOjF,KAAP,EAAc,KAAd,CAArB;EACH,CAFD,CA1FwD,CA6FxD;;;EACA,MAAMwF,8BAA8B,GAAG,UAAUxF,KAAV,EAAiB;IACpD,OAAOiF,cAAc,CAAC,IAAD,EAAOjF,KAAP,EAAc,IAAd,CAArB;EACH,CAFD;;EAGA,SAASyF,uBAAT,CAAiC7E,GAAjC,EAAsCoD,YAAtC,EAAoD;IAChD,IAAI,CAACpD,GAAL,EAAU;MACN,OAAO,KAAP;IACH;;IACD,IAAI8E,iBAAiB,GAAG,IAAxB;;IACA,IAAI1B,YAAY,IAAIA,YAAY,CAAC3J,IAAb,KAAsBjF,SAA1C,EAAqD;MACjDsQ,iBAAiB,GAAG1B,YAAY,CAAC3J,IAAjC;IACH;;IACD,MAAMsL,eAAe,GAAG3B,YAAY,IAAIA,YAAY,CAAC4B,EAArD;IACA,IAAItT,cAAc,GAAG,IAArB;;IACA,IAAI0R,YAAY,IAAIA,YAAY,CAAC6B,MAAb,KAAwBzQ,SAA5C,EAAuD;MACnD9C,cAAc,GAAG0R,YAAY,CAAC6B,MAA9B;IACH;;IACD,IAAIC,YAAY,GAAG,KAAnB;;IACA,IAAI9B,YAAY,IAAIA,YAAY,CAAC+B,EAAb,KAAoB3Q,SAAxC,EAAmD;MAC/C0Q,YAAY,GAAG9B,YAAY,CAAC+B,EAA5B;IACH;;IACD,IAAI7D,KAAK,GAAGtB,GAAZ;;IACA,OAAOsB,KAAK,IAAI,CAACA,KAAK,CAAC7P,cAAN,CAAqB4R,kBAArB,CAAjB,EAA2D;MACvD/B,KAAK,GAAGvE,oBAAoB,CAACuE,KAAD,CAA5B;IACH;;IACD,IAAI,CAACA,KAAD,IAAUtB,GAAG,CAACqD,kBAAD,CAAjB,EAAuC;MACnC;MACA/B,KAAK,GAAGtB,GAAR;IACH;;IACD,IAAI,CAACsB,KAAL,EAAY;MACR,OAAO,KAAP;IACH;;IACD,IAAIA,KAAK,CAACuC,0BAAD,CAAT,EAAuC;MACnC,OAAO,KAAP;IACH;;IACD,MAAMf,iBAAiB,GAAGM,YAAY,IAAIA,YAAY,CAACN,iBAAvD,CA/BgD,CAgChD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMsC,QAAQ,GAAG,EAAjB;IACA,MAAMC,sBAAsB,GAAI/D,KAAK,CAACuC,0BAAD,CAAL,GAAoCvC,KAAK,CAAC+B,kBAAD,CAAzE;IACA,MAAMiC,yBAAyB,GAAIhE,KAAK,CAACzD,UAAU,CAAC0F,qBAAD,CAAX,CAAL,GAC/BjC,KAAK,CAACiC,qBAAD,CADT;IAEA,MAAMgC,eAAe,GAAIjE,KAAK,CAACzD,UAAU,CAAC4F,wBAAD,CAAX,CAAL,GACrBnC,KAAK,CAACmC,wBAAD,CADT;IAEA,MAAM+B,wBAAwB,GAAIlE,KAAK,CAACzD,UAAU,CAAC8F,mCAAD,CAAX,CAAL,GAC9BrC,KAAK,CAACqC,mCAAD,CADT;IAEA,IAAI8B,0BAAJ;;IACA,IAAIrC,YAAY,IAAIA,YAAY,CAACsC,OAAjC,EAA0C;MACtCD,0BAA0B,GAAGnE,KAAK,CAACzD,UAAU,CAACuF,YAAY,CAACsC,OAAd,CAAX,CAAL,GACzBpE,KAAK,CAAC8B,YAAY,CAACsC,OAAd,CADT;IAEH;IACD;AACR;AACA;AACA;;;IACQ,SAASC,yBAAT,CAAmCpM,OAAnC,EAA4CqM,OAA5C,EAAqD;MACjD,IAAI,CAACA,OAAL,EAAc;QACV,OAAOrM,OAAP;MACH;;MACD,IAAI,OAAOA,OAAP,KAAmB,SAAvB,EAAkC;QAC9B,OAAO;UAAEsM,OAAO,EAAEtM,OAAX;UAAoBqM,OAAO,EAAE;QAA7B,CAAP;MACH;;MACD,IAAI,CAACrM,OAAL,EAAc;QACV,OAAO;UAAEqM,OAAO,EAAE;QAAX,CAAP;MACH;;MACD,IAAI,OAAOrM,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,CAACqM,OAAR,KAAoB,KAAvD,EAA8D;QAC1D,OAAO,EAAE,GAAGrM,OAAL;UAAcqM,OAAO,EAAE;QAAvB,CAAP;MACH;;MACD,OAAOrM,OAAP;IACH;;IACD,MAAMuM,oBAAoB,GAAG,UAAUvS,IAAV,EAAgB;MACzC;MACA;MACA,IAAI6R,QAAQ,CAACW,UAAb,EAAyB;QACrB;MACH;;MACD,OAAOV,sBAAsB,CAAC3L,IAAvB,CAA4B0L,QAAQ,CAAChP,MAArC,EAA6CgP,QAAQ,CAAC5E,SAAtD,EAAiE4E,QAAQ,CAACS,OAAT,GAAmBjB,8BAAnB,GAAoDD,uBAArH,EAA8IS,QAAQ,CAAC7L,OAAvJ,CAAP;IACH,CAPD;IAQA;AACR;AACA;AACA;AACA;AACA;;;IACQ,MAAMyM,kBAAkB,GAAG,UAAUzS,IAAV,EAAgB;MACvC;MACA;MACA;MACA,IAAI,CAACA,IAAI,CAAC0Q,SAAV,EAAqB;QACjB,MAAMgC,gBAAgB,GAAGzD,oBAAoB,CAACjP,IAAI,CAACiN,SAAN,CAA7C;QACA,IAAI0F,eAAJ;;QACA,IAAID,gBAAJ,EAAsB;UAClBC,eAAe,GAAGD,gBAAgB,CAAC1S,IAAI,CAACsS,OAAL,GAAerI,QAAf,GAA0BC,SAA3B,CAAlC;QACH;;QACD,MAAM0I,aAAa,GAAGD,eAAe,IAAI3S,IAAI,CAAC6C,MAAL,CAAY8P,eAAZ,CAAzC;;QACA,IAAIC,aAAJ,EAAmB;UACf,KAAK,IAAIrQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqQ,aAAa,CAACpQ,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;YAC3C,MAAMsQ,YAAY,GAAGD,aAAa,CAACrQ,CAAD,CAAlC;;YACA,IAAIsQ,YAAY,KAAK7S,IAArB,EAA2B;cACvB4S,aAAa,CAACE,MAAd,CAAqBvQ,CAArB,EAAwB,CAAxB,EADuB,CAEvB;;cACAvC,IAAI,CAAC0Q,SAAL,GAAiB,IAAjB;;cACA,IAAI1Q,IAAI,CAAC+S,mBAAT,EAA8B;gBAC1B/S,IAAI,CAAC+S,mBAAL;gBACA/S,IAAI,CAAC+S,mBAAL,GAA2B,IAA3B;cACH;;cACD,IAAIH,aAAa,CAACpQ,MAAd,KAAyB,CAA7B,EAAgC;gBAC5B;gBACA;gBACAxC,IAAI,CAACgT,UAAL,GAAkB,IAAlB;gBACAhT,IAAI,CAAC6C,MAAL,CAAY8P,eAAZ,IAA+B,IAA/B;cACH;;cACD;YACH;UACJ;QACJ;MACJ,CAhCsC,CAiCvC;MACA;MACA;;;MACA,IAAI,CAAC3S,IAAI,CAACgT,UAAV,EAAsB;QAClB;MACH;;MACD,OAAOjB,yBAAyB,CAAC5L,IAA1B,CAA+BnG,IAAI,CAAC6C,MAApC,EAA4C7C,IAAI,CAACiN,SAAjD,EAA4DjN,IAAI,CAACsS,OAAL,GAAejB,8BAAf,GAAgDD,uBAA5G,EAAqIpR,IAAI,CAACgG,OAA1I,CAAP;IACH,CAxCD;;IAyCA,MAAMiN,uBAAuB,GAAG,UAAUjT,IAAV,EAAgB;MAC5C,OAAO8R,sBAAsB,CAAC3L,IAAvB,CAA4B0L,QAAQ,CAAChP,MAArC,EAA6CgP,QAAQ,CAAC5E,SAAtD,EAAiEjN,IAAI,CAACJ,MAAtE,EAA8EiS,QAAQ,CAAC7L,OAAvF,CAAP;IACH,CAFD;;IAGA,MAAMkN,qBAAqB,GAAG,UAAUlT,IAAV,EAAgB;MAC1C,OAAOkS,0BAA0B,CAAC/L,IAA3B,CAAgC0L,QAAQ,CAAChP,MAAzC,EAAiDgP,QAAQ,CAAC5E,SAA1D,EAAqEjN,IAAI,CAACJ,MAA1E,EAAkFiS,QAAQ,CAAC7L,OAA3F,CAAP;IACH,CAFD;;IAGA,MAAMmN,qBAAqB,GAAG,UAAUnT,IAAV,EAAgB;MAC1C,OAAO+R,yBAAyB,CAAC5L,IAA1B,CAA+BnG,IAAI,CAAC6C,MAApC,EAA4C7C,IAAI,CAACiN,SAAjD,EAA4DjN,IAAI,CAACJ,MAAjE,EAAyEI,IAAI,CAACgG,OAA9E,CAAP;IACH,CAFD;;IAGA,MAAMnE,cAAc,GAAG0P,iBAAiB,GAAGgB,oBAAH,GAA0BU,uBAAlE;IACA,MAAMhR,YAAY,GAAGsP,iBAAiB,GAAGkB,kBAAH,GAAwBU,qBAA9D;;IACA,MAAMC,6BAA6B,GAAG,UAAUpT,IAAV,EAAgB2C,QAAhB,EAA0B;MAC5D,MAAM0Q,cAAc,GAAG,OAAO1Q,QAA9B;MACA,OAAS0Q,cAAc,KAAK,UAAnB,IAAiCrT,IAAI,CAACb,QAAL,KAAkBwD,QAApD,IACH0Q,cAAc,KAAK,QAAnB,IAA+BrT,IAAI,CAAC4Q,gBAAL,KAA0BjO,QAD9D;IAEH,CAJD;;IAKA,MAAM2Q,OAAO,GAAGzD,YAAY,EAAE0D,IAAd,IAAsBH,6BAAtC;IACA,MAAMI,eAAe,GAAGpJ,IAAI,CAACE,UAAU,CAAC,kBAAD,CAAX,CAA5B;;IACA,MAAMmJ,aAAa,GAAG/I,OAAO,CAACJ,UAAU,CAAC,gBAAD,CAAX,CAA7B;;IACA,SAASoJ,wBAAT,CAAkC1N,OAAlC,EAA2C;MACvC,IAAI,OAAOA,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,KAAK,IAA/C,EAAqD;QACjD;QACA;QACA;QACA,MAAM2N,UAAU,GAAG,EAAE,GAAG3N;QAAL,CAAnB,CAJiD,CAKjD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QACA,IAAIA,OAAO,CAAC4N,MAAZ,EAAoB;UAChBD,UAAU,CAACC,MAAX,GAAoB5N,OAAO,CAAC4N,MAA5B;QACH;;QACD,OAAOD,UAAP;MACH;;MACD,OAAO3N,OAAP;IACH;;IACD,MAAM6N,eAAe,GAAG,UAAUC,cAAV,EAA0BC,SAA1B,EAAqCC,gBAArC,EAAuDC,cAAvD,EAAuEtC,YAAY,GAAG,KAAtF,EAA6FQ,OAAO,GAAG,KAAvG,EAA8G;MAClI,OAAO,YAAY;QACf,MAAMtP,MAAM,GAAG,QAAQ6H,OAAvB;QACA,IAAIuC,SAAS,GAAGzN,SAAS,CAAC,CAAD,CAAzB;;QACA,IAAIqQ,YAAY,IAAIA,YAAY,CAACqE,iBAAjC,EAAoD;UAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAb,CAA+BjH,SAA/B,CAAZ;QACH;;QACD,IAAItK,QAAQ,GAAGnD,SAAS,CAAC,CAAD,CAAxB;;QACA,IAAI,CAACmD,QAAL,EAAe;UACX,OAAOmR,cAAc,CAACzO,KAAf,CAAqB,IAArB,EAA2B7F,SAA3B,CAAP;QACH;;QACD,IAAI8L,MAAM,IAAI2B,SAAS,KAAK,mBAA5B,EAAiD;UAC7C;UACA,OAAO6G,cAAc,CAACzO,KAAf,CAAqB,IAArB,EAA2B7F,SAA3B,CAAP;QACH,CAbc,CAcf;QACA;;;QACA,IAAI2U,qBAAqB,GAAG,KAA5B;;QACA,IAAI,OAAOxR,QAAP,KAAoB,UAAxB,EAAoC;UAChC;UACA;UACA;UACA,IAAI,CAACA,QAAQ,CAACgO,WAAd,EAA2B;YACvB,OAAOmD,cAAc,CAACzO,KAAf,CAAqB,IAArB,EAA2B7F,SAA3B,CAAP;UACH;;UACD2U,qBAAqB,GAAG,IAAxB;QACH;;QACD,IAAI3C,eAAe,IAAI,CAACA,eAAe,CAACsC,cAAD,EAAiBnR,QAAjB,EAA2BE,MAA3B,EAAmCrD,SAAnC,CAAvC,EAAsF;UAClF;QACH;;QACD,MAAM6S,OAAO,GAAG,CAAC,CAACoB,aAAF,IAAmBA,aAAa,CAAC5E,OAAd,CAAsB5B,SAAtB,MAAqC,CAAC,CAAzE;QACA,MAAMjH,OAAO,GAAG0N,wBAAwB,CAACtB,yBAAyB,CAAC5S,SAAS,CAAC,CAAD,CAAV,EAAe6S,OAAf,CAA1B,CAAxC;QACA,MAAMuB,MAAM,GAAG5N,OAAO,EAAE4N,MAAxB;;QACA,IAAIA,MAAM,EAAEQ,OAAZ,EAAqB;UACjB;UACA;QACH;;QACD,IAAIZ,eAAJ,EAAqB;UACjB;UACA,KAAK,IAAIjR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiR,eAAe,CAAChR,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;YAC7C,IAAI0K,SAAS,KAAKuG,eAAe,CAACjR,CAAD,CAAjC,EAAsC;cAClC,IAAI8P,OAAJ,EAAa;gBACT,OAAOyB,cAAc,CAAC3N,IAAf,CAAoBtD,MAApB,EAA4BoK,SAA5B,EAAuCtK,QAAvC,EAAiDqD,OAAjD,CAAP;cACH,CAFD,MAGK;gBACD,OAAO8N,cAAc,CAACzO,KAAf,CAAqB,IAArB,EAA2B7F,SAA3B,CAAP;cACH;YACJ;UACJ;QACJ;;QACD,MAAM8S,OAAO,GAAG,CAACtM,OAAD,GAAW,KAAX,GAAmB,OAAOA,OAAP,KAAmB,SAAnB,GAA+B,IAA/B,GAAsCA,OAAO,CAACsM,OAAjF;QACA,MAAMzB,IAAI,GAAG7K,OAAO,IAAI,OAAOA,OAAP,KAAmB,QAA9B,GAAyCA,OAAO,CAAC6K,IAAjD,GAAwD,KAArE;QACA,MAAMpT,IAAI,GAAG2M,IAAI,CAAC1M,OAAlB;QACA,IAAIgV,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAD,CAA3C;;QACA,IAAI,CAACyF,gBAAL,EAAuB;UACnBpD,iBAAiB,CAACrC,SAAD,EAAYsC,iBAAZ,CAAjB;UACAmD,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAD,CAAvC;QACH;;QACD,MAAM0F,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAGrI,QAAH,GAAcC,SAAtB,CAAxC;QACA,IAAI0I,aAAa,GAAG/P,MAAM,CAAC8P,eAAD,CAA1B;QACA,IAAIH,UAAU,GAAG,KAAjB;;QACA,IAAII,aAAJ,EAAmB;UACf;UACAJ,UAAU,GAAG,IAAb;;UACA,IAAIrU,cAAJ,EAAoB;YAChB,KAAK,IAAIoE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqQ,aAAa,CAACpQ,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;cAC3C,IAAI+Q,OAAO,CAACV,aAAa,CAACrQ,CAAD,CAAd,EAAmBI,QAAnB,CAAX,EAAyC;gBACrC;gBACA;cACH;YACJ;UACJ;QACJ,CAXD,MAYK;UACDiQ,aAAa,GAAG/P,MAAM,CAAC8P,eAAD,CAAN,GAA0B,EAA1C;QACH;;QACD,IAAIvT,MAAJ;QACA,MAAMiV,eAAe,GAAGxR,MAAM,CAACrE,WAAP,CAAmB,MAAnB,CAAxB;QACA,MAAM8V,YAAY,GAAGpF,aAAa,CAACmF,eAAD,CAAlC;;QACA,IAAIC,YAAJ,EAAkB;UACdlV,MAAM,GAAGkV,YAAY,CAACrH,SAAD,CAArB;QACH;;QACD,IAAI,CAAC7N,MAAL,EAAa;UACTA,MAAM,GACFiV,eAAe,GACXN,SADJ,IAEKxE,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAD,CAApB,GAAkCA,SAFxD,CADJ;QAIH,CAtFc,CAuFf;QACA;QACA;QACA;QACA;;;QACA4E,QAAQ,CAAC7L,OAAT,GAAmBA,OAAnB;;QACA,IAAI6K,IAAJ,EAAU;UACN;UACA;UACA;UACAgB,QAAQ,CAAC7L,OAAT,CAAiB6K,IAAjB,GAAwB,KAAxB;QACH;;QACDgB,QAAQ,CAAChP,MAAT,GAAkBA,MAAlB;QACAgP,QAAQ,CAACS,OAAT,GAAmBA,OAAnB;QACAT,QAAQ,CAAC5E,SAAT,GAAqBA,SAArB;QACA4E,QAAQ,CAACW,UAAT,GAAsBA,UAAtB;QACA,MAAMpS,IAAI,GAAGmR,iBAAiB,GAAGvC,8BAAH,GAAoC/N,SAAlE,CAvGe,CAwGf;;QACA,IAAIb,IAAJ,EAAU;UACNA,IAAI,CAACyR,QAAL,GAAgBA,QAAhB;QACH;;QACD,IAAI+B,MAAJ,EAAY;UACR;UACA;UACA;UACA/B,QAAQ,CAAC7L,OAAT,CAAiB4N,MAAjB,GAA0B3S,SAA1B;QACH,CAjHc,CAkHf;QACA;QACA;QACA;;;QACA,MAAMjB,IAAI,GAAGvC,IAAI,CAACyE,iBAAL,CAAuB9C,MAAvB,EAA+BuD,QAA/B,EAAyCvC,IAAzC,EAA+C4T,gBAA/C,EAAiEC,cAAjE,CAAb;;QACA,IAAIL,MAAJ,EAAY;UACR;UACA/B,QAAQ,CAAC7L,OAAT,CAAiB4N,MAAjB,GAA0BA,MAA1B,CAFQ,CAGR;UACA;UACA;;UACA,MAAMW,OAAO,GAAG,MAAMvU,IAAI,CAACvC,IAAL,CAAU0E,UAAV,CAAqBnC,IAArB,CAAtB;;UACA8T,cAAc,CAAC3N,IAAf,CAAoByN,MAApB,EAA4B,OAA5B,EAAqCW,OAArC,EAA8C;YAAE1D,IAAI,EAAE;UAAR,CAA9C,EAPQ,CAQR;UACA;UACA;UACA;;UACA7Q,IAAI,CAAC+S,mBAAL,GAA2B,MAAMa,MAAM,CAACxG,mBAAP,CAA2B,OAA3B,EAAoCmH,OAApC,CAAjC;QACH,CApIc,CAqIf;QACA;;;QACA1C,QAAQ,CAAChP,MAAT,GAAkB,IAAlB,CAvIe,CAwIf;;QACA,IAAIzC,IAAJ,EAAU;UACNA,IAAI,CAACyR,QAAL,GAAgB,IAAhB;QACH,CA3Ic,CA4If;QACA;;;QACA,IAAIhB,IAAJ,EAAU;UACNgB,QAAQ,CAAC7L,OAAT,CAAiB6K,IAAjB,GAAwB,IAAxB;QACH;;QACD,IAAI,OAAO7Q,IAAI,CAACgG,OAAZ,KAAwB,SAA5B,EAAuC;UACnC;UACA;UACA;UACAhG,IAAI,CAACgG,OAAL,GAAeA,OAAf;QACH;;QACDhG,IAAI,CAAC6C,MAAL,GAAcA,MAAd;QACA7C,IAAI,CAACsS,OAAL,GAAeA,OAAf;QACAtS,IAAI,CAACiN,SAAL,GAAiBA,SAAjB;;QACA,IAAIkH,qBAAJ,EAA2B;UACvB;UACAnU,IAAI,CAAC4Q,gBAAL,GAAwBjO,QAAxB;QACH;;QACD,IAAI,CAACwP,OAAL,EAAc;UACVS,aAAa,CAACrN,IAAd,CAAmBvF,IAAnB;QACH,CAFD,MAGK;UACD4S,aAAa,CAAC4B,OAAd,CAAsBxU,IAAtB;QACH;;QACD,IAAI2R,YAAJ,EAAkB;UACd,OAAO9O,MAAP;QACH;MACJ,CAvKD;IAwKH,CAzKD;;IA0KAkL,KAAK,CAAC+B,kBAAD,CAAL,GAA4B+D,eAAe,CAAC/B,sBAAD,EAAyBvB,yBAAzB,EAAoD1O,cAApD,EAAoEI,YAApE,EAAkF0P,YAAlF,CAA3C;;IACA,IAAIO,0BAAJ,EAAgC;MAC5BnE,KAAK,CAACyC,sBAAD,CAAL,GAAgCqD,eAAe,CAAC3B,0BAAD,EAA6BzB,6BAA7B,EAA4DyC,qBAA5D,EAAmFjR,YAAnF,EAAiG0P,YAAjG,EAA+G,IAA/G,CAA/C;IACH;;IACD5D,KAAK,CAACiC,qBAAD,CAAL,GAA+B,YAAY;MACvC,MAAMnN,MAAM,GAAG,QAAQ6H,OAAvB;MACA,IAAIuC,SAAS,GAAGzN,SAAS,CAAC,CAAD,CAAzB;;MACA,IAAIqQ,YAAY,IAAIA,YAAY,CAACqE,iBAAjC,EAAoD;QAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAb,CAA+BjH,SAA/B,CAAZ;MACH;;MACD,MAAMjH,OAAO,GAAGxG,SAAS,CAAC,CAAD,CAAzB;MACA,MAAM8S,OAAO,GAAG,CAACtM,OAAD,GAAW,KAAX,GAAmB,OAAOA,OAAP,KAAmB,SAAnB,GAA+B,IAA/B,GAAsCA,OAAO,CAACsM,OAAjF;MACA,MAAM3P,QAAQ,GAAGnD,SAAS,CAAC,CAAD,CAA1B;;MACA,IAAI,CAACmD,QAAL,EAAe;QACX,OAAOoP,yBAAyB,CAAC1M,KAA1B,CAAgC,IAAhC,EAAsC7F,SAAtC,CAAP;MACH;;MACD,IAAIgS,eAAe,IACf,CAACA,eAAe,CAACO,yBAAD,EAA4BpP,QAA5B,EAAsCE,MAAtC,EAA8CrD,SAA9C,CADpB,EAC8E;QAC1E;MACH;;MACD,MAAMkT,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAD,CAA7C;MACA,IAAI0F,eAAJ;;MACA,IAAID,gBAAJ,EAAsB;QAClBC,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAGrI,QAAH,GAAcC,SAAtB,CAAlC;MACH;;MACD,MAAM0I,aAAa,GAAGD,eAAe,IAAI9P,MAAM,CAAC8P,eAAD,CAA/C,CArBuC,CAsBvC;MACA;MACA;MACA;;MACA,IAAIC,aAAJ,EAAmB;QACf,KAAK,IAAIrQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqQ,aAAa,CAACpQ,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;UAC3C,MAAMsQ,YAAY,GAAGD,aAAa,CAACrQ,CAAD,CAAlC;;UACA,IAAI+Q,OAAO,CAACT,YAAD,EAAelQ,QAAf,CAAX,EAAqC;YACjCiQ,aAAa,CAACE,MAAd,CAAqBvQ,CAArB,EAAwB,CAAxB,EADiC,CAEjC;;YACAsQ,YAAY,CAACnC,SAAb,GAAyB,IAAzB;;YACA,IAAIkC,aAAa,CAACpQ,MAAd,KAAyB,CAA7B,EAAgC;cAC5B;cACA;cACAqQ,YAAY,CAACG,UAAb,GAA0B,IAA1B;cACAnQ,MAAM,CAAC8P,eAAD,CAAN,GAA0B,IAA1B,CAJ4B,CAK5B;cACA;cACA;cACA;cACA;;cACA,IAAI,CAACL,OAAD,IAAY,OAAOrF,SAAP,KAAqB,QAArC,EAA+C;gBAC3C,MAAMwH,gBAAgB,GAAGtK,kBAAkB,GAAG,aAArB,GAAqC8C,SAA9D;gBACApK,MAAM,CAAC4R,gBAAD,CAAN,GAA2B,IAA3B;cACH;YACJ,CAlBgC,CAmBjC;YACA;YACA;YACA;YACA;;;YACA5B,YAAY,CAACpV,IAAb,CAAkB0E,UAAlB,CAA6B0Q,YAA7B;;YACA,IAAIlB,YAAJ,EAAkB;cACd,OAAO9O,MAAP;YACH;;YACD;UACH;QACJ;MACJ,CA5DsC,CA6DvC;MACA;MACA;MACA;MACA;MACA;;;MACA,OAAOkP,yBAAyB,CAAC1M,KAA1B,CAAgC,IAAhC,EAAsC7F,SAAtC,CAAP;IACH,CApED;;IAqEAuO,KAAK,CAACmC,wBAAD,CAAL,GAAkC,YAAY;MAC1C,MAAMrN,MAAM,GAAG,QAAQ6H,OAAvB;MACA,IAAIuC,SAAS,GAAGzN,SAAS,CAAC,CAAD,CAAzB;;MACA,IAAIqQ,YAAY,IAAIA,YAAY,CAACqE,iBAAjC,EAAoD;QAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAb,CAA+BjH,SAA/B,CAAZ;MACH;;MACD,MAAMkD,SAAS,GAAG,EAAlB;MACA,MAAMc,KAAK,GAAGyD,cAAc,CAAC7R,MAAD,EAAS0M,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAD,CAApB,GAAkCA,SAA5D,CAA5B;;MACA,KAAK,IAAI1K,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0O,KAAK,CAACzO,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;QACnC,MAAMvC,IAAI,GAAGiR,KAAK,CAAC1O,CAAD,CAAlB;QACA,IAAII,QAAQ,GAAG3C,IAAI,CAAC4Q,gBAAL,GAAwB5Q,IAAI,CAAC4Q,gBAA7B,GAAgD5Q,IAAI,CAACb,QAApE;QACAgR,SAAS,CAAC5K,IAAV,CAAe5C,QAAf;MACH;;MACD,OAAOwN,SAAP;IACH,CAdD;;IAeApC,KAAK,CAACqC,mCAAD,CAAL,GAA6C,YAAY;MACrD,MAAMvN,MAAM,GAAG,QAAQ6H,OAAvB;MACA,IAAIuC,SAAS,GAAGzN,SAAS,CAAC,CAAD,CAAzB;;MACA,IAAI,CAACyN,SAAL,EAAgB;QACZ,MAAM0H,IAAI,GAAG7N,MAAM,CAAC6N,IAAP,CAAY9R,MAAZ,CAAb;;QACA,KAAK,IAAIN,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoS,IAAI,CAACnS,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;UAClC,MAAMmK,IAAI,GAAGiI,IAAI,CAACpS,CAAD,CAAjB;UACA,MAAMqS,KAAK,GAAGzF,sBAAsB,CAAC0F,IAAvB,CAA4BnI,IAA5B,CAAd;UACA,IAAIoI,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAD,CAA5B,CAHkC,CAIlC;UACA;UACA;UACA;;UACA,IAAIE,OAAO,IAAIA,OAAO,KAAK,gBAA3B,EAA6C;YACzC,KAAK1E,mCAAL,EAA0CjK,IAA1C,CAA+C,IAA/C,EAAqD2O,OAArD;UACH;QACJ,CAbW,CAcZ;;;QACA,KAAK1E,mCAAL,EAA0CjK,IAA1C,CAA+C,IAA/C,EAAqD,gBAArD;MACH,CAhBD,MAiBK;QACD,IAAI0J,YAAY,IAAIA,YAAY,CAACqE,iBAAjC,EAAoD;UAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAb,CAA+BjH,SAA/B,CAAZ;QACH;;QACD,MAAMyF,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAD,CAA7C;;QACA,IAAIyF,gBAAJ,EAAsB;UAClB,MAAMC,eAAe,GAAGD,gBAAgB,CAACxI,SAAD,CAAxC;UACA,MAAM6K,sBAAsB,GAAGrC,gBAAgB,CAACzI,QAAD,CAA/C;UACA,MAAMgH,KAAK,GAAGpO,MAAM,CAAC8P,eAAD,CAApB;UACA,MAAMqC,YAAY,GAAGnS,MAAM,CAACkS,sBAAD,CAA3B;;UACA,IAAI9D,KAAJ,EAAW;YACP,MAAMgE,WAAW,GAAGhE,KAAK,CAACrH,KAAN,EAApB;;YACA,KAAK,IAAIrH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0S,WAAW,CAACzS,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;cACzC,MAAMvC,IAAI,GAAGiV,WAAW,CAAC1S,CAAD,CAAxB;cACA,IAAII,QAAQ,GAAG3C,IAAI,CAAC4Q,gBAAL,GAAwB5Q,IAAI,CAAC4Q,gBAA7B,GAAgD5Q,IAAI,CAACb,QAApE;cACA,KAAK6Q,qBAAL,EAA4B7J,IAA5B,CAAiC,IAAjC,EAAuC8G,SAAvC,EAAkDtK,QAAlD,EAA4D3C,IAAI,CAACgG,OAAjE;YACH;UACJ;;UACD,IAAIgP,YAAJ,EAAkB;YACd,MAAMC,WAAW,GAAGD,YAAY,CAACpL,KAAb,EAApB;;YACA,KAAK,IAAIrH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0S,WAAW,CAACzS,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;cACzC,MAAMvC,IAAI,GAAGiV,WAAW,CAAC1S,CAAD,CAAxB;cACA,IAAII,QAAQ,GAAG3C,IAAI,CAAC4Q,gBAAL,GAAwB5Q,IAAI,CAAC4Q,gBAA7B,GAAgD5Q,IAAI,CAACb,QAApE;cACA,KAAK6Q,qBAAL,EAA4B7J,IAA5B,CAAiC,IAAjC,EAAuC8G,SAAvC,EAAkDtK,QAAlD,EAA4D3C,IAAI,CAACgG,OAAjE;YACH;UACJ;QACJ;MACJ;;MACD,IAAI2L,YAAJ,EAAkB;QACd,OAAO,IAAP;MACH;IACJ,CAnDD,CA1agD,CA8dhD;;;IACAzI,qBAAqB,CAAC6E,KAAK,CAAC+B,kBAAD,CAAN,EAA4BgC,sBAA5B,CAArB;IACA5I,qBAAqB,CAAC6E,KAAK,CAACiC,qBAAD,CAAN,EAA+B+B,yBAA/B,CAArB;;IACA,IAAIE,wBAAJ,EAA8B;MAC1B/I,qBAAqB,CAAC6E,KAAK,CAACqC,mCAAD,CAAN,EAA6C6B,wBAA7C,CAArB;IACH;;IACD,IAAID,eAAJ,EAAqB;MACjB9I,qBAAqB,CAAC6E,KAAK,CAACmC,wBAAD,CAAN,EAAkC8B,eAAlC,CAArB;IACH;;IACD,OAAO,IAAP;EACH;;EACD,IAAIkD,OAAO,GAAG,EAAd;;EACA,KAAK,IAAI3S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqN,IAAI,CAACpN,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;IAClC2S,OAAO,CAAC3S,CAAD,CAAP,GAAa+O,uBAAuB,CAAC1B,IAAI,CAACrN,CAAD,CAAL,EAAUsN,YAAV,CAApC;EACH;;EACD,OAAOqF,OAAP;AACH;;AACD,SAASR,cAAT,CAAwB7R,MAAxB,EAAgCoK,SAAhC,EAA2C;EACvC,IAAI,CAACA,SAAL,EAAgB;IACZ,MAAMkI,UAAU,GAAG,EAAnB;;IACA,KAAK,IAAIzI,IAAT,IAAiB7J,MAAjB,EAAyB;MACrB,MAAM+R,KAAK,GAAGzF,sBAAsB,CAAC0F,IAAvB,CAA4BnI,IAA5B,CAAd;MACA,IAAIoI,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAD,CAA5B;;MACA,IAAIE,OAAO,KAAK,CAAC7H,SAAD,IAAc6H,OAAO,KAAK7H,SAA/B,CAAX,EAAsD;QAClD,MAAMgE,KAAK,GAAGpO,MAAM,CAAC6J,IAAD,CAApB;;QACA,IAAIuE,KAAJ,EAAW;UACP,KAAK,IAAI1O,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0O,KAAK,CAACzO,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;YACnC4S,UAAU,CAAC5P,IAAX,CAAgB0L,KAAK,CAAC1O,CAAD,CAArB;UACH;QACJ;MACJ;IACJ;;IACD,OAAO4S,UAAP;EACH;;EACD,IAAIxC,eAAe,GAAG1D,oBAAoB,CAAChC,SAAD,CAA1C;;EACA,IAAI,CAAC0F,eAAL,EAAsB;IAClBrD,iBAAiB,CAACrC,SAAD,CAAjB;IACA0F,eAAe,GAAG1D,oBAAoB,CAAChC,SAAD,CAAtC;EACH;;EACD,MAAMmI,iBAAiB,GAAGvS,MAAM,CAAC8P,eAAe,CAACzI,SAAD,CAAhB,CAAhC;EACA,MAAMmL,gBAAgB,GAAGxS,MAAM,CAAC8P,eAAe,CAAC1I,QAAD,CAAhB,CAA/B;;EACA,IAAI,CAACmL,iBAAL,EAAwB;IACpB,OAAOC,gBAAgB,GAAGA,gBAAgB,CAACzL,KAAjB,EAAH,GAA8B,EAArD;EACH,CAFD,MAGK;IACD,OAAOyL,gBAAgB,GACjBD,iBAAiB,CAACE,MAAlB,CAAyBD,gBAAzB,CADiB,GAEjBD,iBAAiB,CAACxL,KAAlB,EAFN;EAGH;AACJ;;AACD,SAASpB,mBAAT,CAA6B9L,MAA7B,EAAqCiT,GAArC,EAA0C;EACtC,MAAM4F,KAAK,GAAG7Y,MAAM,CAAC,OAAD,CAApB;;EACA,IAAI6Y,KAAK,IAAIA,KAAK,CAACxO,SAAnB,EAA8B;IAC1B4I,GAAG,CAACvH,WAAJ,CAAgBmN,KAAK,CAACxO,SAAtB,EAAiC,0BAAjC,EAA8DpE,QAAD,IAAc,UAAUsD,IAAV,EAAgBG,IAAhB,EAAsB;MAC7FH,IAAI,CAACoJ,4BAAD,CAAJ,GAAqC,IAArC,CAD6F,CAE7F;MACA;MACA;;MACA1M,QAAQ,IAAIA,QAAQ,CAAC0C,KAAT,CAAeY,IAAf,EAAqBG,IAArB,CAAZ;IACH,CAND;EAOH;AACJ;AAED;AACA;AACA;AACA;;;AACA,SAASoP,mBAAT,CAA6B9Y,MAA7B,EAAqCiT,GAArC,EAA0C;EACtCA,GAAG,CAACvH,WAAJ,CAAgB1L,MAAhB,EAAwB,gBAAxB,EAA2CiG,QAAD,IAAc;IACpD,OAAO,UAAUsD,IAAV,EAAgBG,IAAhB,EAAsB;MACzBgE,IAAI,CAAC1M,OAAL,CAAakE,iBAAb,CAA+B,gBAA/B,EAAiDwE,IAAI,CAAC,CAAD,CAArD;IACH,CAFD;EAGH,CAJD;AAKH;AAED;AACA;AACA;AACA;;;AACA,MAAMqP,UAAU,GAAGnL,UAAU,CAAC,UAAD,CAA7B;;AACA,SAASoL,UAAT,CAAoBlL,MAApB,EAA4BmL,OAA5B,EAAqCC,UAArC,EAAiDC,UAAjD,EAA6D;EACzD,IAAIzH,SAAS,GAAG,IAAhB;EACA,IAAI0H,WAAW,GAAG,IAAlB;EACAH,OAAO,IAAIE,UAAX;EACAD,UAAU,IAAIC,UAAd;EACA,MAAME,eAAe,GAAG,EAAxB;;EACA,SAASvU,YAAT,CAAsBxB,IAAtB,EAA4B;IACxB,MAAMI,IAAI,GAAGJ,IAAI,CAACI,IAAlB;;IACAA,IAAI,CAACgG,IAAL,CAAU,CAAV,IAAe,YAAY;MACvB,OAAOpG,IAAI,CAACJ,MAAL,CAAYyF,KAAZ,CAAkB,IAAlB,EAAwB7F,SAAxB,CAAP;IACH,CAFD;;IAGA,MAAMwW,UAAU,GAAG5H,SAAS,CAAC/I,KAAV,CAAgBmF,MAAhB,EAAwBpK,IAAI,CAACgG,IAA7B,CAAnB,CALwB,CAMxB;IACA;IACA;;IACA,IAAI2I,QAAQ,CAACiH,UAAD,CAAZ,EAA0B;MACtB5V,IAAI,CAACyG,QAAL,GAAgBmP,UAAhB;IACH,CAFD,MAGK;MACD5V,IAAI,CAAC6V,MAAL,GAAcD,UAAd,CADC,CAED;;MACA5V,IAAI,CAACE,aAAL,GAAqBwO,UAAU,CAACkH,UAAU,CAACE,OAAZ,CAA/B;IACH;;IACD,OAAOlW,IAAP;EACH;;EACD,SAASmW,SAAT,CAAmBnW,IAAnB,EAAyB;IACrB,MAAM;MAAEiW,MAAF;MAAUpP;IAAV,IAAuB7G,IAAI,CAACI,IAAlC;IACA,OAAO0V,WAAW,CAAC3P,IAAZ,CAAiBqE,MAAjB,EAAyByL,MAAM,IAAIpP,QAAnC,CAAP;EACH;;EACDuH,SAAS,GAAGhG,WAAW,CAACoC,MAAD,EAASmL,OAAT,EAAmBhT,QAAD,IAAc,UAAUsD,IAAV,EAAgBG,IAAhB,EAAsB;IACzE,IAAI0I,UAAU,CAAC1I,IAAI,CAAC,CAAD,CAAL,CAAd,EAAyB;MACrB,MAAMJ,OAAO,GAAG;QACZ1F,aAAa,EAAE,KADH;QAEZD,UAAU,EAAEwV,UAAU,KAAK,UAFf;QAGZO,KAAK,EAAEP,UAAU,KAAK,SAAf,IAA4BA,UAAU,KAAK,UAA3C,GAAwDzP,IAAI,CAAC,CAAD,CAAJ,IAAW,CAAnE,GAAuEnF,SAHlE;QAIZmF,IAAI,EAAEA;MAJM,CAAhB;MAMA,MAAMjH,QAAQ,GAAGiH,IAAI,CAAC,CAAD,CAArB;;MACAA,IAAI,CAAC,CAAD,CAAJ,GAAU,SAASiQ,KAAT,GAAiB;QACvB,IAAI;UACA,OAAOlX,QAAQ,CAACkG,KAAT,CAAe,IAAf,EAAqB7F,SAArB,CAAP;QACH,CAFD,SAGQ;UACJ;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAM;YAAEyW,MAAF;YAAUpP,QAAV;YAAoBxG,UAApB;YAAgCC;UAAhC,IAAkD0F,OAAxD;;UACA,IAAI,CAAC3F,UAAD,IAAe,CAACC,aAApB,EAAmC;YAC/B,IAAIuG,QAAJ,EAAc;cACV;cACA;cACA,OAAOkP,eAAe,CAAClP,QAAD,CAAtB;YACH,CAJD,MAKK,IAAIoP,MAAJ,EAAY;cACb;cACA;cACAA,MAAM,CAACR,UAAD,CAAN,GAAqB,IAArB;YACH;UACJ;QACJ;MACJ,CA1BD;;MA2BA,MAAMzV,IAAI,GAAGqK,gCAAgC,CAACsL,OAAD,EAAUvP,IAAI,CAAC,CAAD,CAAd,EAAmBJ,OAAnB,EAA4BxE,YAA5B,EAA0C2U,SAA1C,CAA7C;;MACA,IAAI,CAACnW,IAAL,EAAW;QACP,OAAOA,IAAP;MACH,CAtCoB,CAuCrB;;;MACA,MAAM;QAAE6G,QAAF;QAAYoP,MAAZ;QAAoB3V,aAApB;QAAmCD;MAAnC,IAAkDL,IAAI,CAACI,IAA7D;;MACA,IAAIyG,QAAJ,EAAc;QACV;QACA;QACAkP,eAAe,CAAClP,QAAD,CAAf,GAA4B7G,IAA5B;MACH,CAJD,MAKK,IAAIiW,MAAJ,EAAY;QACb;QACA;QACAA,MAAM,CAACR,UAAD,CAAN,GAAqBzV,IAArB;;QACA,IAAIM,aAAa,IAAI,CAACD,UAAtB,EAAkC;UAC9B,MAAMiW,eAAe,GAAGL,MAAM,CAACC,OAA/B;;UACAD,MAAM,CAACC,OAAP,GAAiB,YAAY;YACzB,MAAM;cAAEzY,IAAF;cAAQ8C;YAAR,IAAkBP,IAAxB;;YACA,IAAIO,KAAK,KAAK,cAAd,EAA8B;cAC1BP,IAAI,CAACuG,MAAL,GAAc,WAAd;;cACA9I,IAAI,CAAC8D,gBAAL,CAAsBvB,IAAtB,EAA4B,CAA5B;YACH,CAHD,MAIK,IAAIO,KAAK,KAAK,SAAd,EAAyB;cAC1BP,IAAI,CAACuG,MAAL,GAAc,YAAd;YACH;;YACD,OAAO+P,eAAe,CAACnQ,IAAhB,CAAqB,IAArB,CAAP;UACH,CAVD;QAWH;MACJ;;MACD,OAAO8P,MAAM,IAAIpP,QAAV,IAAsB7G,IAA7B;IACH,CAlED,MAmEK;MACD;MACA,OAAO2C,QAAQ,CAAC0C,KAAT,CAAemF,MAAf,EAAuBpE,IAAvB,CAAP;IACH;EACJ,CAxEsB,CAAvB;EAyEA0P,WAAW,GAAG1N,WAAW,CAACoC,MAAD,EAASoL,UAAT,EAAsBjT,QAAD,IAAc,UAAUsD,IAAV,EAAgBG,IAAhB,EAAsB;IAC9E,MAAMmQ,EAAE,GAAGnQ,IAAI,CAAC,CAAD,CAAf;IACA,IAAIpG,IAAJ;;IACA,IAAI+O,QAAQ,CAACwH,EAAD,CAAZ,EAAkB;MACd;MACAvW,IAAI,GAAG+V,eAAe,CAACQ,EAAD,CAAtB;MACA,OAAOR,eAAe,CAACQ,EAAD,CAAtB;IACH,CAJD,MAKK;MACD;MACAvW,IAAI,GAAGuW,EAAE,GAAGd,UAAH,CAAT;;MACA,IAAIzV,IAAJ,EAAU;QACNuW,EAAE,CAACd,UAAD,CAAF,GAAiB,IAAjB;MACH,CAFD,MAGK;QACDzV,IAAI,GAAGuW,EAAP;MACH;IACJ;;IACD,IAAIvW,IAAI,EAAEG,IAAV,EAAgB;MACZ,IAAIH,IAAI,CAACgB,QAAT,EAAmB;QACf;QACAhB,IAAI,CAACvC,IAAL,CAAU0E,UAAV,CAAqBnC,IAArB;MACH;IACJ,CALD,MAMK;MACD;MACA2C,QAAQ,CAAC0C,KAAT,CAAemF,MAAf,EAAuBpE,IAAvB;IACH;EACJ,CA5BwB,CAAzB;AA6BH;;AAED,SAASoQ,mBAAT,CAA6B9L,OAA7B,EAAsCiF,GAAtC,EAA2C;EACvC,MAAM;IAAEnE,SAAF;IAAaC;EAAb,IAAuBkE,GAAG,CAACjH,gBAAJ,EAA7B;;EACA,IAAK,CAAC8C,SAAD,IAAc,CAACC,KAAhB,IAA0B,CAACf,OAAO,CAAC,gBAAD,CAAlC,IAAwD,EAAE,oBAAoBA,OAAtB,CAA5D,EAA4F;IACxF;EACH,CAJsC,CAKvC;;;EACA,MAAM+L,SAAS,GAAG,CACd,mBADc,EAEd,sBAFc,EAGd,iBAHc,EAId,0BAJc,EAKd,wBALc,EAMd,sBANc,EAOd,mBAPc,EAQd,0BARc,CAAlB;EAUA9G,GAAG,CAACvG,cAAJ,CAAmBuG,GAAnB,EAAwBjF,OAAO,CAACgM,cAAhC,EAAgD,gBAAhD,EAAkE,QAAlE,EAA4ED,SAA5E;AACH;;AAED,SAASE,gBAAT,CAA0BjM,OAA1B,EAAmCiF,GAAnC,EAAwC;EACpC,IAAIvF,IAAI,CAACuF,GAAG,CAAC7H,MAAJ,CAAW,kBAAX,CAAD,CAAR,EAA0C;IACtC;IACA;EACH;;EACD,MAAM;IAAE8O,UAAF;IAAc3H,oBAAd;IAAoChF,QAApC;IAA8CC,SAA9C;IAAyDC;EAAzD,IAAgFwF,GAAG,CAACjH,gBAAJ,EAAtF,CALoC,CAMpC;;EACA,KAAK,IAAInG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqU,UAAU,CAACpU,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;IACxC,MAAM0K,SAAS,GAAG2J,UAAU,CAACrU,CAAD,CAA5B;IACA,MAAMiN,cAAc,GAAGvC,SAAS,GAAG/C,SAAnC;IACA,MAAMuF,aAAa,GAAGxC,SAAS,GAAGhD,QAAlC;IACA,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAGqF,cAApC;IACA,MAAME,aAAa,GAAGvF,kBAAkB,GAAGsF,aAA3C;IACAR,oBAAoB,CAAChC,SAAD,CAApB,GAAkC,EAAlC;IACAgC,oBAAoB,CAAChC,SAAD,CAApB,CAAgC/C,SAAhC,IAA6CpC,MAA7C;IACAmH,oBAAoB,CAAChC,SAAD,CAApB,CAAgChD,QAAhC,IAA4CyF,aAA5C;EACH;;EACD,MAAMmH,YAAY,GAAGnM,OAAO,CAAC,aAAD,CAA5B;;EACA,IAAI,CAACmM,YAAD,IAAiB,CAACA,YAAY,CAAC9P,SAAnC,EAA8C;IAC1C;EACH;;EACD4I,GAAG,CAACzH,gBAAJ,CAAqBwC,OAArB,EAA8BiF,GAA9B,EAAmC,CAACkH,YAAY,IAAIA,YAAY,CAAC9P,SAA9B,CAAnC;EACA,OAAO,IAAP;AACH;;AACD,SAAS+P,UAAT,CAAoBpa,MAApB,EAA4BiT,GAA5B,EAAiC;EAC7BA,GAAG,CAACnH,mBAAJ,CAAwB9L,MAAxB,EAAgCiT,GAAhC;AACH;AAED;AACA;AACA;AACA;;;AACA,SAAS1G,gBAAT,CAA0BpG,MAA1B,EAAkC0K,YAAlC,EAAgDwJ,gBAAhD,EAAkE;EAC9D,IAAI,CAACA,gBAAD,IAAqBA,gBAAgB,CAACvU,MAAjB,KAA4B,CAArD,EAAwD;IACpD,OAAO+K,YAAP;EACH;;EACD,MAAMyJ,GAAG,GAAGD,gBAAgB,CAACE,MAAjB,CAAyBC,EAAD,IAAQA,EAAE,CAACrU,MAAH,KAAcA,MAA9C,CAAZ;;EACA,IAAImU,GAAG,CAACxU,MAAJ,KAAe,CAAnB,EAAsB;IAClB,OAAO+K,YAAP;EACH;;EACD,MAAM4J,sBAAsB,GAAGH,GAAG,CAAC,CAAD,CAAH,CAAOD,gBAAtC;EACA,OAAOxJ,YAAY,CAAC0J,MAAb,CAAqBG,EAAD,IAAQD,sBAAsB,CAACtI,OAAvB,CAA+BuI,EAA/B,MAAuC,CAAC,CAApE,CAAP;AACH;;AACD,SAASC,uBAAT,CAAiCxU,MAAjC,EAAyC0K,YAAzC,EAAuDwJ,gBAAvD,EAAyEhQ,SAAzE,EAAoF;EAChF;EACA;EACA,IAAI,CAAClE,MAAL,EAAa;IACT;EACH;;EACD,MAAMyU,kBAAkB,GAAGrO,gBAAgB,CAACpG,MAAD,EAAS0K,YAAT,EAAuBwJ,gBAAvB,CAA3C;EACA5O,iBAAiB,CAACtF,MAAD,EAASyU,kBAAT,EAA6BvQ,SAA7B,CAAjB;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASwQ,eAAT,CAAyB1U,MAAzB,EAAiC;EAC7B,OAAOiE,MAAM,CAAC0Q,mBAAP,CAA2B3U,MAA3B,EACFoU,MADE,CACMpa,IAAD,IAAUA,IAAI,CAAC4a,UAAL,CAAgB,IAAhB,KAAyB5a,IAAI,CAAC2F,MAAL,GAAc,CADtD,EAEFkV,GAFE,CAEG7a,IAAD,IAAUA,IAAI,CAAC8a,SAAL,CAAe,CAAf,CAFZ,CAAP;AAGH;;AACD,SAASC,uBAAT,CAAiCjI,GAAjC,EAAsCjF,OAAtC,EAA+C;EAC3C,IAAIY,MAAM,IAAI,CAACG,KAAf,EAAsB;IAClB;EACH;;EACD,IAAIrB,IAAI,CAACuF,GAAG,CAAC7H,MAAJ,CAAW,aAAX,CAAD,CAAR,EAAqC;IACjC;IACA;EACH;;EACD,MAAMiP,gBAAgB,GAAGrM,OAAO,CAAC,6BAAD,CAAhC,CAR2C,CAS3C;;EACA,IAAImN,YAAY,GAAG,EAAnB;;EACA,IAAIrM,SAAJ,EAAe;IACX,MAAMf,cAAc,GAAGD,MAAvB;IACAqN,YAAY,GAAGA,YAAY,CAACvC,MAAb,CAAoB,CAC/B,UAD+B,EAE/B,YAF+B,EAG/B,SAH+B,EAI/B,aAJ+B,EAK/B,iBAL+B,EAM/B,kBAN+B,EAO/B,qBAP+B,EAQ/B,kBAR+B,EAS/B,mBAT+B,EAU/B,oBAV+B,EAW/B,QAX+B,CAApB,CAAf;IAaA,MAAMwC,qBAAqB,GAAG,EAA9B,CAfW,CAgBX;IACA;IACA;IACA;;IACAT,uBAAuB,CAAC5M,cAAD,EAAiB8M,eAAe,CAAC9M,cAAD,CAAhC,EAAkDsM,gBAAgB,GAAGA,gBAAgB,CAACzB,MAAjB,CAAwBwC,qBAAxB,CAAH,GAAoDf,gBAAtH,EAAwIvN,oBAAoB,CAACiB,cAAD,CAA5J,CAAvB;EACH;;EACDoN,YAAY,GAAGA,YAAY,CAACvC,MAAb,CAAoB,CAC/B,gBAD+B,EAE/B,2BAF+B,EAG/B,UAH+B,EAI/B,YAJ+B,EAK/B,kBAL+B,EAM/B,aAN+B,EAO/B,gBAP+B,EAQ/B,WAR+B,EAS/B,WAT+B,CAApB,CAAf;;EAWA,KAAK,IAAI/S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsV,YAAY,CAACrV,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;IAC1C,MAAMM,MAAM,GAAG6H,OAAO,CAACmN,YAAY,CAACtV,CAAD,CAAb,CAAtB;IACAM,MAAM,EAAEkE,SAAR,IACIsQ,uBAAuB,CAACxU,MAAM,CAACkE,SAAR,EAAmBwQ,eAAe,CAAC1U,MAAM,CAACkE,SAAR,CAAlC,EAAsDgQ,gBAAtD,CAD3B;EAEH;AACJ;AAED;AACA;AACA;AACA;;;AACA,SAASgB,YAAT,CAAsB3N,IAAtB,EAA4B;EACxBA,IAAI,CAACrM,YAAL,CAAkB,QAAlB,EAA6BrB,MAAD,IAAY;IACpC,MAAMsb,WAAW,GAAGtb,MAAM,CAAC0N,IAAI,CAACxN,UAAL,CAAgB,aAAhB,CAAD,CAA1B;;IACA,IAAIob,WAAJ,EAAiB;MACbA,WAAW;IACd;EACJ,CALD;;EAMA5N,IAAI,CAACrM,YAAL,CAAkB,QAAlB,EAA6BrB,MAAD,IAAY;IACpC,MAAMyO,GAAG,GAAG,KAAZ;IACA,MAAM8M,KAAK,GAAG,OAAd;IACAvC,UAAU,CAAChZ,MAAD,EAASyO,GAAT,EAAc8M,KAAd,EAAqB,SAArB,CAAV;IACAvC,UAAU,CAAChZ,MAAD,EAASyO,GAAT,EAAc8M,KAAd,EAAqB,UAArB,CAAV;IACAvC,UAAU,CAAChZ,MAAD,EAASyO,GAAT,EAAc8M,KAAd,EAAqB,WAArB,CAAV;EACH,CAND;;EAOA7N,IAAI,CAACrM,YAAL,CAAkB,uBAAlB,EAA4CrB,MAAD,IAAY;IACnDgZ,UAAU,CAAChZ,MAAD,EAAS,SAAT,EAAoB,QAApB,EAA8B,gBAA9B,CAAV;IACAgZ,UAAU,CAAChZ,MAAD,EAAS,YAAT,EAAuB,WAAvB,EAAoC,gBAApC,CAAV;IACAgZ,UAAU,CAAChZ,MAAD,EAAS,eAAT,EAA0B,cAA1B,EAA0C,gBAA1C,CAAV;EACH,CAJD;;EAKA0N,IAAI,CAACrM,YAAL,CAAkB,UAAlB,EAA8B,CAACrB,MAAD,EAAS0N,IAAT,KAAkB;IAC5C,MAAM8N,eAAe,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,SAApB,CAAxB;;IACA,KAAK,IAAI3V,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2V,eAAe,CAAC1V,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;MAC7C,MAAM1F,IAAI,GAAGqb,eAAe,CAAC3V,CAAD,CAA5B;MACA6F,WAAW,CAAC1L,MAAD,EAASG,IAAT,EAAe,CAAC8F,QAAD,EAAWmF,MAAX,EAAmBjL,IAAnB,KAA4B;QAClD,OAAO,UAAUsb,CAAV,EAAa/R,IAAb,EAAmB;UACtB,OAAOgE,IAAI,CAAC1M,OAAL,CAAa+B,GAAb,CAAiBkD,QAAjB,EAA2BjG,MAA3B,EAAmC0J,IAAnC,EAAyCvJ,IAAzC,CAAP;QACH,CAFD;MAGH,CAJU,CAAX;IAKH;EACJ,CAVD;;EAWAuN,IAAI,CAACrM,YAAL,CAAkB,aAAlB,EAAiC,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IACpDmH,UAAU,CAACpa,MAAD,EAASiT,GAAT,CAAV;IACAgH,gBAAgB,CAACja,MAAD,EAASiT,GAAT,CAAhB,CAFoD,CAGpD;;IACA,MAAMyI,yBAAyB,GAAG1b,MAAM,CAAC,2BAAD,CAAxC;;IACA,IAAI0b,yBAAyB,IAAIA,yBAAyB,CAACrR,SAA3D,EAAsE;MAClE4I,GAAG,CAACzH,gBAAJ,CAAqBxL,MAArB,EAA6BiT,GAA7B,EAAkC,CAACyI,yBAAyB,CAACrR,SAA3B,CAAlC;IACH;EACJ,CARD;;EASAqD,IAAI,CAACrM,YAAL,CAAkB,kBAAlB,EAAsC,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IACzD5G,UAAU,CAAC,kBAAD,CAAV;IACAA,UAAU,CAAC,wBAAD,CAAV;EACH,CAHD;;EAIAqB,IAAI,CAACrM,YAAL,CAAkB,sBAAlB,EAA0C,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IAC7D5G,UAAU,CAAC,sBAAD,CAAV;EACH,CAFD;;EAGAqB,IAAI,CAACrM,YAAL,CAAkB,YAAlB,EAAgC,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IACnD5G,UAAU,CAAC,YAAD,CAAV;EACH,CAFD;;EAGAqB,IAAI,CAACrM,YAAL,CAAkB,aAAlB,EAAiC,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IACpDiI,uBAAuB,CAACjI,GAAD,EAAMjT,MAAN,CAAvB;EACH,CAFD;;EAGA0N,IAAI,CAACrM,YAAL,CAAkB,gBAAlB,EAAoC,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IACvD6G,mBAAmB,CAAC9Z,MAAD,EAASiT,GAAT,CAAnB;EACH,CAFD;;EAGAvF,IAAI,CAACrM,YAAL,CAAkB,KAAlB,EAAyB,CAACrB,MAAD,EAAS0N,IAAT,KAAkB;IACvC;IACAiO,QAAQ,CAAC3b,MAAD,CAAR;IACA,MAAM4b,QAAQ,GAAGhO,UAAU,CAAC,SAAD,CAA3B;IACA,MAAMiO,QAAQ,GAAGjO,UAAU,CAAC,SAAD,CAA3B;IACA,MAAMkO,YAAY,GAAGlO,UAAU,CAAC,aAAD,CAA/B;IACA,MAAMmO,aAAa,GAAGnO,UAAU,CAAC,cAAD,CAAhC;IACA,MAAMoO,OAAO,GAAGpO,UAAU,CAAC,QAAD,CAA1B;IACA,MAAMqO,0BAA0B,GAAGrO,UAAU,CAAC,yBAAD,CAA7C;;IACA,SAAS+N,QAAT,CAAkB7N,MAAlB,EAA0B;MACtB,MAAMoO,cAAc,GAAGpO,MAAM,CAAC,gBAAD,CAA7B;;MACA,IAAI,CAACoO,cAAL,EAAqB;QACjB;QACA;MACH;;MACD,MAAMC,uBAAuB,GAAGD,cAAc,CAAC7R,SAA/C;;MACA,SAAS+R,eAAT,CAAyBjW,MAAzB,EAAiC;QAC7B,OAAOA,MAAM,CAACyV,QAAD,CAAb;MACH;;MACD,IAAIS,cAAc,GAAGF,uBAAuB,CAAC9O,8BAAD,CAA5C;MACA,IAAIiP,iBAAiB,GAAGH,uBAAuB,CAAC7O,iCAAD,CAA/C;;MACA,IAAI,CAAC+O,cAAL,EAAqB;QACjB,MAAMX,yBAAyB,GAAG5N,MAAM,CAAC,2BAAD,CAAxC;;QACA,IAAI4N,yBAAJ,EAA+B;UAC3B,MAAMa,kCAAkC,GAAGb,yBAAyB,CAACrR,SAArE;UACAgS,cAAc,GAAGE,kCAAkC,CAAClP,8BAAD,CAAnD;UACAiP,iBAAiB,GAAGC,kCAAkC,CAACjP,iCAAD,CAAtD;QACH;MACJ;;MACD,MAAMkP,kBAAkB,GAAG,kBAA3B;MACA,MAAMC,SAAS,GAAG,WAAlB;;MACA,SAAS3X,YAAT,CAAsBxB,IAAtB,EAA4B;QACxB,MAAMI,IAAI,GAAGJ,IAAI,CAACI,IAAlB;QACA,MAAMyC,MAAM,GAAGzC,IAAI,CAACyC,MAApB;QACAA,MAAM,CAAC4V,aAAD,CAAN,GAAwB,KAAxB;QACA5V,MAAM,CAAC8V,0BAAD,CAAN,GAAqC,KAArC,CAJwB,CAKxB;;QACA,MAAM5M,QAAQ,GAAGlJ,MAAM,CAAC2V,YAAD,CAAvB;;QACA,IAAI,CAACO,cAAL,EAAqB;UACjBA,cAAc,GAAGlW,MAAM,CAACkH,8BAAD,CAAvB;UACAiP,iBAAiB,GAAGnW,MAAM,CAACmH,iCAAD,CAA1B;QACH;;QACD,IAAI+B,QAAJ,EAAc;UACViN,iBAAiB,CAAC7S,IAAlB,CAAuBtD,MAAvB,EAA+BqW,kBAA/B,EAAmDnN,QAAnD;QACH;;QACD,MAAMqN,WAAW,GAAIvW,MAAM,CAAC2V,YAAD,CAAN,GAAuB,MAAM;UAC9C,IAAI3V,MAAM,CAACwW,UAAP,KAAsBxW,MAAM,CAACyW,IAAjC,EAAuC;YACnC;YACA;YACA,IAAI,CAAClZ,IAAI,CAACgU,OAAN,IAAiBvR,MAAM,CAAC4V,aAAD,CAAvB,IAA0CzY,IAAI,CAACO,KAAL,KAAe4Y,SAA7D,EAAwE;cACpE;cACA;cACA;cACA;cACA;cACA;cACA;cACA,MAAMI,SAAS,GAAG1W,MAAM,CAACuH,IAAI,CAACxN,UAAL,CAAgB,WAAhB,CAAD,CAAxB;;cACA,IAAIiG,MAAM,CAAC2W,MAAP,KAAkB,CAAlB,IAAuBD,SAAvB,IAAoCA,SAAS,CAAC/W,MAAV,GAAmB,CAA3D,EAA8D;gBAC1D,MAAMiX,SAAS,GAAGzZ,IAAI,CAACJ,MAAvB;;gBACAI,IAAI,CAACJ,MAAL,GAAc,YAAY;kBACtB;kBACA;kBACA,MAAM2Z,SAAS,GAAG1W,MAAM,CAACuH,IAAI,CAACxN,UAAL,CAAgB,WAAhB,CAAD,CAAxB;;kBACA,KAAK,IAAI2F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgX,SAAS,CAAC/W,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;oBACvC,IAAIgX,SAAS,CAAChX,CAAD,CAAT,KAAiBvC,IAArB,EAA2B;sBACvBuZ,SAAS,CAACzG,MAAV,CAAiBvQ,CAAjB,EAAoB,CAApB;oBACH;kBACJ;;kBACD,IAAI,CAACnC,IAAI,CAACgU,OAAN,IAAiBpU,IAAI,CAACO,KAAL,KAAe4Y,SAApC,EAA+C;oBAC3CM,SAAS,CAACtT,IAAV,CAAenG,IAAf;kBACH;gBACJ,CAZD;;gBAaAuZ,SAAS,CAAChU,IAAV,CAAevF,IAAf;cACH,CAhBD,MAiBK;gBACDA,IAAI,CAACJ,MAAL;cACH;YACJ,CA7BD,MA8BK,IAAI,CAACQ,IAAI,CAACgU,OAAN,IAAiBvR,MAAM,CAAC4V,aAAD,CAAN,KAA0B,KAA/C,EAAsD;cACvD;cACA5V,MAAM,CAAC8V,0BAAD,CAAN,GAAqC,IAArC;YACH;UACJ;QACJ,CAvCD;;QAwCAI,cAAc,CAAC5S,IAAf,CAAoBtD,MAApB,EAA4BqW,kBAA5B,EAAgDE,WAAhD;QACA,MAAMM,UAAU,GAAG7W,MAAM,CAACyV,QAAD,CAAzB;;QACA,IAAI,CAACoB,UAAL,EAAiB;UACb7W,MAAM,CAACyV,QAAD,CAAN,GAAmBtY,IAAnB;QACH;;QACD2Z,UAAU,CAACtU,KAAX,CAAiBxC,MAAjB,EAAyBzC,IAAI,CAACgG,IAA9B;QACAvD,MAAM,CAAC4V,aAAD,CAAN,GAAwB,IAAxB;QACA,OAAOzY,IAAP;MACH;;MACD,SAAS4Z,mBAAT,GAA+B,CAAG;;MAClC,SAASzD,SAAT,CAAmBnW,IAAnB,EAAyB;QACrB,MAAMI,IAAI,GAAGJ,IAAI,CAACI,IAAlB,CADqB,CAErB;QACA;;QACAA,IAAI,CAACgU,OAAL,GAAe,IAAf;QACA,OAAOyF,WAAW,CAACxU,KAAZ,CAAkBjF,IAAI,CAACyC,MAAvB,EAA+BzC,IAAI,CAACgG,IAApC,CAAP;MACH;;MACD,MAAM0T,UAAU,GAAG1R,WAAW,CAACyQ,uBAAD,EAA0B,MAA1B,EAAkC,MAAM,UAAU5S,IAAV,EAAgBG,IAAhB,EAAsB;QACxFH,IAAI,CAACsS,QAAD,CAAJ,GAAiBnS,IAAI,CAAC,CAAD,CAAJ,IAAW,KAA5B;QACAH,IAAI,CAACyS,OAAD,CAAJ,GAAgBtS,IAAI,CAAC,CAAD,CAApB;QACA,OAAO0T,UAAU,CAACzU,KAAX,CAAiBY,IAAjB,EAAuBG,IAAvB,CAAP;MACH,CAJ6B,CAA9B;MAKA,MAAM2T,qBAAqB,GAAG,qBAA9B;MACA,MAAMC,iBAAiB,GAAG1P,UAAU,CAAC,mBAAD,CAApC;MACA,MAAM2P,mBAAmB,GAAG3P,UAAU,CAAC,qBAAD,CAAtC;MACA,MAAMqP,UAAU,GAAGvR,WAAW,CAACyQ,uBAAD,EAA0B,MAA1B,EAAkC,MAAM,UAAU5S,IAAV,EAAgBG,IAAhB,EAAsB;QACxF,IAAIgE,IAAI,CAAC1M,OAAL,CAAauc,mBAAb,MAAsC,IAA1C,EAAgD;UAC5C;UACA;UACA;UACA,OAAON,UAAU,CAACtU,KAAX,CAAiBY,IAAjB,EAAuBG,IAAvB,CAAP;QACH;;QACD,IAAIH,IAAI,CAACsS,QAAD,CAAR,EAAoB;UAChB;UACA,OAAOoB,UAAU,CAACtU,KAAX,CAAiBY,IAAjB,EAAuBG,IAAvB,CAAP;QACH,CAHD,MAIK;UACD,MAAMJ,OAAO,GAAG;YACZnD,MAAM,EAAEoD,IADI;YAEZiU,GAAG,EAAEjU,IAAI,CAACyS,OAAD,CAFG;YAGZrY,UAAU,EAAE,KAHA;YAIZ+F,IAAI,EAAEA,IAJM;YAKZgO,OAAO,EAAE;UALG,CAAhB;UAOA,MAAMpU,IAAI,GAAGqK,gCAAgC,CAAC0P,qBAAD,EAAwBH,mBAAxB,EAA6C5T,OAA7C,EAAsDxE,YAAtD,EAAoE2U,SAApE,CAA7C;;UACA,IAAIlQ,IAAI,IACJA,IAAI,CAAC0S,0BAAD,CAAJ,KAAqC,IADrC,IAEA,CAAC3S,OAAO,CAACoO,OAFT,IAGApU,IAAI,CAACO,KAAL,KAAe4Y,SAHnB,EAG8B;YAC1B;YACA;YACA;YACAnZ,IAAI,CAACJ,MAAL;UACH;QACJ;MACJ,CA9B6B,CAA9B;MA+BA,MAAMia,WAAW,GAAGzR,WAAW,CAACyQ,uBAAD,EAA0B,OAA1B,EAAmC,MAAM,UAAU5S,IAAV,EAAgBG,IAAhB,EAAsB;QAC1F,MAAMpG,IAAI,GAAG8Y,eAAe,CAAC7S,IAAD,CAA5B;;QACA,IAAIjG,IAAI,IAAI,OAAOA,IAAI,CAACG,IAAZ,IAAoB,QAAhC,EAA0C;UACtC;UACA;UACA;UACA;UACA,IAAIH,IAAI,CAACgB,QAAL,IAAiB,IAAjB,IAA0BhB,IAAI,CAACI,IAAL,IAAaJ,IAAI,CAACI,IAAL,CAAUgU,OAArD,EAA+D;YAC3D;UACH;;UACDpU,IAAI,CAACvC,IAAL,CAAU0E,UAAV,CAAqBnC,IAArB;QACH,CATD,MAUK,IAAIoK,IAAI,CAAC1M,OAAL,CAAasc,iBAAb,MAAoC,IAAxC,EAA8C;UAC/C;UACA,OAAOH,WAAW,CAACxU,KAAZ,CAAkBY,IAAlB,EAAwBG,IAAxB,CAAP;QACH,CAfyF,CAgB1F;QACA;QACA;;MACH,CAnB8B,CAA/B;IAoBH;EACJ,CAlKD;;EAmKAgE,IAAI,CAACrM,YAAL,CAAkB,aAAlB,EAAkCrB,MAAD,IAAY;IACzC;IACA,IAAIA,MAAM,CAAC,WAAD,CAAN,IAAuBA,MAAM,CAAC,WAAD,CAAN,CAAoByd,WAA/C,EAA4D;MACxDvP,cAAc,CAAClO,MAAM,CAAC,WAAD,CAAN,CAAoByd,WAArB,EAAkC,CAAC,oBAAD,EAAuB,eAAvB,CAAlC,CAAd;IACH;EACJ,CALD;;EAMA/P,IAAI,CAACrM,YAAL,CAAkB,uBAAlB,EAA2C,CAACrB,MAAD,EAAS0N,IAAT,KAAkB;IACzD;IACA,SAASgQ,2BAAT,CAAqCtF,OAArC,EAA8C;MAC1C,OAAO,UAAUuF,CAAV,EAAa;QAChB,MAAMC,UAAU,GAAG5F,cAAc,CAAChY,MAAD,EAASoY,OAAT,CAAjC;QACAwF,UAAU,CAACC,OAAX,CAAoB9Z,SAAD,IAAe;UAC9B;UACA;UACA,MAAM+Z,qBAAqB,GAAG9d,MAAM,CAAC,uBAAD,CAApC;;UACA,IAAI8d,qBAAJ,EAA2B;YACvB,MAAMC,GAAG,GAAG,IAAID,qBAAJ,CAA0B1F,OAA1B,EAAmC;cAC3C4F,OAAO,EAAEL,CAAC,CAACK,OADgC;cAE3CC,MAAM,EAAEN,CAAC,CAACO;YAFiC,CAAnC,CAAZ;YAIAna,SAAS,CAACb,MAAV,CAAiB6a,GAAjB;UACH;QACJ,CAXD;MAYH,CAdD;IAeH;;IACD,IAAI/d,MAAM,CAAC,uBAAD,CAAV,EAAqC;MACjC0N,IAAI,CAACE,UAAU,CAAC,kCAAD,CAAX,CAAJ,GACI8P,2BAA2B,CAAC,oBAAD,CAD/B;MAEAhQ,IAAI,CAACE,UAAU,CAAC,yBAAD,CAAX,CAAJ,GACI8P,2BAA2B,CAAC,kBAAD,CAD/B;IAEH;EACJ,CAzBD;;EA0BAhQ,IAAI,CAACrM,YAAL,CAAkB,gBAAlB,EAAoC,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IACvD6F,mBAAmB,CAAC9Y,MAAD,EAASiT,GAAT,CAAnB;EACH,CAFD;AAGH;;AAED,SAASkL,YAAT,CAAsBzQ,IAAtB,EAA4B;EACxBA,IAAI,CAACrM,YAAL,CAAkB,kBAAlB,EAAsC,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IACzD,MAAM/G,8BAA8B,GAAG9B,MAAM,CAACwC,wBAA9C;IACA,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAApC;;IACA,SAASuR,sBAAT,CAAgCrO,GAAhC,EAAqC;MACjC,IAAIA,GAAG,IAAIA,GAAG,CAAC7F,QAAJ,KAAiBE,MAAM,CAACC,SAAP,CAAiBH,QAA7C,EAAuD;QACnD,MAAM8G,SAAS,GAAGjB,GAAG,CAACjO,WAAJ,IAAmBiO,GAAG,CAACjO,WAAJ,CAAgB3B,IAArD;QACA,OAAO,CAAC6Q,SAAS,GAAGA,SAAH,GAAe,EAAzB,IAA+B,IAA/B,GAAsCqN,IAAI,CAACC,SAAL,CAAevO,GAAf,CAA7C;MACH;;MACD,OAAOA,GAAG,GAAGA,GAAG,CAAC7F,QAAJ,EAAH,GAAoBE,MAAM,CAACC,SAAP,CAAiBH,QAAjB,CAA0BT,IAA1B,CAA+BsG,GAA/B,CAA9B;IACH;;IACD,MAAM7P,UAAU,GAAG+S,GAAG,CAAC7H,MAAvB;IACA,MAAMmT,sBAAsB,GAAG,EAA/B;IACA,MAAMC,yCAAyC,GAAGxe,MAAM,CAACE,UAAU,CAAC,6CAAD,CAAX,CAAN,KAAsE,KAAxH;;IACA,MAAMsK,aAAa,GAAGtK,UAAU,CAAC,SAAD,CAAhC;;IACA,MAAMuK,UAAU,GAAGvK,UAAU,CAAC,MAAD,CAA7B;;IACA,MAAMue,aAAa,GAAG,mBAAtB;;IACAxL,GAAG,CAAC/H,gBAAJ,GAAwByS,CAAD,IAAO;MAC1B,IAAI1K,GAAG,CAAC1H,iBAAJ,EAAJ,EAA6B;QACzB,MAAM2S,SAAS,GAAGP,CAAC,IAAIA,CAAC,CAACO,SAAzB;;QACA,IAAIA,SAAJ,EAAe;UACXQ,OAAO,CAACvb,KAAR,CAAc,8BAAd,EAA8C+a,SAAS,YAAYrd,KAArB,GAA6Bqd,SAAS,CAAC1O,OAAvC,GAAiD0O,SAA/F,EAA0G,SAA1G,EAAqHP,CAAC,CAAC5c,IAAF,CAAOZ,IAA5H,EAAkI,SAAlI,EAA6Iwd,CAAC,CAACra,IAAF,IAAUqa,CAAC,CAACra,IAAF,CAAOZ,MAA9J,EAAsK,UAAtK,EAAkLwb,SAAlL,EAA6LA,SAAS,YAAYrd,KAArB,GAA6Bqd,SAAS,CAACS,KAAvC,GAA+Cpa,SAA5O;QACH,CAFD,MAGK;UACDma,OAAO,CAACvb,KAAR,CAAcwa,CAAd;QACH;MACJ;IACJ,CAVD;;IAWA1K,GAAG,CAAC9H,kBAAJ,GAAyB,MAAM;MAC3B,OAAOoT,sBAAsB,CAACzY,MAA9B,EAAsC;QAClC,MAAM8Y,oBAAoB,GAAGL,sBAAsB,CAACM,KAAvB,EAA7B;;QACA,IAAI;UACAD,oBAAoB,CAAC7d,IAArB,CAA0B8B,UAA1B,CAAqC,MAAM;YACvC,IAAI+b,oBAAoB,CAACE,aAAzB,EAAwC;cACpC,MAAMF,oBAAoB,CAACV,SAA3B;YACH;;YACD,MAAMU,oBAAN;UACH,CALD;QAMH,CAPD,CAQA,OAAOzb,KAAP,EAAc;UACV4b,wBAAwB,CAAC5b,KAAD,CAAxB;QACH;MACJ;IACJ,CAfD;;IAgBA,MAAM6b,0CAA0C,GAAG9e,UAAU,CAAC,kCAAD,CAA7D;;IACA,SAAS6e,wBAAT,CAAkCpB,CAAlC,EAAqC;MACjC1K,GAAG,CAAC/H,gBAAJ,CAAqByS,CAArB;;MACA,IAAI;QACA,MAAMsB,OAAO,GAAGvR,IAAI,CAACsR,0CAAD,CAApB;;QACA,IAAI,OAAOC,OAAP,KAAmB,UAAvB,EAAmC;UAC/BA,OAAO,CAACxV,IAAR,CAAa,IAAb,EAAmBkU,CAAnB;QACH;MACJ,CALD,CAMA,OAAO1Y,GAAP,EAAY,CAAG;IAClB;;IACD,SAASia,UAAT,CAAoBnW,KAApB,EAA2B;MACvB,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACoW,IAAb,KAAsB,UAAtC;IACH;;IACD,SAASC,iBAAT,CAA2BrW,KAA3B,EAAkC;MAC9B,OAAOA,KAAP;IACH;;IACD,SAASsW,gBAAT,CAA0BnB,SAA1B,EAAqC;MACjC,OAAOoB,gBAAgB,CAACC,MAAjB,CAAwBrB,SAAxB,CAAP;IACH;;IACD,MAAMsB,WAAW,GAAGtf,UAAU,CAAC,OAAD,CAA9B;;IACA,MAAMuf,WAAW,GAAGvf,UAAU,CAAC,OAAD,CAA9B;;IACA,MAAMwf,aAAa,GAAGxf,UAAU,CAAC,SAAD,CAAhC;;IACA,MAAMyf,wBAAwB,GAAGzf,UAAU,CAAC,oBAAD,CAA3C;;IACA,MAAM0f,wBAAwB,GAAG1f,UAAU,CAAC,oBAAD,CAA3C;;IACA,MAAMwC,MAAM,GAAG,cAAf;IACA,MAAMmd,UAAU,GAAG,IAAnB;IACA,MAAMC,QAAQ,GAAG,IAAjB;IACA,MAAMC,QAAQ,GAAG,KAAjB;IACA,MAAMC,iBAAiB,GAAG,CAA1B;;IACA,SAASC,YAAT,CAAsBjC,OAAtB,EAA+Bna,KAA/B,EAAsC;MAClC,OAAQqc,CAAD,IAAO;QACV,IAAI;UACAC,cAAc,CAACnC,OAAD,EAAUna,KAAV,EAAiBqc,CAAjB,CAAd;QACH,CAFD,CAGA,OAAOjb,GAAP,EAAY;UACRkb,cAAc,CAACnC,OAAD,EAAU,KAAV,EAAiB/Y,GAAjB,CAAd;QACH,CANS,CAOV;;MACH,CARD;IASH;;IACD,MAAMkP,IAAI,GAAG,YAAY;MACrB,IAAIiM,SAAS,GAAG,KAAhB;MACA,OAAO,SAASC,OAAT,CAAiBC,eAAjB,EAAkC;QACrC,OAAO,YAAY;UACf,IAAIF,SAAJ,EAAe;YACX;UACH;;UACDA,SAAS,GAAG,IAAZ;UACAE,eAAe,CAAC3X,KAAhB,CAAsB,IAAtB,EAA4B7F,SAA5B;QACH,CAND;MAOH,CARD;IASH,CAXD;;IAYA,MAAMyd,UAAU,GAAG,8BAAnB;;IACA,MAAMC,yBAAyB,GAAGtgB,UAAU,CAAC,kBAAD,CAA5C,CAjGyD,CAkGzD;;;IACA,SAASigB,cAAT,CAAwBnC,OAAxB,EAAiCna,KAAjC,EAAwCkF,KAAxC,EAA+C;MAC3C,MAAM0X,WAAW,GAAGtM,IAAI,EAAxB;;MACA,IAAI6J,OAAO,KAAKjV,KAAhB,EAAuB;QACnB,MAAM,IAAI2X,SAAJ,CAAcH,UAAd,CAAN;MACH;;MACD,IAAIvC,OAAO,CAACwB,WAAD,CAAP,KAAyBK,UAA7B,EAAyC;QACrC;QACA,IAAIV,IAAI,GAAG,IAAX;;QACA,IAAI;UACA,IAAI,OAAOpW,KAAP,KAAiB,QAAjB,IAA6B,OAAOA,KAAP,KAAiB,UAAlD,EAA8D;YAC1DoW,IAAI,GAAGpW,KAAK,IAAIA,KAAK,CAACoW,IAAtB;UACH;QACJ,CAJD,CAKA,OAAOla,GAAP,EAAY;UACRwb,WAAW,CAAC,MAAM;YACdN,cAAc,CAACnC,OAAD,EAAU,KAAV,EAAiB/Y,GAAjB,CAAd;UACH,CAFU,CAAX;UAGA,OAAO+Y,OAAP;QACH,CAboC,CAcrC;;;QACA,IAAIna,KAAK,KAAKkc,QAAV,IACAhX,KAAK,YAAYuW,gBADjB,IAEAvW,KAAK,CAACvH,cAAN,CAAqBge,WAArB,CAFA,IAGAzW,KAAK,CAACvH,cAAN,CAAqBie,WAArB,CAHA,IAIA1W,KAAK,CAACyW,WAAD,CAAL,KAAuBK,UAJ3B,EAIuC;UACnCc,oBAAoB,CAAC5X,KAAD,CAApB;UACAoX,cAAc,CAACnC,OAAD,EAAUjV,KAAK,CAACyW,WAAD,CAAf,EAA8BzW,KAAK,CAAC0W,WAAD,CAAnC,CAAd;QACH,CAPD,MAQK,IAAI5b,KAAK,KAAKkc,QAAV,IAAsB,OAAOZ,IAAP,KAAgB,UAA1C,EAAsD;UACvD,IAAI;YACAA,IAAI,CAAC1V,IAAL,CAAUV,KAAV,EAAiB0X,WAAW,CAACR,YAAY,CAACjC,OAAD,EAAUna,KAAV,CAAb,CAA5B,EAA4D4c,WAAW,CAACR,YAAY,CAACjC,OAAD,EAAU,KAAV,CAAb,CAAvE;UACH,CAFD,CAGA,OAAO/Y,GAAP,EAAY;YACRwb,WAAW,CAAC,MAAM;cACdN,cAAc,CAACnC,OAAD,EAAU,KAAV,EAAiB/Y,GAAjB,CAAd;YACH,CAFU,CAAX;UAGH;QACJ,CATI,MAUA;UACD+Y,OAAO,CAACwB,WAAD,CAAP,GAAuB3b,KAAvB;UACA,MAAMoH,KAAK,GAAG+S,OAAO,CAACyB,WAAD,CAArB;UACAzB,OAAO,CAACyB,WAAD,CAAP,GAAuB1W,KAAvB;;UACA,IAAIiV,OAAO,CAAC0B,aAAD,CAAP,KAA2BA,aAA/B,EAA8C;YAC1C;YACA,IAAI7b,KAAK,KAAKic,QAAd,EAAwB;cACpB;cACA;cACA9B,OAAO,CAACwB,WAAD,CAAP,GAAuBxB,OAAO,CAAC4B,wBAAD,CAA9B;cACA5B,OAAO,CAACyB,WAAD,CAAP,GAAuBzB,OAAO,CAAC2B,wBAAD,CAA9B;YACH;UACJ,CAZA,CAaD;UACA;;;UACA,IAAI9b,KAAK,KAAKkc,QAAV,IAAsBhX,KAAK,YAAYlI,KAA3C,EAAkD;YAC9C;YACA,MAAM+f,KAAK,GAAGlT,IAAI,CAACvM,WAAL,IACVuM,IAAI,CAACvM,WAAL,CAAiBuC,IADP,IAEVgK,IAAI,CAACvM,WAAL,CAAiBuC,IAAjB,CAAsB+a,aAAtB,CAFJ;;YAGA,IAAImC,KAAJ,EAAW;cACP;cACA3U,oBAAoB,CAAClD,KAAD,EAAQyX,yBAAR,EAAmC;gBACnDrQ,YAAY,EAAE,IADqC;gBAEnDD,UAAU,EAAE,KAFuC;gBAGnD1B,QAAQ,EAAE,IAHyC;gBAInDzF,KAAK,EAAE6X;cAJ4C,CAAnC,CAApB;YAMH;UACJ;;UACD,KAAK,IAAI/a,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoF,KAAK,CAACnF,MAA1B,GAAmC;YAC/B+a,uBAAuB,CAAC7C,OAAD,EAAU/S,KAAK,CAACpF,CAAC,EAAF,CAAf,EAAsBoF,KAAK,CAACpF,CAAC,EAAF,CAA3B,EAAkCoF,KAAK,CAACpF,CAAC,EAAF,CAAvC,EAA8CoF,KAAK,CAACpF,CAAC,EAAF,CAAnD,CAAvB;UACH;;UACD,IAAIoF,KAAK,CAACnF,MAAN,IAAgB,CAAhB,IAAqBjC,KAAK,IAAIkc,QAAlC,EAA4C;YACxC/B,OAAO,CAACwB,WAAD,CAAP,GAAuBQ,iBAAvB;YACA,IAAIpB,oBAAoB,GAAG7V,KAA3B;;YACA,IAAI;cACA;cACA;cACA;cACA,MAAM,IAAIlI,KAAJ,CAAU,4BACZud,sBAAsB,CAACrV,KAAD,CADV,IAEXA,KAAK,IAAIA,KAAK,CAAC4V,KAAf,GAAuB,OAAO5V,KAAK,CAAC4V,KAApC,GAA4C,EAFjC,CAAV,CAAN;YAGH,CAPD,CAQA,OAAO1Z,GAAP,EAAY;cACR2Z,oBAAoB,GAAG3Z,GAAvB;YACH;;YACD,IAAIuZ,yCAAJ,EAA+C;cAC3C;cACA;cACAI,oBAAoB,CAACE,aAArB,GAAqC,IAArC;YACH;;YACDF,oBAAoB,CAACV,SAArB,GAAiCnV,KAAjC;YACA6V,oBAAoB,CAACZ,OAArB,GAA+BA,OAA/B;YACAY,oBAAoB,CAAC7d,IAArB,GAA4B2M,IAAI,CAAC1M,OAAjC;YACA4d,oBAAoB,CAACtb,IAArB,GAA4BoK,IAAI,CAACvM,WAAjC;;YACAod,sBAAsB,CAAC1V,IAAvB,CAA4B+V,oBAA5B;;YACA3L,GAAG,CAAC/N,iBAAJ,GAxBwC,CAwBf;UAC5B;QACJ;MACJ,CAlG0C,CAmG3C;;;MACA,OAAO8Y,OAAP;IACH;;IACD,MAAM8C,yBAAyB,GAAG5gB,UAAU,CAAC,yBAAD,CAA5C;;IACA,SAASygB,oBAAT,CAA8B3C,OAA9B,EAAuC;MACnC,IAAIA,OAAO,CAACwB,WAAD,CAAP,KAAyBQ,iBAA7B,EAAgD;QAC5C;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,MAAMf,OAAO,GAAGvR,IAAI,CAACoT,yBAAD,CAApB;;UACA,IAAI7B,OAAO,IAAI,OAAOA,OAAP,KAAmB,UAAlC,EAA8C;YAC1CA,OAAO,CAACxV,IAAR,CAAa,IAAb,EAAmB;cAAEyU,SAAS,EAAEF,OAAO,CAACyB,WAAD,CAApB;cAAmCzB,OAAO,EAAEA;YAA5C,CAAnB;UACH;QACJ,CALD,CAMA,OAAO/Y,GAAP,EAAY,CAAG;;QACf+Y,OAAO,CAACwB,WAAD,CAAP,GAAuBO,QAAvB;;QACA,KAAK,IAAIla,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0Y,sBAAsB,CAACzY,MAA3C,EAAmDD,CAAC,EAApD,EAAwD;UACpD,IAAImY,OAAO,KAAKO,sBAAsB,CAAC1Y,CAAD,CAAtB,CAA0BmY,OAA1C,EAAmD;YAC/CO,sBAAsB,CAACnI,MAAvB,CAA8BvQ,CAA9B,EAAiC,CAAjC;UACH;QACJ;MACJ;IACJ;;IACD,SAASgb,uBAAT,CAAiC7C,OAAjC,EAA0Cjd,IAA1C,EAAgDggB,YAAhD,EAA8DC,WAA9D,EAA2EC,UAA3E,EAAuF;MACnFN,oBAAoB,CAAC3C,OAAD,CAApB;MACA,MAAMkD,YAAY,GAAGlD,OAAO,CAACwB,WAAD,CAA5B;MACA,MAAMvZ,QAAQ,GAAGib,YAAY,GACvB,OAAOF,WAAP,KAAuB,UAAvB,GACIA,WADJ,GAEI5B,iBAHmB,GAIvB,OAAO6B,UAAP,KAAsB,UAAtB,GACIA,UADJ,GAEI5B,gBANV;MAOAte,IAAI,CAACmE,iBAAL,CAAuBxC,MAAvB,EAA+B,MAAM;QACjC,IAAI;UACA,MAAMye,kBAAkB,GAAGnD,OAAO,CAACyB,WAAD,CAAlC;UACA,MAAM2B,gBAAgB,GAAG,CAAC,CAACL,YAAF,IAAkBrB,aAAa,KAAKqB,YAAY,CAACrB,aAAD,CAAzE;;UACA,IAAI0B,gBAAJ,EAAsB;YAClB;YACAL,YAAY,CAACpB,wBAAD,CAAZ,GAAyCwB,kBAAzC;YACAJ,YAAY,CAACnB,wBAAD,CAAZ,GAAyCsB,YAAzC;UACH,CAPD,CAQA;;;UACA,MAAMnY,KAAK,GAAGhI,IAAI,CAACgC,GAAL,CAASkD,QAAT,EAAmB1B,SAAnB,EAA8B6c,gBAAgB,IAAInb,QAAQ,KAAKoZ,gBAAjC,IAAqDpZ,QAAQ,KAAKmZ,iBAAlE,GACtC,EADsC,GAEtC,CAAC+B,kBAAD,CAFQ,CAAd;UAGAhB,cAAc,CAACY,YAAD,EAAe,IAAf,EAAqBhY,KAArB,CAAd;QACH,CAbD,CAcA,OAAO5F,KAAP,EAAc;UACV;UACAgd,cAAc,CAACY,YAAD,EAAe,KAAf,EAAsB5d,KAAtB,CAAd;QACH;MACJ,CAnBD,EAmBG4d,YAnBH;IAoBH;;IACD,MAAMM,4BAA4B,GAAG,+CAArC;;IACA,MAAM/V,IAAI,GAAG,YAAY,CAAG,CAA5B;;IACA,MAAMgW,cAAc,GAAGthB,MAAM,CAACshB,cAA9B;;IACA,MAAMhC,gBAAN,CAAuB;MACJ,OAARpV,QAAQ,GAAG;QACd,OAAOmX,4BAAP;MACH;;MACa,OAAPtW,OAAO,CAAChC,KAAD,EAAQ;QAClB,IAAIA,KAAK,YAAYuW,gBAArB,EAAuC;UACnC,OAAOvW,KAAP;QACH;;QACD,OAAOoX,cAAc,CAAC,IAAI,IAAJ,CAAS,IAAT,CAAD,EAAiBL,QAAjB,EAA2B/W,KAA3B,CAArB;MACH;;MACY,OAANwW,MAAM,CAACpc,KAAD,EAAQ;QACjB,OAAOgd,cAAc,CAAC,IAAI,IAAJ,CAAS,IAAT,CAAD,EAAiBJ,QAAjB,EAA2B5c,KAA3B,CAArB;MACH;;MACmB,OAAboe,aAAa,GAAG;QACnB,MAAMjS,MAAM,GAAG,EAAf;QACAA,MAAM,CAAC0O,OAAP,GAAiB,IAAIsB,gBAAJ,CAAqB,CAACkC,GAAD,EAAMC,GAAN,KAAc;UAChDnS,MAAM,CAACvE,OAAP,GAAiByW,GAAjB;UACAlS,MAAM,CAACiQ,MAAP,GAAgBkC,GAAhB;QACH,CAHgB,CAAjB;QAIA,OAAOnS,MAAP;MACH;;MACS,OAAHoS,GAAG,CAACC,MAAD,EAAS;QACf,IAAI,CAACA,MAAD,IAAW,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAR,CAAb,KAAmC,UAAlD,EAA8D;UAC1D,OAAOC,OAAO,CAACvC,MAAR,CAAe,IAAI+B,cAAJ,CAAmB,EAAnB,EAAuB,4BAAvB,CAAf,CAAP;QACH;;QACD,MAAMS,QAAQ,GAAG,EAAjB;QACA,IAAInc,KAAK,GAAG,CAAZ;;QACA,IAAI;UACA,KAAK,IAAIsa,CAAT,IAAcyB,MAAd,EAAsB;YAClB/b,KAAK;YACLmc,QAAQ,CAAClZ,IAAT,CAAcyW,gBAAgB,CAACvU,OAAjB,CAAyBmV,CAAzB,CAAd;UACH;QACJ,CALD,CAMA,OAAOjb,GAAP,EAAY;UACR,OAAO6c,OAAO,CAACvC,MAAR,CAAe,IAAI+B,cAAJ,CAAmB,EAAnB,EAAuB,4BAAvB,CAAf,CAAP;QACH;;QACD,IAAI1b,KAAK,KAAK,CAAd,EAAiB;UACb,OAAOkc,OAAO,CAACvC,MAAR,CAAe,IAAI+B,cAAJ,CAAmB,EAAnB,EAAuB,4BAAvB,CAAf,CAAP;QACH;;QACD,IAAIU,QAAQ,GAAG,KAAf;QACA,MAAMxN,MAAM,GAAG,EAAf;QACA,OAAO,IAAI8K,gBAAJ,CAAqB,CAACvU,OAAD,EAAUwU,MAAV,KAAqB;UAC7C,KAAK,IAAI1Z,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkc,QAAQ,CAACjc,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;YACtCkc,QAAQ,CAAClc,CAAD,CAAR,CAAYsZ,IAAZ,CAAkBe,CAAD,IAAO;cACpB,IAAI8B,QAAJ,EAAc;gBACV;cACH;;cACDA,QAAQ,GAAG,IAAX;cACAjX,OAAO,CAACmV,CAAD,CAAP;YACH,CAND,EAMIjb,GAAD,IAAS;cACRuP,MAAM,CAAC3L,IAAP,CAAY5D,GAAZ;cACAW,KAAK;;cACL,IAAIA,KAAK,KAAK,CAAd,EAAiB;gBACboc,QAAQ,GAAG,IAAX;gBACAzC,MAAM,CAAC,IAAI+B,cAAJ,CAAmB9M,MAAnB,EAA2B,4BAA3B,CAAD,CAAN;cACH;YACJ,CAbD;UAcH;QACJ,CAjBM,CAAP;MAkBH;;MACU,OAAJyN,IAAI,CAACN,MAAD,EAAS;QAChB,IAAI5W,OAAJ;QACA,IAAIwU,MAAJ;QACA,IAAIvB,OAAO,GAAG,IAAI,IAAJ,CAAS,CAACwD,GAAD,EAAMC,GAAN,KAAc;UACjC1W,OAAO,GAAGyW,GAAV;UACAjC,MAAM,GAAGkC,GAAT;QACH,CAHa,CAAd;;QAIA,SAASS,SAAT,CAAmBnZ,KAAnB,EAA0B;UACtBgC,OAAO,CAAChC,KAAD,CAAP;QACH;;QACD,SAASoZ,QAAT,CAAkBhf,KAAlB,EAAyB;UACrBoc,MAAM,CAACpc,KAAD,CAAN;QACH;;QACD,KAAK,IAAI4F,KAAT,IAAkB4Y,MAAlB,EAA0B;UACtB,IAAI,CAACzC,UAAU,CAACnW,KAAD,CAAf,EAAwB;YACpBA,KAAK,GAAG,KAAKgC,OAAL,CAAahC,KAAb,CAAR;UACH;;UACDA,KAAK,CAACoW,IAAN,CAAW+C,SAAX,EAAsBC,QAAtB;QACH;;QACD,OAAOnE,OAAP;MACH;;MACS,OAAHoE,GAAG,CAACT,MAAD,EAAS;QACf,OAAOrC,gBAAgB,CAAC+C,eAAjB,CAAiCV,MAAjC,CAAP;MACH;;MACgB,OAAVW,UAAU,CAACX,MAAD,EAAS;QACtB,MAAMY,CAAC,GAAG,QAAQ,KAAKlY,SAAL,YAA0BiV,gBAAlC,GAAqD,IAArD,GAA4DA,gBAAtE;QACA,OAAOiD,CAAC,CAACF,eAAF,CAAkBV,MAAlB,EAA0B;UAC7Ba,YAAY,EAAGzZ,KAAD,KAAY;YAAE+T,MAAM,EAAE,WAAV;YAAuB/T;UAAvB,CAAZ,CADe;UAE7B0Z,aAAa,EAAGxd,GAAD,KAAU;YAAE6X,MAAM,EAAE,UAAV;YAAsBmB,MAAM,EAAEhZ;UAA9B,CAAV;QAFc,CAA1B,CAAP;MAIH;;MACqB,OAAfod,eAAe,CAACV,MAAD,EAASlf,QAAT,EAAmB;QACrC,IAAIsI,OAAJ;QACA,IAAIwU,MAAJ;QACA,IAAIvB,OAAO,GAAG,IAAI,IAAJ,CAAS,CAACwD,GAAD,EAAMC,GAAN,KAAc;UACjC1W,OAAO,GAAGyW,GAAV;UACAjC,MAAM,GAAGkC,GAAT;QACH,CAHa,CAAd,CAHqC,CAOrC;;QACA,IAAIiB,eAAe,GAAG,CAAtB;QACA,IAAIC,UAAU,GAAG,CAAjB;QACA,MAAMC,cAAc,GAAG,EAAvB;;QACA,KAAK,IAAI7Z,KAAT,IAAkB4Y,MAAlB,EAA0B;UACtB,IAAI,CAACzC,UAAU,CAACnW,KAAD,CAAf,EAAwB;YACpBA,KAAK,GAAG,KAAKgC,OAAL,CAAahC,KAAb,CAAR;UACH;;UACD,MAAM8Z,aAAa,GAAGF,UAAtB;;UACA,IAAI;YACA5Z,KAAK,CAACoW,IAAN,CAAYpW,KAAD,IAAW;cAClB6Z,cAAc,CAACC,aAAD,CAAd,GAAgCpgB,QAAQ,GAAGA,QAAQ,CAAC+f,YAAT,CAAsBzZ,KAAtB,CAAH,GAAkCA,KAA1E;cACA2Z,eAAe;;cACf,IAAIA,eAAe,KAAK,CAAxB,EAA2B;gBACvB3X,OAAO,CAAC6X,cAAD,CAAP;cACH;YACJ,CAND,EAMI3d,GAAD,IAAS;cACR,IAAI,CAACxC,QAAL,EAAe;gBACX8c,MAAM,CAACta,GAAD,CAAN;cACH,CAFD,MAGK;gBACD2d,cAAc,CAACC,aAAD,CAAd,GAAgCpgB,QAAQ,CAACggB,aAAT,CAAuBxd,GAAvB,CAAhC;gBACAyd,eAAe;;gBACf,IAAIA,eAAe,KAAK,CAAxB,EAA2B;kBACvB3X,OAAO,CAAC6X,cAAD,CAAP;gBACH;cACJ;YACJ,CAjBD;UAkBH,CAnBD,CAoBA,OAAOE,OAAP,EAAgB;YACZvD,MAAM,CAACuD,OAAD,CAAN;UACH;;UACDJ,eAAe;UACfC,UAAU;QACb,CAzCoC,CA0CrC;;;QACAD,eAAe,IAAI,CAAnB;;QACA,IAAIA,eAAe,KAAK,CAAxB,EAA2B;UACvB3X,OAAO,CAAC6X,cAAD,CAAP;QACH;;QACD,OAAO5E,OAAP;MACH;;MACDlc,WAAW,CAACihB,QAAD,EAAW;QAClB,MAAM/E,OAAO,GAAG,IAAhB;;QACA,IAAI,EAAEA,OAAO,YAAYsB,gBAArB,CAAJ,EAA4C;UACxC,MAAM,IAAIze,KAAJ,CAAU,gCAAV,CAAN;QACH;;QACDmd,OAAO,CAACwB,WAAD,CAAP,GAAuBK,UAAvB;QACA7B,OAAO,CAACyB,WAAD,CAAP,GAAuB,EAAvB,CANkB,CAMS;;QAC3B,IAAI;UACA,MAAMgB,WAAW,GAAGtM,IAAI,EAAxB;UACA4O,QAAQ,IACJA,QAAQ,CAACtC,WAAW,CAACR,YAAY,CAACjC,OAAD,EAAU8B,QAAV,CAAb,CAAZ,EAA+CW,WAAW,CAACR,YAAY,CAACjC,OAAD,EAAU+B,QAAV,CAAb,CAA1D,CADZ;QAEH,CAJD,CAKA,OAAO5c,KAAP,EAAc;UACVgd,cAAc,CAACnC,OAAD,EAAU,KAAV,EAAiB7a,KAAjB,CAAd;QACH;MACJ;;MACsB,KAAlBye,MAAM,CAACoB,WAAW,IAAI;QACvB,OAAO,SAAP;MACH;;MACkB,KAAdpB,MAAM,CAACqB,OAAO,IAAI;QACnB,OAAO3D,gBAAP;MACH;;MACDH,IAAI,CAAC6B,WAAD,EAAcC,UAAd,EAA0B;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIiC,CAAC,GAAG,KAAKphB,WAAL,GAAmB8f,MAAM,CAACqB,OAA1B,CAAR;;QACA,IAAI,CAACC,CAAD,IAAM,OAAOA,CAAP,KAAa,UAAvB,EAAmC;UAC/BA,CAAC,GAAG,KAAKphB,WAAL,IAAoBwd,gBAAxB;QACH;;QACD,MAAMyB,YAAY,GAAG,IAAImC,CAAJ,CAAM5X,IAAN,CAArB;QACA,MAAMvK,IAAI,GAAG2M,IAAI,CAAC1M,OAAlB;;QACA,IAAI,KAAKwe,WAAL,KAAqBK,UAAzB,EAAqC;UACjC,KAAKJ,WAAL,EAAkB5W,IAAlB,CAAuB9H,IAAvB,EAA6BggB,YAA7B,EAA2CC,WAA3C,EAAwDC,UAAxD;QACH,CAFD,MAGK;UACDJ,uBAAuB,CAAC,IAAD,EAAO9f,IAAP,EAAaggB,YAAb,EAA2BC,WAA3B,EAAwCC,UAAxC,CAAvB;QACH;;QACD,OAAOF,YAAP;MACH;;MACDoC,KAAK,CAAClC,UAAD,EAAa;QACd,OAAO,KAAK9B,IAAL,CAAU,IAAV,EAAgB8B,UAAhB,CAAP;MACH;;MACDmC,OAAO,CAACC,SAAD,EAAY;QACf;QACA,IAAIH,CAAC,GAAG,KAAKphB,WAAL,GAAmB8f,MAAM,CAACqB,OAA1B,CAAR;;QACA,IAAI,CAACC,CAAD,IAAM,OAAOA,CAAP,KAAa,UAAvB,EAAmC;UAC/BA,CAAC,GAAG5D,gBAAJ;QACH;;QACD,MAAMyB,YAAY,GAAG,IAAImC,CAAJ,CAAM5X,IAAN,CAArB;QACAyV,YAAY,CAACrB,aAAD,CAAZ,GAA8BA,aAA9B;QACA,MAAM3e,IAAI,GAAG2M,IAAI,CAAC1M,OAAlB;;QACA,IAAI,KAAKwe,WAAL,KAAqBK,UAAzB,EAAqC;UACjC,KAAKJ,WAAL,EAAkB5W,IAAlB,CAAuB9H,IAAvB,EAA6BggB,YAA7B,EAA2CsC,SAA3C,EAAsDA,SAAtD;QACH,CAFD,MAGK;UACDxC,uBAAuB,CAAC,IAAD,EAAO9f,IAAP,EAAaggB,YAAb,EAA2BsC,SAA3B,EAAsCA,SAAtC,CAAvB;QACH;;QACD,OAAOtC,YAAP;MACH;;IA5MkB,CAlQkC,CAgdzD;IACA;;;IACAzB,gBAAgB,CAAC,SAAD,CAAhB,GAA8BA,gBAAgB,CAACvU,OAA/C;IACAuU,gBAAgB,CAAC,QAAD,CAAhB,GAA6BA,gBAAgB,CAACC,MAA9C;IACAD,gBAAgB,CAAC,MAAD,CAAhB,GAA2BA,gBAAgB,CAAC2C,IAA5C;IACA3C,gBAAgB,CAAC,KAAD,CAAhB,GAA0BA,gBAAgB,CAAC8C,GAA3C;IACA,MAAMkB,aAAa,GAAItjB,MAAM,CAACwK,aAAD,CAAN,GAAwBxK,MAAM,CAAC,SAAD,CAArD;IACAA,MAAM,CAAC,SAAD,CAAN,GAAoBsf,gBAApB;;IACA,MAAMiE,iBAAiB,GAAGrjB,UAAU,CAAC,aAAD,CAApC;;IACA,SAAS0L,SAAT,CAAmB4X,IAAnB,EAAyB;MACrB,MAAMnS,KAAK,GAAGmS,IAAI,CAACnZ,SAAnB;MACA,MAAM2F,IAAI,GAAG9D,8BAA8B,CAACmF,KAAD,EAAQ,MAAR,CAA3C;;MACA,IAAIrB,IAAI,KAAKA,IAAI,CAACxB,QAAL,KAAkB,KAAlB,IAA2B,CAACwB,IAAI,CAACG,YAAtC,CAAR,EAA6D;QACzD;QACA;QACA;MACH;;MACD,MAAMsT,YAAY,GAAGpS,KAAK,CAAC8N,IAA3B,CARqB,CASrB;;MACA9N,KAAK,CAAC5G,UAAD,CAAL,GAAoBgZ,YAApB;;MACAD,IAAI,CAACnZ,SAAL,CAAe8U,IAAf,GAAsB,UAAU+C,SAAV,EAAqBC,QAArB,EAA+B;QACjD,MAAMuB,OAAO,GAAG,IAAIpE,gBAAJ,CAAqB,CAACvU,OAAD,EAAUwU,MAAV,KAAqB;UACtDkE,YAAY,CAACha,IAAb,CAAkB,IAAlB,EAAwBsB,OAAxB,EAAiCwU,MAAjC;QACH,CAFe,CAAhB;QAGA,OAAOmE,OAAO,CAACvE,IAAR,CAAa+C,SAAb,EAAwBC,QAAxB,CAAP;MACH,CALD;;MAMAqB,IAAI,CAACD,iBAAD,CAAJ,GAA0B,IAA1B;IACH;;IACDtQ,GAAG,CAACrH,SAAJ,GAAgBA,SAAhB;;IACA,SAAS+X,OAAT,CAAiBriB,EAAjB,EAAqB;MACjB,OAAO,UAAUiI,IAAV,EAAgBG,IAAhB,EAAsB;QACzB,IAAIka,aAAa,GAAGtiB,EAAE,CAACqH,KAAH,CAASY,IAAT,EAAeG,IAAf,CAApB;;QACA,IAAIka,aAAa,YAAYtE,gBAA7B,EAA+C;UAC3C,OAAOsE,aAAP;QACH;;QACD,IAAIC,IAAI,GAAGD,aAAa,CAAC9hB,WAAzB;;QACA,IAAI,CAAC+hB,IAAI,CAACN,iBAAD,CAAT,EAA8B;UAC1B3X,SAAS,CAACiY,IAAD,CAAT;QACH;;QACD,OAAOD,aAAP;MACH,CAVD;IAWH;;IACD,IAAIN,aAAJ,EAAmB;MACf1X,SAAS,CAAC0X,aAAD,CAAT;MACA5X,WAAW,CAAC1L,MAAD,EAAS,OAAT,EAAmBiG,QAAD,IAAc0d,OAAO,CAAC1d,QAAD,CAAvC,CAAX;IACH,CA7fwD,CA8fzD;;;IACA6b,OAAO,CAACpU,IAAI,CAACxN,UAAL,CAAgB,uBAAhB,CAAD,CAAP,GAAoDqe,sBAApD;IACA,OAAOe,gBAAP;EACH,CAjgBD;AAkgBH;;AAED,SAASwE,aAAT,CAAuBpW,IAAvB,EAA6B;EACzB;EACA;EACAA,IAAI,CAACrM,YAAL,CAAkB,UAAlB,EAA+BrB,MAAD,IAAY;IACtC;IACA,MAAM+jB,wBAAwB,GAAGC,QAAQ,CAAC3Z,SAAT,CAAmBH,QAApD;IACA,MAAM+Z,wBAAwB,GAAGrW,UAAU,CAAC,kBAAD,CAA3C;IACA,MAAMsW,cAAc,GAAGtW,UAAU,CAAC,SAAD,CAAjC;IACA,MAAMuW,YAAY,GAAGvW,UAAU,CAAC,OAAD,CAA/B;;IACA,MAAMwW,mBAAmB,GAAG,SAASla,QAAT,GAAoB;MAC5C,IAAI,OAAO,IAAP,KAAgB,UAApB,EAAgC;QAC5B,MAAMgK,gBAAgB,GAAG,KAAK+P,wBAAL,CAAzB;;QACA,IAAI/P,gBAAJ,EAAsB;UAClB,IAAI,OAAOA,gBAAP,KAA4B,UAAhC,EAA4C;YACxC,OAAO6P,wBAAwB,CAACta,IAAzB,CAA8ByK,gBAA9B,CAAP;UACH,CAFD,MAGK;YACD,OAAO9J,MAAM,CAACC,SAAP,CAAiBH,QAAjB,CAA0BT,IAA1B,CAA+ByK,gBAA/B,CAAP;UACH;QACJ;;QACD,IAAI,SAAS4N,OAAb,EAAsB;UAClB,MAAMuC,aAAa,GAAGrkB,MAAM,CAACkkB,cAAD,CAA5B;;UACA,IAAIG,aAAJ,EAAmB;YACf,OAAON,wBAAwB,CAACta,IAAzB,CAA8B4a,aAA9B,CAAP;UACH;QACJ;;QACD,IAAI,SAASxjB,KAAb,EAAoB;UAChB,MAAMyjB,WAAW,GAAGtkB,MAAM,CAACmkB,YAAD,CAA1B;;UACA,IAAIG,WAAJ,EAAiB;YACb,OAAOP,wBAAwB,CAACta,IAAzB,CAA8B6a,WAA9B,CAAP;UACH;QACJ;MACJ;;MACD,OAAOP,wBAAwB,CAACta,IAAzB,CAA8B,IAA9B,CAAP;IACH,CAzBD;;IA0BA2a,mBAAmB,CAACH,wBAAD,CAAnB,GAAgDF,wBAAhD;IACAC,QAAQ,CAAC3Z,SAAT,CAAmBH,QAAnB,GAA8Bka,mBAA9B,CAjCsC,CAkCtC;;IACA,MAAMG,sBAAsB,GAAGna,MAAM,CAACC,SAAP,CAAiBH,QAAhD;IACA,MAAMsa,wBAAwB,GAAG,kBAAjC;;IACApa,MAAM,CAACC,SAAP,CAAiBH,QAAjB,GAA4B,YAAY;MACpC,IAAI,OAAO4X,OAAP,KAAmB,UAAnB,IAAiC,gBAAgBA,OAArD,EAA8D;QAC1D,OAAO0C,wBAAP;MACH;;MACD,OAAOD,sBAAsB,CAAC9a,IAAvB,CAA4B,IAA5B,CAAP;IACH,CALD;EAMH,CA3CD;AA4CH;;AAED,SAASiD,cAAT,CAAwBuG,GAAxB,EAA6B9M,MAA7B,EAAqCse,UAArC,EAAiDC,MAAjD,EAAyD3K,SAAzD,EAAoE;EAChE,MAAM3O,MAAM,GAAGsC,IAAI,CAACxN,UAAL,CAAgBwkB,MAAhB,CAAf;;EACA,IAAIve,MAAM,CAACiF,MAAD,CAAV,EAAoB;IAChB;EACH;;EACD,MAAMuZ,cAAc,GAAIxe,MAAM,CAACiF,MAAD,CAAN,GAAiBjF,MAAM,CAACue,MAAD,CAA/C;;EACAve,MAAM,CAACue,MAAD,CAAN,GAAiB,UAAUvkB,IAAV,EAAgBykB,IAAhB,EAAsBtb,OAAtB,EAA+B;IAC5C,IAAIsb,IAAI,IAAIA,IAAI,CAACva,SAAjB,EAA4B;MACxB0P,SAAS,CAAC8D,OAAV,CAAkB,UAAUpb,QAAV,EAAoB;QAClC,MAAMC,MAAM,GAAI,GAAE+hB,UAAW,IAAGC,MAAO,IAAxB,GAA8BjiB,QAA7C;QACA,MAAM4H,SAAS,GAAGua,IAAI,CAACva,SAAvB,CAFkC,CAGlC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QACA,IAAI;UACA,IAAIA,SAAS,CAAC7I,cAAV,CAAyBiB,QAAzB,CAAJ,EAAwC;YACpC,MAAMoiB,UAAU,GAAG5R,GAAG,CAAC/G,8BAAJ,CAAmC7B,SAAnC,EAA8C5H,QAA9C,CAAnB;;YACA,IAAIoiB,UAAU,IAAIA,UAAU,CAAC9b,KAA7B,EAAoC;cAChC8b,UAAU,CAAC9b,KAAX,GAAmBkK,GAAG,CAAC3G,mBAAJ,CAAwBuY,UAAU,CAAC9b,KAAnC,EAA0CrG,MAA1C,CAAnB;;cACAuQ,GAAG,CAACxG,iBAAJ,CAAsBmY,IAAI,CAACva,SAA3B,EAAsC5H,QAAtC,EAAgDoiB,UAAhD;YACH,CAHD,MAIK,IAAIxa,SAAS,CAAC5H,QAAD,CAAb,EAAyB;cAC1B4H,SAAS,CAAC5H,QAAD,CAAT,GAAsBwQ,GAAG,CAAC3G,mBAAJ,CAAwBjC,SAAS,CAAC5H,QAAD,CAAjC,EAA6CC,MAA7C,CAAtB;YACH;UACJ,CATD,MAUK,IAAI2H,SAAS,CAAC5H,QAAD,CAAb,EAAyB;YAC1B4H,SAAS,CAAC5H,QAAD,CAAT,GAAsBwQ,GAAG,CAAC3G,mBAAJ,CAAwBjC,SAAS,CAAC5H,QAAD,CAAjC,EAA6CC,MAA7C,CAAtB;UACH;QACJ,CAdD,CAeA,MAAM,CACF;UACA;QACH;MACJ,CA9BD;IA+BH;;IACD,OAAOiiB,cAAc,CAAClb,IAAf,CAAoBtD,MAApB,EAA4BhG,IAA5B,EAAkCykB,IAAlC,EAAwCtb,OAAxC,CAAP;EACH,CAnCD;;EAoCA2J,GAAG,CAACzG,qBAAJ,CAA0BrG,MAAM,CAACue,MAAD,CAAhC,EAA0CC,cAA1C;AACH;;AAED,SAASG,SAAT,CAAmBpX,IAAnB,EAAyB;EACrBA,IAAI,CAACrM,YAAL,CAAkB,MAAlB,EAA0B,CAACrB,MAAD,EAAS0N,IAAT,EAAeuF,GAAf,KAAuB;IAC7C;IACA;IACA,MAAMiH,UAAU,GAAGW,eAAe,CAAC7a,MAAD,CAAlC;IACAiT,GAAG,CAACxH,iBAAJ,GAAwBA,iBAAxB;IACAwH,GAAG,CAACvH,WAAJ,GAAkBA,WAAlB;IACAuH,GAAG,CAACtH,aAAJ,GAAoBA,aAApB;IACAsH,GAAG,CAACpH,cAAJ,GAAqBA,cAArB,CAP6C,CAQ7C;IACA;IACA;IACA;IACA;;IACA,MAAMkZ,0BAA0B,GAAGrX,IAAI,CAACxN,UAAL,CAAgB,qBAAhB,CAAnC;;IACA,MAAM8kB,uBAAuB,GAAGtX,IAAI,CAACxN,UAAL,CAAgB,kBAAhB,CAAhC;;IACA,IAAIF,MAAM,CAACglB,uBAAD,CAAV,EAAqC;MACjChlB,MAAM,CAAC+kB,0BAAD,CAAN,GAAqC/kB,MAAM,CAACglB,uBAAD,CAA3C;IACH;;IACD,IAAIhlB,MAAM,CAAC+kB,0BAAD,CAAV,EAAwC;MACpCrX,IAAI,CAACqX,0BAAD,CAAJ,GAAmCrX,IAAI,CAACsX,uBAAD,CAAJ,GAC/BhlB,MAAM,CAAC+kB,0BAAD,CADV;IAEH;;IACD9R,GAAG,CAACnH,mBAAJ,GAA0BA,mBAA1B;IACAmH,GAAG,CAACzH,gBAAJ,GAAuBA,gBAAvB;IACAyH,GAAG,CAAClH,UAAJ,GAAiBA,UAAjB;IACAkH,GAAG,CAAChH,oBAAJ,GAA2BA,oBAA3B;IACAgH,GAAG,CAAC/G,8BAAJ,GAAqCA,8BAArC;IACA+G,GAAG,CAAC9G,YAAJ,GAAmBA,YAAnB;IACA8G,GAAG,CAAC7G,UAAJ,GAAiBA,UAAjB;IACA6G,GAAG,CAAC5G,UAAJ,GAAiBA,UAAjB;IACA4G,GAAG,CAAC3G,mBAAJ,GAA0BA,mBAA1B;IACA2G,GAAG,CAAC1G,gBAAJ,GAAuBA,gBAAvB;IACA0G,GAAG,CAACzG,qBAAJ,GAA4BA,qBAA5B;IACAyG,GAAG,CAACxG,iBAAJ,GAAwBrC,MAAM,CAACyC,cAA/B;IACAoG,GAAG,CAACvG,cAAJ,GAAqBA,cAArB;;IACAuG,GAAG,CAACjH,gBAAJ,GAAuB,OAAO;MAC1BwG,aAD0B;MAE1BD,oBAF0B;MAG1B2H,UAH0B;MAI1BpL,SAJ0B;MAK1BC,KAL0B;MAM1BH,MAN0B;MAO1BrB,QAP0B;MAQ1BC,SAR0B;MAS1BC,kBAT0B;MAU1BN,sBAV0B;MAW1BC;IAX0B,CAAP,CAAvB;EAaH,CAhDD;AAiDH;;AAED,SAAS6X,WAAT,CAAqBvX,IAArB,EAA2B;EACvByQ,YAAY,CAACzQ,IAAD,CAAZ;EACAoW,aAAa,CAACpW,IAAD,CAAb;EACAoX,SAAS,CAACpX,IAAD,CAAT;AACH;;AAED,MAAMwX,MAAM,GAAGvY,QAAQ,EAAvB;AACAsY,WAAW,CAACC,MAAD,CAAX;AACA7J,YAAY,CAAC6J,MAAD,CAAZ"}, "metadata": {}, "sourceType": "script"}