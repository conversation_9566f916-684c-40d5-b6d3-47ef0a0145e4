  <div class="modern-list-container">
    <!-- Modern List Header -->
    <div class="modern-list-header">
      <div class="list-title-section">
        <h2 class="list-title">
          <i data-feather="package" class="title-icon"></i>
          Product List
        </h2>
        <p class="list-subtitle">Manage and view all products in your inventory</p>
      </div>

      <div class="list-actions">
        <a href="/ajouterproduit" class="btn-modern btn-primary">
          <i data-feather="plus" class="btn-icon"></i>
          Add Product
        </a>
      </div>
    </div>

    <!-- Modern Filters Section -->
    <div class="modern-filters-section">
      <div class="filters-container">

        <!-- Search by Barcode -->
        <div class="filter-group">
          <label class="filter-label">
            <i data-feather="search" class="label-icon"></i>
            {{ 'Barcode' | translate }}
          </label>
          <div class="search-input-group">
            <input
              type="text"
              class="form-control modern-search-input"
              [(ngModel)]="searchBarcode"
              placeholder="Enter barcode to search">
            <button
              class="btn-modern btn-secondary search-btn"
              type="button"
              (click)="searchByBarcode()">
              <i data-feather="search" class="btn-icon"></i>
              Search
            </button>
          </div>
        </div>

        <!-- Filter by Category -->
        <div class="filter-group">
          <label class="filter-label">
            <i data-feather="folder" class="label-icon"></i>
            {{ 'Category' | translate }}
          </label>
          <select
            class="form-control modern-select"
            [(ngModel)]="selectedCategorieId"
            (change)="filterByCategorie()">
            <option value="0" selected>All Categories</option>
            <option *ngFor="let categorie of categories" [value]="categorie.id">{{ categorie.titre }}</option>
          </select>
        </div>

        <!-- Items per page -->
        <div class="filter-group">
          <label class="filter-label">
            <i data-feather="list" class="label-icon"></i>
            {{ 'Show' | translate }}
          </label>
          <select
            class="form-control modern-select"
            [(ngModel)]="pageSize"
            (change)="paginate($event)">
            <option value="5">5 items</option>
            <option value="10">10 items</option>
            <option value="20">20 items</option>
          </select>
        </div>
      </div>
    </div>
<    <!-- Modern Data Table -->
    <div class="modern-table-container">
      <div class="table-wrapper">
        <table class="modern-table">
          <thead class="table-header">
            <tr>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="hash" class="header-icon"></i>
                  ID
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="package" class="header-icon"></i>
                  {{ 'Name' | translate }}
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="folder" class="header-icon"></i>
                  {{ 'Category' | translate }}
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="dollar-sign" class="header-icon"></i>
                  {{ 'Price' | translate }}
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="settings" class="header-icon"></i>
                  Actions
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="table-body">
            <tr
              class="table-row"
              *ngFor="let produit of produits | paginate: { itemsPerPage: pageSize, currentPage: currentPage }">

              <td class="table-cell">
                <div class="cell-content">
                  <a [routerLink]="['/afficherproduit', produit.id]" class="id-link">
                    <span class="id-badge">{{ produit.id }}</span>
                  </a>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <div class="product-info">
                    <span class="product-name">{{ produit.nom }}</span>
                    <span class="product-barcode" *ngIf="produit.codebar">
                      <i data-feather="hash" class="barcode-icon"></i>
                      {{ produit.codebar }}
                    </span>
                  </div>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <span class="category-badge">
                    <i data-feather="folder" class="category-icon"></i>
                    <span class="category-id">{{ produit.categorieid }}</span>
                    <span class="category-name">{{ getCategorie(produit.categorieid)?.titre }}</span>
                  </span>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <span class="price-value">{{ produit.prixvente | currency }}</span>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <div class="action-buttons">
                    <a [routerLink]="['/afficherproduit', produit.id]" class="btn-action btn-view" title="View Product">
                      <i data-feather="eye" class="action-icon"></i>
                    </a>
                    <a [routerLink]="['/modifierproduit', produit.id]" class="btn-action btn-edit" title="Edit Product">
                      <i data-feather="edit" class="action-icon"></i>
                    </a>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="!produits || produits.length === 0">
          <div class="empty-icon">
            <i data-feather="package" class="empty-icon-svg"></i>
          </div>
          <h3 class="empty-title">No Products Found</h3>
          <p class="empty-description">There are no products matching your current filters.</p>
          <a href="/ajouterproduit" class="btn-modern btn-primary">
            <i data-feather="plus" class="btn-icon"></i>
            Add First Product
          </a>
        </div>
      </div>
    </div>

    <!-- Modern Pagination -->
    <div class="modern-pagination-container">
      <pagination-controls
        (pageChange)="currentPage = $event"
        previousLabel="Previous"
        nextLabel="Next"
        class="modern-pagination">
      </pagination-controls>
    </div>
  </div>