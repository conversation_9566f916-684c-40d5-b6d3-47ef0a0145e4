using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using MskStoreAPI.Data;
using MskStoreAPI.Models;
using MskStoreAPI.Services;
using Newtonsoft.Json.Linq;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using BCryptNet = BCrypt.Net.BCrypt;

namespace MskStoreAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly DataContext _context;
        private readonly IConfiguration _configuration;
        private readonly ISecurityService _securityService;
        private readonly IPasswordValidationService _passwordValidationService;
        private readonly ILogger<UsersController> _logger;

        public UsersController(
            DataContext context,
            IConfiguration configuration,
            ISecurityService securityService,
            IPasswordValidationService passwordValidationService,
            ILogger<UsersController> logger)
        {
            _context = context;
            _configuration = configuration;
            _securityService = securityService;
            _passwordValidationService = passwordValidationService;
            _logger = logger;
        }

        [AllowAnonymous]
        [HttpPost("authenticate")]
        public async Task<IActionResult> Authenticate([FromBody] AuthentificationRequest model)
        {
            try
            {
                // Get client IP address
                var clientIp = GetClientIpAddress();

                // Input validation and sanitization
                if (model == null || string.IsNullOrWhiteSpace(model.authemail) || string.IsNullOrWhiteSpace(model.authpassword))
                {
                    await _securityService.LogSecurityEventAsync(
                        "LOGIN_INVALID_INPUT",
                        "Login attempt with invalid input data",
                        null,
                        clientIp,
                        Request.Headers["User-Agent"].ToString(),
                        "WARNING",
                        false
                    );
                    return BadRequest(new { message = "Email and password are required" });
                }

                // Sanitize inputs
                var sanitizedEmail = model.authemail.Trim().ToLowerInvariant();

                // Validate email format
                var emailValidation = await _passwordValidationService.ValidateInputSecurityAsync(sanitizedEmail, "email");
                if (!emailValidation)
                {
                    await _securityService.LogSecurityEventAsync(
                        "LOGIN_MALICIOUS_INPUT",
                        $"Login attempt with potentially malicious email: {sanitizedEmail}",
                        null,
                        clientIp,
                        Request.Headers["User-Agent"].ToString(),
                        "WARNING",
                        false
                    );
                    return BadRequest(new { message = "Invalid email format" });
                }

                // Check for account lockout
                var isLocked = await _securityService.IsAccountLockedAsync(sanitizedEmail);
                if (isLocked)
                {
                    await _securityService.LogSecurityEventAsync(
                        "LOGIN_ACCOUNT_LOCKED",
                        $"Login attempt on locked account: {sanitizedEmail}",
                        null,
                        clientIp,
                        Request.Headers["User-Agent"].ToString(),
                        "WARNING",
                        false
                    );
                    return StatusCode(423, new { message = "Account is locked due to multiple failed login attempts" });
                }

                // Rate limiting check
                var rateLimitResult = await _securityService.CheckRateLimitAsync(clientIp, "login");
                if (false) // TEMPORARILY DISABLED: (!rateLimitResult)
                {
                    await _securityService.LogSecurityEventAsync(
                        "LOGIN_RATE_LIMITED",
                        $"Rate limit exceeded for IP: {clientIp}",
                        null,
                        clientIp,
                        Request.Headers["User-Agent"].ToString(),
                        "WARNING",
                        false
                    );
                    return StatusCode(429, new { message = "Too many login attempts. Please try again later." });
                }

                // Find user
                var user = await _context.User.SingleOrDefaultAsync(x => x.email == sanitizedEmail);

                if (user == null)
                {
                    // Log failed attempt
                    await _securityService.LogLoginAttemptAsync(sanitizedEmail, false, clientIp, "User not found");
                    await _securityService.LogSecurityEventAsync(
                        "LOGIN_USER_NOT_FOUND",
                        $"Login attempt with non-existent email: {sanitizedEmail}",
                        null,
                        clientIp,
                        Request.Headers["User-Agent"].ToString(),
                        "WARNING",
                        false
                    );

                    // Generic error message to prevent user enumeration
                    return BadRequest(new { message = "Invalid email or password" });
                }

                // Validate password
                var isPasswordValid = BCryptNet.Verify(model.authpassword, user.password);
                if (!isPasswordValid)
                {
                    // Log failed attempt
                    await _securityService.LogLoginAttemptAsync(sanitizedEmail, false, clientIp, "Invalid password");
                    await _securityService.HandleFailedLoginAsync(sanitizedEmail, clientIp);

                    await _securityService.LogSecurityEventAsync(
                        "LOGIN_INVALID_PASSWORD",
                        $"Login attempt with invalid password for user: {sanitizedEmail}",
                        user.id.ToString(),
                        clientIp,
                        Request.Headers["User-Agent"].ToString(),
                        "WARNING",
                        false
                    );

                    return BadRequest(new { message = "Invalid email or password" });
                }

                // Check if password is expired or needs change
                var passwordStatus = await _passwordValidationService.CheckPasswordStatusAsync(user.id);
                if (passwordStatus.RequiresChange)
                {
                    await _securityService.LogSecurityEventAsync(
                        "LOGIN_PASSWORD_EXPIRED",
                        $"Login with expired password for user: {sanitizedEmail}",
                        user.id.ToString(),
                        clientIp,
                        Request.Headers["User-Agent"].ToString(),
                        "INFO",
                        true
                    );

                    return StatusCode(202, new {
                        message = "Password change required",
                        requiresPasswordChange = true,
                        daysUntilExpiration = passwordStatus.DaysUntilExpiration
                    });
                }

                // Successful login
                await _securityService.LogLoginAttemptAsync(sanitizedEmail, true, clientIp, Request.Headers["User-Agent"].ToString());
                await _securityService.ResetFailedAttemptsAsync(sanitizedEmail);

                var token = await GenerateJwtTokenAsync(user);

                await _securityService.LogSecurityEventAsync(
                    "LOGIN_SUCCESS",
                    $"Successful login for user: {sanitizedEmail}",
                    user.id.ToString(),
                    clientIp,
                    Request.Headers["User-Agent"].ToString(),
                    "INFO",
                    true
                );

                return Ok(new {
                    token,
                    user = new {
                        id = user.id,
                        email = user.email,
                        name = user.name,
                        role = user.role
                    },
                    passwordStatus = new {
                        daysUntilExpiration = passwordStatus.DaysUntilExpiration,
                        isNearExpiration = passwordStatus.IsNearExpiration
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication for email: {Email}", model?.authemail);

                await _securityService.LogSecurityEventAsync(
                    "LOGIN_ERROR",
                    $"Authentication error: {ex.Message}",
                    null,
                    GetClientIpAddress(),
                    Request.Headers["User-Agent"].ToString(),
                    "ERROR",
                    false
                );

                return StatusCode(500, new { message = "An error occurred during authentication" });
            }
        }

        private async Task<string> GenerateJwtTokenAsync(User user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();

            // Use secure key from configuration
            var jwtKey = _configuration["JwtSettings:SecretKey"] ?? "MSKStore_SecureKey_2024_AES256_CompliantKey_ForProduction_Use_Only_32Chars";
            var key = Encoding.UTF8.GetBytes(jwtKey);

            // Generate unique session ID
            var sessionId = Guid.NewGuid().ToString();
            var jti = Guid.NewGuid().ToString(); // JWT ID for token revocation

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.id.ToString()),
                new Claim(ClaimTypes.Email, user.email),
                new Claim(ClaimTypes.Name, user.name),
                new Claim(ClaimTypes.Surname, user.lastName),
                new Claim(ClaimTypes.Role, user.role),
                new Claim("jti", jti), // JWT ID
                new Claim("session_id", sessionId),
                new Claim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
                new Claim("ip_address", GetClientIpAddress()),
                new Claim("user_agent_hash", GetUserAgentHash())
            };

            // Add role-specific claims
            if (user is Employee employee)
            {
                claims.Add(new Claim("permission", employee.permission.ToString()));
                claims.Add(new Claim("employee_id", employee.id.ToString()));
            }
            else if (user is Admin admin)
            {
                claims.Add(new Claim("admin_level", "full"));
                claims.Add(new Claim("admin_id", admin.id.ToString()));
            }

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(8), // Shorter token lifetime for security
                NotBefore = DateTime.UtcNow,
                IssuedAt = DateTime.UtcNow,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = _configuration["JwtSettings:Issuer"] ?? "MSKStore_API",
                Audience = _configuration["JwtSettings:Audience"] ?? "MSKStore_Client",
                // Add additional security headers
                AdditionalHeaderClaims = new Dictionary<string, object>
                {
                    { "typ", "JWT" },
                    { "alg", "HS256" },
                    { "kid", "mskstore_key_1" } // Key identifier
                }
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = tokenHandler.WriteToken(token);

            // Log token generation for security audit
            await _securityService.LogSecurityEventAsync(
                "TOKEN_GENERATED",
                $"JWT token generated for user: {user.email}",
                user.id.ToString(),
                GetClientIpAddress(),
                Request.Headers["User-Agent"].ToString(),
                "INFO",
                true,
                JsonSerializer.Serialize(new { sessionId, jti, expiresAt = tokenDescriptor.Expires })
            );

            return tokenString;
        }

        private string GetClientIpAddress()
        {
            var ipAddress = Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
            }
            return ipAddress ?? "Unknown";
        }

        private string GetUserAgentHash()
        {
            var userAgent = Request.Headers["User-Agent"].ToString();
            if (string.IsNullOrEmpty(userAgent))
                return "Unknown";

            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(userAgent));
                return Convert.ToBase64String(hash);
            }
        }

        [Authorize(Roles = "Admin")]
        [HttpGet]
        public async Task<IEnumerable<User>> GetUser()
        {
            return await _context.User.ToListAsync();
        }

        [Authorize(Roles = "Admin")]
        [HttpGet("{id}")]
        public async Task<ActionResult<User>> GetById(int id)
        {
            var user = await _context.User.FindAsync(id);

            if (user == null)
                return NotFound();

            return Ok(user);
        }





        public class UserDTO
        {
            public int id { get; set; }
            public string? name { get; set; }
            public string? lastName { get; set; }
            public string? email { get; set; }
            public string? password { get; set; }
            public string? phone { get; set; }
            public string? role { get; set; }
            public string? photo { get; set; }
            public string? token { get; set; }
            public int? cin { get; set; } // for Employee 
            public string? typecontrat { get; set; } // for Employee 
            public decimal? salaire { get; set; } // for Employee 
            public decimal? salairebrut { get; set; } // for Employee 
            public int? permission { get; set; } // for Employee 
        }

      
        [HttpPost]
        public async Task<IActionResult> PostUser(UserDTO userDto)
        {
            // Debug: Log the incoming request
            _logger.LogInformation("Received user creation request: {UserDto}", System.Text.Json.JsonSerializer.Serialize(userDto));

            // Debug: Check ModelState
            if (!ModelState.IsValid)
            {
                var errors = ModelState
                    .Where(x => x.Value.Errors.Count > 0)
                    .Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage) })
                    .ToArray();

                _logger.LogWarning("ModelState validation failed: {Errors}", System.Text.Json.JsonSerializer.Serialize(errors));
                return BadRequest(new { message = "Validation failed", errors = errors });
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(userDto.name))
            {
                _logger.LogWarning("Validation failed: Name is required");
                return BadRequest(new { message = "Name is required" });
            }

            if (string.IsNullOrWhiteSpace(userDto.lastName))
            {
                _logger.LogWarning("Validation failed: Last name is required");
                return BadRequest(new { message = "Last name is required" });
            }

            if (string.IsNullOrWhiteSpace(userDto.email))
            {
                _logger.LogWarning("Validation failed: Email is required");
                return BadRequest(new { message = "Email is required" });
            }

            if (string.IsNullOrWhiteSpace(userDto.password))
            {
                _logger.LogWarning("Validation failed: Password is required");
                return BadRequest(new { message = "Password is required" });
            }

            if (string.IsNullOrWhiteSpace(userDto.role))
            {
                _logger.LogWarning("Validation failed: Role is required");
                return BadRequest(new { message = "Role is required" });
            }

            // Validate input security
            if (!_passwordValidationService.ValidateInputSecurity(userDto.email, "email"))
            {
                _logger.LogWarning("Validation failed: Invalid email format for {Email}", userDto.email);
                return BadRequest(new { message = "Invalid email format" });
            }

            if (!_passwordValidationService.ValidateInputSecurity(userDto.name, "name"))
            {
                _logger.LogWarning("Validation failed: Invalid name format for {Name}", userDto.name);
                return BadRequest(new { message = "Invalid name format" });
            }

            if (!_passwordValidationService.ValidateInputSecurity(userDto.lastName, "name"))
            {
                _logger.LogWarning("Validation failed: Invalid last name format for {LastName}", userDto.lastName);
                return BadRequest(new { message = "Invalid last name format" });
            }

            if (!string.IsNullOrWhiteSpace(userDto.phone) && !_passwordValidationService.ValidateInputSecurity(userDto.phone, "phone"))
            {
                _logger.LogWarning("Validation failed: Invalid phone format for {Phone}", userDto.phone);
                return BadRequest(new { message = "Invalid phone format" });
            }

            // Validate password strength
            var passwordValidation = await _passwordValidationService.ValidatePasswordAsync(userDto.password, null, userDto.email, userDto.name);
            if (!passwordValidation.IsValid)
            {
                _logger.LogWarning("Password validation failed: {Errors}", string.Join(", ", passwordValidation.Errors));
                return BadRequest(new { message = "Password validation failed", errors = passwordValidation.Errors });
            }

            // check if the email already exists
            var existingUser = await _context.User.FirstOrDefaultAsync(u => u.email == userDto.email);
            if (existingUser != null)
            {
                return BadRequest(new { message = "Email already exists." });
            }

               


                User user;

                if (userDto.role == "Employee") // Employee
                {
                    var employee = new Employee
                    {   id = userDto.id,
                        name = userDto.name,
                        lastName = userDto.lastName,
                        email = userDto.email,
                        password = userDto.password,
                        phone = userDto.phone,
                        role = userDto.role,
                        photo = userDto.photo,
                        token = userDto.token,
                        cin = userDto.cin,
                        typecontrat = userDto.typecontrat,
                        salaire = userDto.salaire,
                        salairebrut = userDto.salairebrut,
                        permission = userDto.permission
                    };
                    user = employee; // update user to be Employee
                }
                else if (userDto.role == "Admin") // Admin
                {
                    var admin = new Admin
                    {
                        name = userDto.name,
                        lastName = userDto.lastName,
                        email = userDto.email,
                        password = userDto.password,
                        phone = userDto.phone,
                        role = userDto.role,
                        photo = userDto.photo,
                        token = userDto.token
                    };
                    user = admin; // update user to be Admin
                }
                else // invalid role
                {
                    return BadRequest("Invalid role.");
                }

            // Hash the password before storing it in the database
            user.password = BCryptNet.HashPassword(userDto.password);

            _context.User.Add(user);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetUser", new
            {
                id = user.id
            }, user);
        }





        public class PasswordUpdateRequest
        {
            public string currentPassword { get; set; }
            public string newPassword { get; set; }
        }

        [Authorize]
        [HttpGet("me")]
        public async Task<ActionResult<User>> GetMe()
        {
            int userId = int.Parse(HttpContext.User.FindFirst(ClaimTypes.Name)?.Value);
            var user = await _context.User.FindAsync(userId);

            if (user == null)
                return NotFound();

            return Ok(user);
        }

        [Authorize]
        [HttpPut("me")]
        public async Task<IActionResult> UpdatePassword([FromBody] PasswordUpdateRequest request)
        {
            int userId = int.Parse(HttpContext.User.FindFirst(ClaimTypes.Name)?.Value);
            var user = await _context.User.FindAsync(userId);

            if (user == null)
            {
                return NotFound();
            }

            string currentPassword = request.currentPassword;
            string newPassword = request.newPassword;
            if (string.IsNullOrEmpty(currentPassword) || string.IsNullOrEmpty(newPassword))
            {
                return BadRequest("Current password and new password are required.");
            }

            bool isPasswordValid = BCryptNet.Verify(currentPassword, user.password);
            if (!isPasswordValid)
            {
                return BadRequest("Current password is incorrect.");
            }

            var newPasswordHash = BCryptNet.HashPassword(newPassword);
            user.password = newPasswordHash;

            _context.User.Update(user);
            await _context.SaveChangesAsync();

            return Ok("Password updated successfully.");
        }

        [Authorize(Roles = "Admin")]
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var user = await _context.User.FindAsync(id);

            if (user == null)
                return NotFound();

            _context.User.Remove(user);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, UserDTO userDto)
        {
            if (id != userDto.id)
            {
                return BadRequest("User ID in the request body does not match the ID in the URL.");
            }

            var user = await _context.User.FindAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Update user properties
            user.name = userDto.name;
            user.lastName = userDto.lastName;
            user.email = userDto.email;
            user.password = userDto.password;
            user.phone = userDto.phone;
            user.role = userDto.role;
            user.photo = userDto.photo;

            if (user is Employee employee)
            {
                // Update employee properties
                employee.cin = userDto.cin;
                employee.typecontrat = userDto.typecontrat;
                employee.salaire = userDto.salaire;
                employee.salairebrut = userDto.salairebrut;
                employee.permission = userDto.permission;
            }

            await _context.SaveChangesAsync();

            return Ok(user);
        }

        private bool UserExists(int id)
        {
            return _context.User.Any(e => e.id == id);
        }
    }
}


