﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using MskStoreAPI.Data;
using MskStoreAPI.Models;
using Newtonsoft.Json.Linq;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using BCryptNet = BCrypt.Net.BCrypt;

namespace MskStoreAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly DataContext _context;
        private readonly IConfiguration _configuration;

        public UsersController(DataContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        [AllowAnonymous]
        [HttpPost("authenticate")]
        public IActionResult Authenticate([FromBody] AuthentificationRequest model)
        {
            var user = _context.User.SingleOrDefault(x => x.email == model.authemail);

            if (user == null)
                return BadRequest(new { message = "Email or password is incorrect" });

            var isPasswordValid = BCryptNet.Verify(model.authpassword, user.password);
            if (!isPasswordValid)
                return BadRequest(new { message = "Email or password is incorrect" });

            var token = GenerateJwtToken(user);

            return Ok(new { token });
        }

        private string GenerateJwtToken(User user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes("azertyazertyazerty");

            Claim[] claims = null;

            if (user is Employee employee)
            {
                claims = new Claim[]
                {
            new Claim(ClaimTypes.Name, employee.id.ToString()),
            new Claim(ClaimTypes.Role, employee.permission.ToString()),
            new Claim(ClaimTypes.GivenName, employee.name),
            new Claim(ClaimTypes.Surname, employee.lastName),
                };
            }
            else if (user is Admin)
            {
                claims = new Claim[]
                {
            new Claim(ClaimTypes.Name, user.id.ToString()),
            new Claim(ClaimTypes.Role, user.role),
            new Claim(ClaimTypes.GivenName, user.name),
            new Claim(ClaimTypes.Surname, user.lastName)
                };
            }
            else
            {
                // handle unknown user type
            }

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddDays(1),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = _configuration.GetConnectionString("PathConnection"),
                Audience = _configuration.GetConnectionString("PathConnection")
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        [Authorize(Roles = "Admin")]
        [HttpGet]
        public async Task<IEnumerable<User>> GetUser()
        {
            return await _context.User.ToListAsync();
        }

        [Authorize(Roles = "Admin")]
        [HttpGet("{id}")]
        public async Task<ActionResult<User>> GetById(int id)
        {
            var user = await _context.User.FindAsync(id);

            if (user == null)
                return NotFound();

            return Ok(user);
        }





        public class UserDTO
        {
            public int id { get; set; }
            public string? name { get; set; }
            public string? lastName { get; set; }
            public string? email { get; set; }
            public string? password { get; set; }
            public string? phone { get; set; }
            public string? role { get; set; }
            public string? photo { get; set; }
            public string? token { get; set; }
            public int? cin { get; set; } // for Employee 
            public string? typecontrat { get; set; } // for Employee 
            public decimal? salaire { get; set; } // for Employee 
            public decimal? salairebrut { get; set; } // for Employee 
            public int? permission { get; set; } // for Employee 
        }

      
        [HttpPost]
        public async Task<IActionResult> PostUser(UserDTO userDto)
        {
            if (ModelState.IsValid)
            {

                // check if the email already exists
                var existingUser = await _context.User.FirstOrDefaultAsync(u => u.email == userDto.email);
                if (existingUser != null)
                {
                    return BadRequest(new { message = "Email already exists." });
                }

               


                User user;

                if (userDto.role == "Employee") // Employee
                {
                    var employee = new Employee
                    {   id = userDto.id,
                        name = userDto.name,
                        lastName = userDto.lastName,
                        email = userDto.email,
                        password = userDto.password,
                        phone = userDto.phone,
                        role = userDto.role,
                        photo = userDto.photo,
                        token = userDto.token,
                        cin = userDto.cin,
                        typecontrat = userDto.typecontrat,
                        salaire = userDto.salaire,
                        salairebrut = userDto.salairebrut,
                        permission = userDto.permission
                    };
                    user = employee; // update user to be Employee
                }
                else if (userDto.role == "Admin") // Admin
                {
                    var admin = new Admin
                    {
                        name = userDto.name,
                        lastName = userDto.lastName,
                        email = userDto.email,
                        password = userDto.password,
                        phone = userDto.phone,
                        role = userDto.role,
                        photo = userDto.photo,
                        token = userDto.token
                    };
                    user = admin; // update user to be Admin
                }
                else // invalid role
                {
                    return BadRequest("Invalid role.");
                }

                // Hash the password before storing it in the database
                user.password = BCryptNet.HashPassword(userDto.password);

                _context.User.Add(user);
                await _context.SaveChangesAsync();

                return CreatedAtAction("GetUser", new
                {
                    id = user.id
                }, user);
            }

            return BadRequest(ModelState);
        }





        public class PasswordUpdateRequest
        {
            public string currentPassword { get; set; }
            public string newPassword { get; set; }
        }

        [Authorize]
        [HttpGet("me")]
        public async Task<ActionResult<User>> GetMe()
        {
            int userId = int.Parse(HttpContext.User.FindFirst(ClaimTypes.Name)?.Value);
            var user = await _context.User.FindAsync(userId);

            if (user == null)
                return NotFound();

            return Ok(user);
        }

        [Authorize]
        [HttpPut("me")]
        public async Task<IActionResult> UpdatePassword([FromBody] PasswordUpdateRequest request)
        {
            int userId = int.Parse(HttpContext.User.FindFirst(ClaimTypes.Name)?.Value);
            var user = await _context.User.FindAsync(userId);

            if (user == null)
            {
                return NotFound();
            }

            string currentPassword = request.currentPassword;
            string newPassword = request.newPassword;
            if (string.IsNullOrEmpty(currentPassword) || string.IsNullOrEmpty(newPassword))
            {
                return BadRequest("Current password and new password are required.");
            }

            bool isPasswordValid = BCryptNet.Verify(currentPassword, user.password);
            if (!isPasswordValid)
            {
                return BadRequest("Current password is incorrect.");
            }

            var newPasswordHash = BCryptNet.HashPassword(newPassword);
            user.password = newPasswordHash;

            _context.User.Update(user);
            await _context.SaveChangesAsync();

            return Ok("Password updated successfully.");
        }

        [Authorize(Roles = "Admin")]
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var user = await _context.User.FindAsync(id);

            if (user == null)
                return NotFound();

            _context.User.Remove(user);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, UserDTO userDto)
        {
            if (id != userDto.id)
            {
                return BadRequest("User ID in the request body does not match the ID in the URL.");
            }

            var user = await _context.User.FindAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Update user properties
            user.name = userDto.name;
            user.lastName = userDto.lastName;
            user.email = userDto.email;
            user.password = userDto.password;
            user.phone = userDto.phone;
            user.role = userDto.role;
            user.photo = userDto.photo;

            if (user is Employee employee)
            {
                // Update employee properties
                employee.cin = userDto.cin;
                employee.typecontrat = userDto.typecontrat;
                employee.salaire = userDto.salaire;
                employee.salairebrut = userDto.salairebrut;
                employee.permission = userDto.permission;
            }

            await _context.SaveChangesAsync();

            return Ok(user);
        }

        private bool UserExists(int id)
        {
            return _context.User.Any(e => e.id == id);
        }
    }
}