
  <form (submit)="onSubmit()">
    <div class="form-group">
      <label for="nom">{{ 'Name' | translate }}</label>
      <input type="text" class="form-control" id="nom" name="nom" placeholder="Enter Nom" [(ngModel)]="newColonne.nom" required>
    </div>
    <div class="form-group">
      <label for="description">{{ 'Description' | translate }}</label>
      <input type="text" class="form-control" id="description" name="description" placeholder="Enter Description" [(ngModel)]="newColonne.description" required>
    </div>
    <button type="submit" class="btn btn-primary">{{ 'AddColumn' | translate }}</button>
  </form>
