import { Component, OnInit, OnDestroy, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { CoreConfigService } from '@core/services/config.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { InputValidationService, ValidationResult } from 'app/auth/services/input-validation.service';
import { first } from 'rxjs/operators'

@Component({
  selector: 'app-auth-login-v2',
  templateUrl: './auth-login-v2.component.html',
  styleUrls: ['./auth-login-v2.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AuthLoginV2Component implements OnInit, OnD<PERSON>roy {
  //  Public
  public coreConfig: any;
  public loginForm: UntypedFormGroup;
  public loading = false;
  public submitted = false;
  public returnUrl: string;
  public error = '';
  public passwordTextType: boolean;

  // Validation properties
  public emailValidation: ValidationResult = { isValid: true, errors: [], warnings: [], suggestions: [] };
  public passwordValidation: ValidationResult = { isValid: true, errors: [], warnings: [], suggestions: [] };
  public showValidationFeedback = false;
  public loginAttempts = 0;
  public maxLoginAttempts = 5;
  public isAccountLocked = false;
  public lockoutTimeRemaining = 0;

  // Private
  private _unsubscribeAll: Subject<any>;

  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   */
  constructor(
    private authenticationService: AuthenticationService,
    private _coreConfigService: CoreConfigService,
    private _formBuilder: UntypedFormBuilder,
    private _route: ActivatedRoute,
    private _router: Router,
    private validationService: InputValidationService
  ) {
    this._unsubscribeAll = new Subject();

    // Configure the layout
    this._coreConfigService.config = {
      layout: {
        navbar: {
          hidden: true
        },
        menu: {
          hidden: true
        },
        footer: {
          hidden: true
        },
        customizer: false,
        enableLocalStorage: false
      }
    };
  }

  // convenience getter for easy access to form fields
  get f() {
    return this.loginForm.controls;
  }

  /**
   * Toggle password
   */
  togglePasswordTextType() {
    this.passwordTextType = !this.passwordTextType;
  }

  onSubmit() {
    this.submitted = true;
    this.showValidationFeedback = true;

    // Validate inputs before submission
    this.validateEmail();
    this.validatePassword();

    // Check if account is locked
    if (this.isAccountLocked) {
      this.error = `Compte verrouillé. Réessayez dans ${this.lockoutTimeRemaining} minutes.`;
      return;
    }

    // Check if max attempts reached
    if (this.loginAttempts >= this.maxLoginAttempts) {
      this.lockAccount();
      return;
    }

    // Stop here if form is invalid
    if (this.loginForm.invalid || !this.emailValidation.isValid || !this.passwordValidation.isValid) {
      this.error = 'Veuillez corriger les erreurs dans le formulaire.';
      return;
    }

    this.loading = true;
    this.error = '';

    // Sanitize inputs before sending
    const sanitizedEmail = this.validationService.sanitizeInput(this.f.email.value);
    const sanitizedPassword = this.f.password.value; // Don't sanitize password as it might change it

    this.authenticationService.login(sanitizedEmail, sanitizedPassword)
      .subscribe(
        (response) => {
          console.log('Login successful:', response);
          this.loginAttempts = 0; // Reset attempts on successful login
          this.loading = false;

          // Show success message
          this.showSuccessMessage('Connexion réussie !');

          // Navigate to return URL or dashboard
          this._router.navigate([this.returnUrl]);
        },
        (error) => {
          console.error('Login error:', error);
          this.loading = false;
          this.loginAttempts++;

          // Handle different error types
          if (error.status === 401) {
            this.error = 'Email ou mot de passe incorrect.';
          } else if (error.status === 423) {
            this.error = 'Compte verrouillé en raison de tentatives de connexion multiples.';
            this.isAccountLocked = true;
          } else if (error.status === 429) {
            this.error = 'Trop de tentatives de connexion. Veuillez réessayer plus tard.';
          } else {
            this.error = 'Erreur de connexion. Veuillez réessayer.';
          }

          // Check if should lock account
          if (this.loginAttempts >= this.maxLoginAttempts) {
            this.lockAccount();
          }
        }
      );
  }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * Validate email input
   */
  validateEmail(): void {
    const email = this.f.email.value;
    this.emailValidation = this.validationService.validateEmail(email);
  }

  /**
   * Validate password input
   */
  validatePassword(): void {
    const password = this.f.password.value;
    this.passwordValidation = this.validationService.validatePassword(password, this.f.email.value);
  }

  /**
   * Handle real-time email validation
   */
  onEmailChange(): void {
    if (this.showValidationFeedback) {
      this.validateEmail();
    }
  }

  /**
   * Handle real-time password validation
   */
  onPasswordChange(): void {
    if (this.showValidationFeedback) {
      this.validatePassword();
    }
  }

  /**
   * Lock account after max attempts
   */
  private lockAccount(): void {
    this.isAccountLocked = true;
    this.lockoutTimeRemaining = 30; // 30 minutes
    this.error = `Compte verrouillé pour ${this.lockoutTimeRemaining} minutes en raison de tentatives de connexion multiples.`;

    // Start countdown timer
    const timer = setInterval(() => {
      this.lockoutTimeRemaining--;
      if (this.lockoutTimeRemaining <= 0) {
        this.isAccountLocked = false;
        this.loginAttempts = 0;
        this.error = '';
        clearInterval(timer);
      }
    }, 60000); // Update every minute
  }

  /**
   * Show success message
   */
  private showSuccessMessage(message: string): void {
    // You can implement a toast service here
    console.log('Success:', message);
  }

  /**
   * Get validation error messages for display
   */
  getEmailErrors(): string[] {
    const errors = [];
    if (this.f.email.errors?.['required']) {
      errors.push('L\'email est requis');
    }
    if (this.f.email.errors?.['email']) {
      errors.push('Format d\'email invalide');
    }
    if (this.emailValidation.errors.length > 0) {
      errors.push(...this.emailValidation.errors);
    }
    return errors;
  }

  /**
   * Get validation error messages for password
   */
  getPasswordErrors(): string[] {
    const errors = [];
    if (this.f.password.errors?.['required']) {
      errors.push('Le mot de passe est requis');
    }
    if (this.passwordValidation.errors.length > 0) {
      errors.push(...this.passwordValidation.errors);
    }
    return errors;
  }

  /**
   * Check if field has errors
   */
  hasFieldErrors(fieldName: string): boolean {
    const field = this.f[fieldName];
    return (field.invalid && (field.dirty || field.touched || this.submitted)) ||
           (fieldName === 'email' && !this.emailValidation.isValid && this.showValidationFeedback) ||
           (fieldName === 'password' && !this.passwordValidation.isValid && this.showValidationFeedback);
  }

  /**
   * On init
   */
  ngOnInit(): void {
    this.loginForm = this._formBuilder.group({
      email: ['', [Validators.required, InputValidationService.emailValidator(), InputValidationService.securityValidator()]],
      password: ['', [Validators.required, InputValidationService.securityValidator()]]
    });

    // Set up real-time validation
    this.f.email.valueChanges.subscribe(() => this.onEmailChange());
    this.f.password.valueChanges.subscribe(() => this.onPasswordChange());

    // get return url from route parameters or default to '/'
    this.returnUrl = this._route.snapshot.queryParams['returnUrl'] || '/';

    // Subscribe to config changes
    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {
      this.coreConfig = config;
    });
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }
}
