import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  strengthScore?: number;
  strengthLevel?: string;
}

export interface PasswordStrengthResult {
  score: number;
  level: string;
  feedback: string[];
  requirements: {
    length: boolean;
    uppercase: boolean;
    lowercase: boolean;
    numbers: boolean;
    special: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class InputValidationService {

  // Common weak passwords
  private readonly commonPasswords = new Set([
    'password', '123456', 'password123', 'admin', 'qwerty', 'letmein', 'welcome',
    'monkey', '1234567890', 'abc123', '111111', '123123', 'password1', '1234',
    '12345', 'dragon', 'master', 'login', 'passw0rd', 'football', 'baseball'
  ]);

  // Malicious patterns for XSS and injection prevention
  private readonly maliciousPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /onload\s*=/gi,
    /onerror\s*=/gi,
    /onclick\s*=/gi,
    /<iframe[^>]*>/gi,
    /<object[^>]*>/gi,
    /<embed[^>]*>/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi,
    /union\s+select/gi,
    /drop\s+table/gi,
    /insert\s+into/gi,
    /delete\s+from/gi,
    /update\s+set/gi,
    /--\s*$/gm,
    /\/\*.*?\*\//gs,
    /xp_cmdshell/gi,
    /sp_executesql/gi
  ];

  constructor() { }

  /**
   * Comprehensive password validation with strength analysis
   */
  validatePassword(password: string, email?: string, name?: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    if (!password) {
      result.errors.push('Le mot de passe est requis');
      result.isValid = false;
      return result;
    }

    // Length validation
    if (password.length < 8) {
      result.errors.push('Le mot de passe doit contenir au moins 8 caractères');
      result.isValid = false;
    }

    if (password.length > 128) {
      result.errors.push('Le mot de passe ne peut pas dépasser 128 caractères');
      result.isValid = false;
    }

    // Character requirements
    if (!/[A-Z]/.test(password)) {
      result.errors.push('Le mot de passe doit contenir au moins une lettre majuscule');
      result.isValid = false;
    }

    if (!/[a-z]/.test(password)) {
      result.errors.push('Le mot de passe doit contenir au moins une lettre minuscule');
      result.isValid = false;
    }

    if (!/\d/.test(password)) {
      result.errors.push('Le mot de passe doit contenir au moins un chiffre');
      result.isValid = false;
    }

    if (!/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
      result.errors.push('Le mot de passe doit contenir au moins un caractère spécial');
      result.isValid = false;
    }

    // Check against common passwords
    if (this.commonPasswords.has(password.toLowerCase())) {
      result.errors.push('Ce mot de passe est trop commun. Veuillez choisir un mot de passe plus unique');
      result.isValid = false;
    }

    // Check against personal information
    if (email && password.toLowerCase().includes(email.split('@')[0].toLowerCase())) {
      result.errors.push('Le mot de passe ne peut pas contenir votre nom d\'utilisateur');
      result.isValid = false;
    }

    if (name && password.toLowerCase().includes(name.toLowerCase())) {
      result.errors.push('Le mot de passe ne peut pas contenir votre nom');
      result.isValid = false;
    }

    // Calculate strength
    const strengthResult = this.calculatePasswordStrength(password);
    result.strengthScore = strengthResult.score;
    result.strengthLevel = strengthResult.level;

    // Add suggestions
    result.suggestions.push(...strengthResult.feedback);

    // Warnings for weak passwords
    if (strengthResult.score < 60) {
      result.warnings.push('Votre mot de passe est faible. Considérez l\'utilisation d\'un mot de passe plus long avec plus de variété de caractères');
    }

    return result;
  }

  /**
   * Calculate password strength score and provide feedback
   */
  calculatePasswordStrength(password: string): PasswordStrengthResult {
    let score = 0;
    const feedback: string[] = [];
    
    const requirements = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      numbers: /\d/.test(password),
      special: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)
    };

    // Length scoring
    if (password.length >= 8) score += 10;
    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 10;
    else if (password.length < 12) feedback.push('Utilisez au moins 12 caractères');

    // Character variety scoring
    if (requirements.lowercase) score += 10;
    else feedback.push('Ajoutez des lettres minuscules');

    if (requirements.uppercase) score += 10;
    else feedback.push('Ajoutez des lettres majuscules');

    if (requirements.numbers) score += 10;
    else feedback.push('Ajoutez des chiffres');

    if (requirements.special) score += 15;
    else feedback.push('Ajoutez des caractères spéciaux');

    // Pattern complexity bonuses
    if (this.hasMixedCase(password)) score += 5;
    if (this.hasNumbersAndLetters(password)) score += 5;
    if (this.hasSpecialCharacterVariety(password)) score += 10;

    // Penalties for weak patterns
    if (this.hasRepeatingCharacters(password)) {
      score -= 10;
      feedback.push('Évitez les caractères répétitifs');
    }

    if (this.hasSequentialCharacters(password)) {
      score -= 10;
      feedback.push('Évitez les séquences de caractères');
    }

    score = Math.max(0, Math.min(100, score));

    const level = this.getStrengthLevel(score);

    return {
      score,
      level,
      feedback,
      requirements
    };
  }

  /**
   * Validate email format with comprehensive checks
   */
  validateEmail(email: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    if (!email) {
      result.errors.push('L\'email est requis');
      result.isValid = false;
      return result;
    }

    // Check for malicious patterns
    if (!this.isInputSecure(email)) {
      result.errors.push('L\'email contient des caractères non autorisés');
      result.isValid = false;
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      result.errors.push('Format d\'email invalide');
      result.isValid = false;
    }

    // Length validation
    if (email.length > 254) {
      result.errors.push('L\'email ne peut pas dépasser 254 caractères');
      result.isValid = false;
    }

    // Additional email security checks
    if (email.includes('..')) {
      result.errors.push('L\'email ne peut pas contenir des points consécutifs');
      result.isValid = false;
    }

    return result;
  }

  /**
   * Validate phone number format
   */
  validatePhone(phone: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    if (!phone) {
      result.errors.push('Le numéro de téléphone est requis');
      result.isValid = false;
      return result;
    }

    // Remove formatting characters
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');

    // Check for malicious patterns
    if (!this.isInputSecure(cleanPhone)) {
      result.errors.push('Le numéro de téléphone contient des caractères non autorisés');
      result.isValid = false;
    }

    // Phone format validation (international format)
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(cleanPhone)) {
      result.errors.push('Format de numéro de téléphone invalide');
      result.isValid = false;
    }

    return result;
  }

  /**
   * Validate name fields
   */
  validateName(name: string, fieldName: string = 'nom'): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    if (!name) {
      result.errors.push(`Le ${fieldName} est requis`);
      result.isValid = false;
      return result;
    }

    // Check for malicious patterns
    if (!this.isInputSecure(name)) {
      result.errors.push(`Le ${fieldName} contient des caractères non autorisés`);
      result.isValid = false;
    }

    // Name format validation
    const nameRegex = /^[a-zA-ZÀ-ÿ\s\-\.\']+$/;
    if (!nameRegex.test(name)) {
      result.errors.push(`Le ${fieldName} ne peut contenir que des lettres, espaces, tirets et apostrophes`);
      result.isValid = false;
    }

    // Length validation
    if (name.length > 100) {
      result.errors.push(`Le ${fieldName} ne peut pas dépasser 100 caractères`);
      result.isValid = false;
    }

    if (name.length < 2) {
      result.errors.push(`Le ${fieldName} doit contenir au moins 2 caractères`);
      result.isValid = false;
    }

    return result;
  }

  /**
   * General input security validation
   */
  isInputSecure(input: string): boolean {
    if (!input) return true;

    // Check against malicious patterns
    for (const pattern of this.maliciousPatterns) {
      if (pattern.test(input)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Sanitize input to prevent XSS
   */
  sanitizeInput(input: string): string {
    if (!input) return '';

    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
      .trim();
  }

  // Helper methods for password strength calculation
  private hasMixedCase(password: string): boolean {
    return /[A-Z]/.test(password) && /[a-z]/.test(password);
  }

  private hasNumbersAndLetters(password: string): boolean {
    return /\d/.test(password) && /[a-zA-Z]/.test(password);
  }

  private hasSpecialCharacterVariety(password: string): boolean {
    const specialChars = password.match(/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/g);
    return specialChars ? new Set(specialChars).size >= 2 : false;
  }

  private hasRepeatingCharacters(password: string): boolean {
    for (let i = 0; i < password.length - 2; i++) {
      if (password[i] === password[i + 1] && password[i + 1] === password[i + 2]) {
        return true;
      }
    }
    return false;
  }

  private hasSequentialCharacters(password: string): boolean {
    for (let i = 0; i < password.length - 2; i++) {
      const char1 = password.charCodeAt(i);
      const char2 = password.charCodeAt(i + 1);
      const char3 = password.charCodeAt(i + 2);

      if ((char1 + 1 === char2 && char2 + 1 === char3) ||
          (char1 - 1 === char2 && char2 - 1 === char3)) {
        return true;
      }
    }
    return false;
  }

  private getStrengthLevel(score: number): string {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Fort';
    if (score >= 40) return 'Bon';
    if (score >= 20) return 'Faible';
    return 'Très faible';
  }

  /**
   * Angular Validators for reactive forms
   */

  // Custom password validator
  static passwordValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const service = new InputValidationService();
      const result = service.validatePassword(control.value);

      if (result.isValid) {
        return null;
      }

      return {
        passwordInvalid: {
          errors: result.errors,
          warnings: result.warnings,
          suggestions: result.suggestions
        }
      };
    };
  }

  // Custom email validator
  static emailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const service = new InputValidationService();
      const result = service.validateEmail(control.value);

      if (result.isValid) {
        return null;
      }

      return {
        emailInvalid: {
          errors: result.errors
        }
      };
    };
  }

  // Custom phone validator
  static phoneValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const service = new InputValidationService();
      const result = service.validatePhone(control.value);

      if (result.isValid) {
        return null;
      }

      return {
        phoneInvalid: {
          errors: result.errors
        }
      };
    };
  }

  // Custom name validator
  static nameValidator(fieldName: string = 'nom'): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const service = new InputValidationService();
      const result = service.validateName(control.value, fieldName);

      if (result.isValid) {
        return null;
      }

      return {
        nameInvalid: {
          errors: result.errors
        }
      };
    };
  }

  // Security validator for general inputs
  static securityValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const service = new InputValidationService();

      if (!service.isInputSecure(control.value)) {
        return {
          securityViolation: {
            message: 'Input contains potentially dangerous content'
          }
        };
      }

      return null;
    };
  }

  // Password confirmation validator
  static passwordMatchValidator(passwordField: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const password = control.parent?.get(passwordField);
      const confirmPassword = control.value;

      if (password && confirmPassword && password.value !== confirmPassword) {
        return {
          passwordMismatch: {
            message: 'Les mots de passe ne correspondent pas'
          }
        };
      }

      return null;
    };
  }
}
