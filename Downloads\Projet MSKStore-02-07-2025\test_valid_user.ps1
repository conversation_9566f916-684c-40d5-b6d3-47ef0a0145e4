# Bypass SSL certificate validation
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12

# Create a user with a password that won't trigger validation errors
$body = @{
    name = "<PERSON>"
    lastName = "Smith"
    email = "<EMAIL>"
    password = "SecurePass123!"
    phone = "1234567890"
    role = "Admin"
} | ConvertTo-Json

try {
    Write-Host "Testing with valid user data..."
    Write-Host "Request body: $body"
    
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $body
    Write-Host "SUCCESS! User created:"
    Write-Host ($response | ConvertTo-Json)
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)"
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
        
        # Try to read the response body for more details
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody"
        } catch {
            Write-Host "Could not read response body"
        }
    }
}
