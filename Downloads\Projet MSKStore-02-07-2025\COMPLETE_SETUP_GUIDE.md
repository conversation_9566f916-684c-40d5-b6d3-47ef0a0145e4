# Complete MSKStore Project Setup Guide

## 🚀 Step-by-Step Setup After Installing .NET 7 SDK

### Phase 1: Verify .NET Installation
```bash
# Open PowerShell and verify installation
dotnet --version
# Should show 7.x.x

dotnet --list-sdks
# Should show .NET 7 SDK
```

### Phase 2: Backend API Setup

#### 1. Navigate to API Directory
```bash
cd "Downloads\Projet MSKStore-02-07-2025\MskStoreAPI\MskStoreAPI"
```

#### 2. Restore Dependencies
```bash
dotnet restore
```

#### 3. Build the Project
```bash
dotnet build
```

#### 4. Database Setup
```bash
# Create initial migration
dotnet ef migrations add InitialCreate

# Apply migration to create database
dotnet ef database update
```

#### 5. Run the Backend API
```bash
dotnet run
```

**Expected Output:**
```
Building...
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: https://localhost:7258
      Now listening on: http://localhost:5258
info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shutdown.
```

### Phase 3: Swagger API Integration

#### Swagger is Already Configured! 🎉
The backend already includes Swagger integration with:
- **JWT Bearer Authentication** support
- **Authorization filters** for protected endpoints
- **API documentation** for all controllers

#### Access Swagger UI:
1. **Start the backend** (dotnet run)
2. **Open browser** and navigate to: `https://localhost:7258/swagger`
3. **Swagger UI** will show all available API endpoints

#### Swagger Features Available:
- 📋 **Complete API Documentation**
- 🔐 **JWT Authentication Testing**
- 🧪 **Interactive API Testing**
- 📊 **Request/Response Examples**

### Phase 4: Frontend Integration

#### Angular Frontend is Already Running ✅
- **URL**: `http://localhost:4200`
- **API Connection**: Pre-configured to `https://localhost:7258`
- **CORS**: Already configured in backend

### Phase 5: Full Project Testing

#### 1. Test API Endpoints via Swagger
```
1. Go to: https://localhost:7258/swagger
2. Try GET endpoints (no auth required):
   - GET /api/Produits
   - GET /api/Categories
3. For protected endpoints:
   - Click "Authorize" button
   - Use JWT token from login
```

#### 2. Test Frontend-Backend Integration
```
1. Open Angular app: http://localhost:4200
2. Try login functionality
3. Navigate through different modules
4. Check browser console for any errors
```

### Phase 6: Authentication Flow

#### Login Process:
1. **Frontend**: User enters credentials
2. **Backend**: Validates and returns JWT token
3. **Frontend**: Stores token and includes in API requests
4. **Backend**: Validates token for protected endpoints

#### Test Authentication:
```
POST /api/Users/<USER>
{
  "authemail": "<EMAIL>",
  "authpassword": "password"
}
```

### 🔧 Troubleshooting

#### If Backend Fails to Start:
```bash
# Check for port conflicts
netstat -ano | findstr :7258

# Try different port
dotnet run --urls="https://localhost:7259;http://localhost:5259"
```

#### If Database Issues:
```bash
# Reset database
dotnet ef database drop
dotnet ef database update
```

#### If CORS Issues:
- Backend CORS is configured for `http://localhost:4200`
- If Angular runs on different port, update Program.cs

### 📱 Available Modules

#### Frontend Modules:
- 👥 **User Management** (Admin/Employee)
- 📦 **Product Management**
- 🏪 **Categories**
- 📋 **Orders** (Client/Supplier)
- 💰 **Invoicing**
- 📊 **Reports**
- 🔐 **Authentication**

#### API Endpoints:
- `/api/Users` - User management
- `/api/Produits` - Products
- `/api/Categories` - Categories  
- `/api/Commandeclients` - Client orders
- `/api/Commandefournisseurs` - Supplier orders
- `/api/Factureclients` - Invoices

### 🎯 Success Indicators

✅ **Backend Running**: Console shows "Now listening on: https://localhost:7258"
✅ **Swagger Accessible**: https://localhost:7258/swagger loads
✅ **Frontend Running**: http://localhost:4200 loads
✅ **API Integration**: Frontend can call backend APIs
✅ **Authentication**: Login/logout works
✅ **Database**: EF migrations applied successfully

### 🚀 You're Ready!
Once all steps complete successfully, you'll have:
- ✅ Full-stack application running
- ✅ Swagger API documentation
- ✅ JWT authentication
- ✅ Database integration
- ✅ Frontend-backend communication
