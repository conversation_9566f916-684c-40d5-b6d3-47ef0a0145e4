is_global = true
build_property.TargetFramework = net7.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = MskStoreAPI
build_property.RootNamespace = MskStoreAPI
build_property.ProjectDir = C:\Users\<USER>\Downloads\Projet MSKStore-02-07-2025\MskStoreAPI\MskStoreAPI\
build_property.RazorLangVersion = 7.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\Downloads\Projet MSKStore-02-07-2025\MskStoreAPI\MskStoreAPI
build_property._RazorSourceGeneratorDebug = 
