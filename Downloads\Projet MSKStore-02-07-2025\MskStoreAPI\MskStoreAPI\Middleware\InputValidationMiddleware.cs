using Microsoft.AspNetCore.Http;
using MskStoreAPI.Services;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace MskStoreAPI.Middleware
{
    /// <summary>
    /// Middleware for comprehensive input validation and sanitization
    /// </summary>
    public class InputValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<InputValidationMiddleware> _logger;
        private readonly ISecurityService _securityService;

        // Malicious patterns to detect
        private static readonly Regex[] MaliciousPatterns = new[]
        {
            new Regex(@"<script[^>]*>.*?</script>", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"javascript:", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"on\w+\s*=", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(union|select|insert|update|delete|drop|create|alter|exec|execute)\s+", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(\||&|;|\$|\`|>|<)", RegexOptions.Compiled),
            new Regex(@"\.\.\/|\.\.\\", RegexOptions.Compiled), // Path traversal
            new Regex(@"(eval|setTimeout|setInterval)\s*\(", RegexOptions.IgnoreCase | RegexOptions.Compiled)
        };

        public InputValidationMiddleware(RequestDelegate next, ILogger<InputValidationMiddleware> logger, ISecurityService securityService)
        {
            _next = next;
            _logger = logger;
            _securityService = securityService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // Skip validation for certain endpoints
                if (ShouldSkipValidation(context.Request.Path))
                {
                    await _next(context);
                    return;
                }

                // Validate request size
                if (context.Request.ContentLength > 10 * 1024 * 1024) // 10MB limit
                {
                    await LogSecurityEvent(context, "REQUEST_TOO_LARGE", "Request size exceeds limit");
                    context.Response.StatusCode = 413;
                    await context.Response.WriteAsync("Request too large");
                    return;
                }

                // Validate headers
                if (!await ValidateHeaders(context))
                {
                    return;
                }

                // Validate query parameters
                if (!await ValidateQueryParameters(context))
                {
                    return;
                }

                // Validate request body for POST/PUT requests
                if (context.Request.Method == "POST" || context.Request.Method == "PUT")
                {
                    if (!await ValidateRequestBody(context))
                    {
                        return;
                    }
                }

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InputValidationMiddleware");
                await LogSecurityEvent(context, "VALIDATION_ERROR", $"Validation error: {ex.Message}");
                
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync("Internal server error");
            }
        }

        private bool ShouldSkipValidation(PathString path)
        {
            var skipPaths = new[] { "/health", "/metrics", "/swagger" };
            return skipPaths.Any(skipPath => path.StartsWithSegments(skipPath));
        }

        private async Task<bool> ValidateHeaders(HttpContext context)
        {
            foreach (var header in context.Request.Headers)
            {
                if (ContainsMaliciousContent(header.Value.ToString()))
                {
                    await LogSecurityEvent(context, "MALICIOUS_HEADER", $"Malicious content in header: {header.Key}");
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsync("Invalid request headers");
                    return false;
                }
            }
            return true;
        }

        private async Task<bool> ValidateQueryParameters(HttpContext context)
        {
            foreach (var param in context.Request.Query)
            {
                if (ContainsMaliciousContent(param.Value.ToString()))
                {
                    await LogSecurityEvent(context, "MALICIOUS_QUERY_PARAM", $"Malicious content in query parameter: {param.Key}");
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsync("Invalid query parameters");
                    return false;
                }
            }
            return true;
        }

        private async Task<bool> ValidateRequestBody(HttpContext context)
        {
            if (context.Request.ContentType?.Contains("application/json") == true)
            {
                context.Request.EnableBuffering();
                
                using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, leaveOpen: true);
                var body = await reader.ReadToEndAsync();
                context.Request.Body.Position = 0;

                if (string.IsNullOrEmpty(body))
                    return true;

                // Validate JSON structure
                try
                {
                    JsonDocument.Parse(body);
                }
                catch (JsonException)
                {
                    await LogSecurityEvent(context, "INVALID_JSON", "Invalid JSON format");
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsync("Invalid JSON format");
                    return false;
                }

                // Check for malicious content
                if (ContainsMaliciousContent(body))
                {
                    await LogSecurityEvent(context, "MALICIOUS_BODY", "Malicious content in request body");
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsync("Invalid request content");
                    return false;
                }
            }

            return true;
        }

        private bool ContainsMaliciousContent(string input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            return MaliciousPatterns.Any(pattern => pattern.IsMatch(input));
        }

        private async Task LogSecurityEvent(HttpContext context, string eventType, string description)
        {
            var clientIp = GetClientIpAddress(context);
            var userAgent = context.Request.Headers["User-Agent"].ToString();

            await _securityService.LogSecurityEventAsync(
                eventType,
                description,
                null,
                clientIp,
                userAgent,
                "WARNING",
                false,
                JsonSerializer.Serialize(new
                {
                    Path = context.Request.Path.ToString(),
                    Method = context.Request.Method,
                    QueryString = context.Request.QueryString.ToString()
                })
            );
        }

        private string GetClientIpAddress(HttpContext context)
        {
            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Connection.RemoteIpAddress?.ToString();
            }
            return ipAddress ?? "Unknown";
        }
    }
}
