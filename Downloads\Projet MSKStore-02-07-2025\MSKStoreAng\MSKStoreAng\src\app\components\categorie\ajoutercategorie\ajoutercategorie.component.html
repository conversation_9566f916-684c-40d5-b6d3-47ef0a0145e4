
    <form (submit)="onSubmit()">
      <div class="form-group">
        <label for="title">{{ 'Title' | translate }}</label>
        <input type="text" class="form-control" id="title" name="title"  [(ngModel)]="newCategorie.titre" required>
      </div>
      <div class="form-group">
        <label for="description">{{ 'Description' | translate }}</label>
        <textarea class="form-control" id="description" name="description"  [(ngModel)]="newCategorie.description" required></textarea>
      </div>
      <div class="form-group">
        <label for="parentCategory">{{ 'ParentCategory' | translate }}</label>
        <select id="parentCategory" name="parentCategory" class="form-control" [(ngModel)]="newCategorie.parentid">
          <option *ngFor="let categorie of categories" [value]="categorie.id">{{categorie.id}} - {{ categorie.titre }}</option>
        </select>
      </div>
      <button type="submit" class="btn btn-primary">{{ 'AddCategory' | translate }}</button>
    </form>
  