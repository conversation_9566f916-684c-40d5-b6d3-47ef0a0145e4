import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { Inject } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class KeyboardNavigationService {
  private renderer: Renderer2;
  private isKeyboardUser = false;

  constructor(
    private rendererFactory: RendererFactory2,
    @Inject(DOCUMENT) private document: Document
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    this.initKeyboardDetection();
  }

  private initKeyboardDetection(): void {
    // Detect keyboard usage
    this.renderer.listen(this.document, 'keydown', (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        this.enableKeyboardNavigation();
      }
    });

    // Detect mouse usage
    this.renderer.listen(this.document, 'mousedown', () => {
      this.disableKeyboardNavigation();
    });

    // Handle escape key for closing modals/dropdowns
    this.renderer.listen(this.document, 'keydown', (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        this.handleEscapeKey();
      }
    });
  }

  private enableKeyboardNavigation(): void {
    if (!this.isKeyboardUser) {
      this.isKeyboardUser = true;
      this.renderer.addClass(this.document.body, 'keyboard-navigation');
    }
  }

  private disableKeyboardNavigation(): void {
    if (this.isKeyboardUser) {
      this.isKeyboardUser = false;
      this.renderer.removeClass(this.document.body, 'keyboard-navigation');
    }
  }

  private handleEscapeKey(): void {
    // Close any open modals or dropdowns
    const openModals = this.document.querySelectorAll('.modal.show');
    openModals.forEach(modal => {
      const closeButton = modal.querySelector('.btn-close, .modal-close, [data-dismiss="modal"]') as HTMLElement;
      if (closeButton) {
        closeButton.click();
      }
    });

    // Close any open dropdowns
    const openDropdowns = this.document.querySelectorAll('.dropdown-menu.show');
    openDropdowns.forEach(dropdown => {
      this.renderer.removeClass(dropdown, 'show');
    });
  }

  // Utility method to trap focus within an element
  trapFocus(element: HTMLElement): void {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        if (event.shiftKey) {
          if (this.document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          }
        } else {
          if (this.document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    this.renderer.listen(element, 'keydown', handleTabKey);
    
    // Focus the first element
    firstElement.focus();
  }

  // Utility method to restore focus to a previous element
  restoreFocus(element: HTMLElement): void {
    if (element && typeof element.focus === 'function') {
      element.focus();
    }
  }
}
