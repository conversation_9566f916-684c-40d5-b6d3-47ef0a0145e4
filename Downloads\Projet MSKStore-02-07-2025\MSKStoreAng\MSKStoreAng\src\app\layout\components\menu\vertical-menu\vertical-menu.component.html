<!-- Modern Menu header -->
<div class="modern-menu-header">
  <div class="modern-menu-brand">
    <!-- App Branding -->
    <a class="modern-brand-link" [routerLink]="['/']">
      <span class="modern-brand-logo">
        <img src="{{ coreConfig.app.appLogoImage }}" alt="brand-logo" />
      </span>
      <h2 class="modern-brand-text mb-0" [ngClass]="{'collapsed': isCollapsed}">{{ coreConfig.app.appName }}</h2>
    </a>
  </div>

  <!-- Menu Toggler -->
  <div class="modern-menu-toggle">
    <button class="modern-toggle-btn d-none d-xl-block" (click)="toggleSidebarCollapsible()">
      <i
        [ngClass]="isCollapsed === true ? 'icon-chevron-right' : 'icon-chevron-left'"
        class="modern-toggle-icon feather"
      ></i>
    </button>
    <button class="modern-toggle-btn modern-close-btn d-block d-xl-none" (click)="toggleSidebar()">
      <i data-feather="x" class="modern-toggle-icon"></i>
    </button>
  </div>
</div>
<!--/ Modern Menu header -->

<!-- Modern shadow -->
<div class="modern-menu-shadow" [ngClass]="{ 'visible': isScrolled }"></div>

<!-- Modern Main menu -->
<div class="modern-menu-content" [perfectScrollbar] (scroll)="onSidebarScroll()">
  <nav class="modern-navigation">
    <ul class="modern-navigation-list navigation navigation-main" layout="vertical" core-menu></ul>
  </nav>
</div>
<!--/ Modern Main menu -->
