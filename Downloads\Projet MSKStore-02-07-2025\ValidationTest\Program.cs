﻿using System;
using System.Text.RegularExpressions;

// Test the phone validation regex from PasswordValidationService
string phonePattern = @"^[\+]?[1-9][\d]{0,15}$";
Regex phoneRegex = new Regex(phonePattern);

string[] testPhones = {
    "1234567890",
    "+1234567890",
    "0123456789",
    "123456789",
    "+33123456789",
    "9876543210"
};

Console.WriteLine("Testing phone validation regex: " + phonePattern);
Console.WriteLine();

foreach (string phone in testPhones)
{
    bool isValid = phoneRegex.IsMatch(phone);
    Console.WriteLine($"Phone: '{phone}' -> Valid: {isValid}");
}

// Test email validation
string emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
Regex emailRegex = new Regex(emailPattern);

string[] testEmails = {
    "<EMAIL>",
    "<EMAIL>",
    "invalid-email",
    "<EMAIL>"
};

Console.WriteLine();
Console.WriteLine("Testing email validation regex: " + emailPattern);
Console.WriteLine();

foreach (string email in testEmails)
{
    bool isValid = emailRegex.IsMatch(email);
    Console.WriteLine($"Email: '{email}' -> Valid: {isValid}");
}

// Test name validation
string namePattern = @"^[a-zA-Z\s\-\.\']+$";
Regex nameRegex = new Regex(namePattern);

string[] testNames = {
    "Admin",
    "User",
    "Jean-Pierre",
    "O'Connor",
    "Admin123",
    "Test User"
};

Console.WriteLine();
Console.WriteLine("Testing name validation regex: " + namePattern);
Console.WriteLine();

foreach (string name in testNames)
{
    bool isValid = nameRegex.IsMatch(name);
    Console.WriteLine($"Name: '{name}' -> Valid: {isValid}");
}
