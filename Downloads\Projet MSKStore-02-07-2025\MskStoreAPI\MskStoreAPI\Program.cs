﻿
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json.Serialization;
using Swashbuckle.AspNetCore.Filters;
using System.Text;
using Microsoft.Extensions.FileProviders;
using System.IO;
using MskStoreAPI.Data;
using Microsoft.AspNetCore.Authentication.Cookies;

var myAllowSpecificOrigins = "_myAllowSpecificOrigins";
var builder = WebApplication.CreateBuilder(args);
string connPath = builder.Configuration.GetConnectionString("PathConnection");

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT"
    });
    options.OperationFilter<AuthorizeCheckOperationFilter>();

    options.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
    {    //(\"Bearer {token}\")
        Description = "Standard Authorization header using the Bearer shema (\"Bearer {token}\")",
        In = ParameterLocation.Header,
        Name = "Authorization",
        Type = SecuritySchemeType.ApiKey
    });
    options.OperationFilter<AuthorizeCheckOperationFilter>();
});

builder.Services.AddCors(Option =>
{
    Option.AddPolicy(name: myAllowSpecificOrigins,
        builder =>
        {
            builder.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();

        });
});





// Enhanced JWT Configuration
var jwtKey = builder.Configuration["JwtSettings:SecretKey"] ?? "MSKStore_SecureKey_2024_AES256_CompliantKey_ForProduction_Use_Only_32Chars";
var jwtIssuer = builder.Configuration["JwtSettings:Issuer"] ?? "MSKStore_API";
var jwtAudience = builder.Configuration["JwtSettings:Audience"] ?? "MSKStore_Client";

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtIssuer,
            ValidAudience = jwtAudience,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey)),
            ClockSkew = TimeSpan.FromMinutes(5), // Reduce clock skew tolerance
            RequireExpirationTime = true,
            RequireSignedTokens = true,
            RequireAudience = true
        };

        // Enhanced JWT events for security
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                logger.LogWarning("JWT Authentication failed: {Error}", context.Exception.Message);
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                logger.LogInformation("JWT Token validated for user: {UserId}", context.Principal?.Identity?.Name);
                return Task.CompletedTask;
            },
            OnChallenge = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                logger.LogWarning("JWT Challenge triggered: {Error}", context.Error);
                return Task.CompletedTask;
            }
        };
    });





builder.Services.AddAuthorization(options =>
{
    
    options.AddPolicy("SalePolicy", policy => policy.RequireClaim("permission", "1", "3", "5", "7"));
    options.AddPolicy("DepositPolicy", policy => policy.RequireClaim("permission", "2", "3", "6", "7"));
    options.AddPolicy("ProviderPolicy", policy => policy.RequireClaim("permission", "4", "5", "6", "7"));

});

builder.Services.AddDbContext<DataContext>(options =>
{
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection"));
});

// Register security services
builder.Services.AddScoped<MskStoreAPI.Services.IEncryptionService, MskStoreAPI.Services.EncryptionService>();
builder.Services.AddScoped<MskStoreAPI.Services.ISecurityService, MskStoreAPI.Services.SecurityService>();
builder.Services.AddScoped<MskStoreAPI.Services.IPasswordValidationService, MskStoreAPI.Services.PasswordValidationService>();
builder.Services.AddScoped<MskStoreAPI.Services.ISecurityMonitoringService, MskStoreAPI.Services.SecurityMonitoringService>();

var app = builder.Build();

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<DataContext>();
    context.Database.EnsureCreated();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add comprehensive security middleware (order is important)
// Temporarily disable HTTPS redirection for testing
// app.UseMiddleware<MskStoreAPI.Middleware.HttpsRedirectionMiddleware>();
app.UseMiddleware<MskStoreAPI.Middleware.SecurityHeadersMiddleware>();
app.UseMiddleware<MskStoreAPI.Middleware.InputValidationMiddleware>();
app.UseMiddleware<MskStoreAPI.Middleware.RateLimitingMiddleware>();

/*app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
        Path.Combine(Directory.GetCurrentDirectory(), "assets")),
    RequestPath = "/assets"
});*/
app.UseStaticFiles();
app.UseCors(myAllowSpecificOrigins);
// app.UseHttpsRedirection(); // Temporarily disabled for testing
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

// Ensure database is created with all tables including security tables
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<DataContext>();
    try
    {
        // Ensure database is created
        context.Database.EnsureCreated();

        // Create security tables if they don't exist
        var sql = @"
            -- Create SecurityAuditLogs table
            CREATE TABLE IF NOT EXISTS ""SecurityAuditLogs"" (
                ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_SecurityAuditLogs"" PRIMARY KEY AUTOINCREMENT,
                ""Timestamp"" TEXT NOT NULL,
                ""EventType"" TEXT NOT NULL,
                ""Description"" TEXT NOT NULL,
                ""UserId"" INTEGER NULL,
                ""IpAddress"" TEXT NULL,
                ""UserAgent"" TEXT NULL,
                ""Severity"" TEXT NOT NULL,
                ""AdditionalData"" TEXT NULL,
                ""IsSuccessful"" INTEGER NOT NULL DEFAULT 1
            );

            -- Create LoginAttempts table
            CREATE TABLE IF NOT EXISTS ""LoginAttempts"" (
                ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_LoginAttempts"" PRIMARY KEY AUTOINCREMENT,
                ""Email"" TEXT NOT NULL,
                ""IpAddress"" TEXT NOT NULL,
                ""AttemptTime"" TEXT NOT NULL,
                ""Success"" INTEGER NOT NULL,
                ""FailureReason"" TEXT NULL
            );

            -- Create AccountLockouts table
            CREATE TABLE IF NOT EXISTS ""AccountLockouts"" (
                ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_AccountLockouts"" PRIMARY KEY AUTOINCREMENT,
                ""Email"" TEXT NOT NULL,
                ""LockoutTime"" TEXT NOT NULL,
                ""UnlockTime"" TEXT NULL,
                ""FailedAttempts"" INTEGER NOT NULL,
                ""IsActive"" INTEGER NOT NULL
            );

            -- Create PasswordPolicies table
            CREATE TABLE IF NOT EXISTS ""PasswordPolicies"" (
                ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_PasswordPolicies"" PRIMARY KEY AUTOINCREMENT,
                ""MinimumLength"" INTEGER NOT NULL DEFAULT 8,
                ""MaximumLength"" INTEGER NOT NULL DEFAULT 128,
                ""RequireUppercase"" INTEGER NOT NULL DEFAULT 1,
                ""RequireLowercase"" INTEGER NOT NULL DEFAULT 1,
                ""RequireDigits"" INTEGER NOT NULL DEFAULT 1,
                ""RequireSpecialCharacters"" INTEGER NOT NULL DEFAULT 1,
                ""AllowedSpecialCharacters"" TEXT NOT NULL DEFAULT '!@#$%^&*()_+-=',
                ""PasswordHistoryCount"" INTEGER NOT NULL DEFAULT 5,
                ""PasswordExpirationDays"" INTEGER NOT NULL DEFAULT 90,
                ""PasswordWarningDays"" INTEGER NOT NULL DEFAULT 7,
                ""MaxFailedAttempts"" INTEGER NOT NULL DEFAULT 5,
                ""LockoutDurationMinutes"" INTEGER NOT NULL DEFAULT 30,
                ""EnableProgressiveLockout"" INTEGER NOT NULL DEFAULT 1,
                ""SessionTimeoutMinutes"" INTEGER NOT NULL DEFAULT 60,
                ""MaxConcurrentSessions"" INTEGER NOT NULL DEFAULT 3,
                ""ForceLogoutOnPasswordChange"" INTEGER NOT NULL DEFAULT 1,
                ""EnableTwoFactorAuthentication"" INTEGER NOT NULL DEFAULT 0,
                ""RequirePasswordChangeOnFirstLogin"" INTEGER NOT NULL DEFAULT 1,
                ""PreventPasswordReuse"" INTEGER NOT NULL DEFAULT 1,
                ""ForbiddenPatterns"" TEXT DEFAULT 'password,123456,qwerty,admin,user',
                ""CheckCommonPasswords"" INTEGER NOT NULL DEFAULT 1,
                ""CheckPersonalInformation"" INTEGER NOT NULL DEFAULT 1,
                ""CreatedAt"" TEXT NOT NULL,
                ""UpdatedAt"" TEXT NOT NULL,
                ""IsActive"" INTEGER NOT NULL DEFAULT 1
            );

            -- Create PasswordHistories table
            CREATE TABLE IF NOT EXISTS ""PasswordHistories"" (
                ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_PasswordHistories"" PRIMARY KEY AUTOINCREMENT,
                ""UserId"" INTEGER NOT NULL,
                ""HashedPassword"" TEXT NOT NULL,
                ""CreatedAt"" TEXT NOT NULL,
                ""CreatedByIp"" TEXT NOT NULL
            );

            -- Create indexes for better performance
            CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_Timestamp"" ON ""SecurityAuditLogs"" (""Timestamp"");
            CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_EventType"" ON ""SecurityAuditLogs"" (""EventType"");
            CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_UserId"" ON ""SecurityAuditLogs"" (""UserId"");
            CREATE INDEX IF NOT EXISTS ""IX_LoginAttempts_Email"" ON ""LoginAttempts"" (""Email"");
            CREATE INDEX IF NOT EXISTS ""IX_LoginAttempts_AttemptTime"" ON ""LoginAttempts"" (""AttemptTime"");
            CREATE INDEX IF NOT EXISTS ""IX_AccountLockouts_Email"" ON ""AccountLockouts"" (""Email"");
            CREATE INDEX IF NOT EXISTS ""IX_AccountLockouts_IsActive"" ON ""AccountLockouts"" (""IsActive"");
            CREATE INDEX IF NOT EXISTS ""IX_PasswordHistories_UserId"" ON ""PasswordHistories"" (""UserId"");
            CREATE INDEX IF NOT EXISTS ""IX_PasswordHistories_CreatedAt"" ON ""PasswordHistories"" (""CreatedAt"");

            -- Insert default password policy if none exists
            INSERT OR IGNORE INTO ""PasswordPolicies"" (
                ""Id"", ""MinimumLength"", ""MaximumLength"", ""RequireUppercase"", ""RequireLowercase"",
                ""RequireDigits"", ""RequireSpecialCharacters"", ""AllowedSpecialCharacters"",
                ""PasswordHistoryCount"", ""PasswordExpirationDays"", ""PasswordWarningDays"",
                ""MaxFailedAttempts"", ""LockoutDurationMinutes"", ""EnableProgressiveLockout"",
                ""SessionTimeoutMinutes"", ""MaxConcurrentSessions"", ""ForceLogoutOnPasswordChange"",
                ""EnableTwoFactorAuthentication"", ""RequirePasswordChangeOnFirstLogin"",
                ""PreventPasswordReuse"", ""ForbiddenPatterns"", ""CheckCommonPasswords"",
                ""CheckPersonalInformation"", ""CreatedAt"", ""UpdatedAt"", ""IsActive""
            ) VALUES (
                1, 8, 128, 1, 1, 1, 1, '@#$%*+-=', 5, 90, 7, 5, 30, 1, 60, 3, 1, 0, 1, 1,
                'password,123456,qwerty', 1, 1, datetime('now'), datetime('now'), 1
            );
        ";

        context.Database.ExecuteSqlRaw(sql);
        Console.WriteLine("âœ… Security tables created successfully!");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"âŒ Error creating security tables: {ex.Message}");
    }
}

app.Run();



