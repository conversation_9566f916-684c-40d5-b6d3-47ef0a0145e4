
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json.Serialization;
using Swashbuckle.AspNetCore.Filters;
using System.Text;
using Microsoft.Extensions.FileProviders;
using System.IO;
using MskStoreAPI.Data;
using Microsoft.AspNetCore.Authentication.Cookies;

var myAllowSpecificOrigins = "_myAllowSpecificOrigins";
var builder = WebApplication.CreateBuilder(args);
string connPath = builder.Configuration.GetConnectionString("PathConnection");

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT"
    });
    options.OperationFilter<AuthorizeCheckOperationFilter>();

    options.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
    {    //(\"Bearer {token}\")
        Description = "Standard Authorization header using the Bearer shema (\"Bearer {token}\")",
        In = ParameterLocation.Header,
        Name = "Authorization",
        Type = SecuritySchemeType.ApiKey
    });
    options.OperationFilter<AuthorizeCheckOperationFilter>();
});

builder.Services.AddCors(Option =>
{
    Option.AddPolicy(name: myAllowSpecificOrigins,
        builder =>
        {
            builder.WithOrigins("http://localhost:4200", "http://localhost:5095", "https://localhost:7258")
            .AllowAnyMethod()
            .AllowAnyHeader();

        });
});





builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = connPath,
            ValidAudience = connPath,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes("azertyazertyazerty"))
        };
    });





builder.Services.AddAuthorization(options =>
{
    
    options.AddPolicy("SalePolicy", policy => policy.RequireClaim("permission", "1", "3", "5", "7"));
    options.AddPolicy("DepositPolicy", policy => policy.RequireClaim("permission", "2", "3", "6", "7"));
    options.AddPolicy("ProviderPolicy", policy => policy.RequireClaim("permission", "4", "5", "6", "7"));

});

builder.Services.AddDbContext<DataContext>(options =>
{
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection"));
});

// Register security services
builder.Services.AddScoped<MskStoreAPI.Services.IEncryptionService, MskStoreAPI.Services.EncryptionService>();
builder.Services.AddScoped<MskStoreAPI.Services.ISecurityService, MskStoreAPI.Services.SecurityService>();

var app = builder.Build();

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<DataContext>();
    context.Database.EnsureCreated();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add security middleware
app.UseMiddleware<MskStoreAPI.Middleware.SecurityHeadersMiddleware>();
app.UseMiddleware<MskStoreAPI.Middleware.RateLimitingMiddleware>();

/*app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
        Path.Combine(Directory.GetCurrentDirectory(), "assets")),
    RequestPath = "/assets"
});*/
app.UseStaticFiles();
app.UseCors(myAllowSpecificOrigins);
app.UseHttpsRedirection();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

// Ensure database is created with all tables including security tables
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<DataContext>();
    try
    {
        // Ensure database is created
        context.Database.EnsureCreated();

        // Create security tables if they don't exist
        var sql = @"
            -- Create SecurityAuditLogs table
            CREATE TABLE IF NOT EXISTS ""SecurityAuditLogs"" (
                ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_SecurityAuditLogs"" PRIMARY KEY AUTOINCREMENT,
                ""Timestamp"" TEXT NOT NULL,
                ""EventType"" TEXT NOT NULL,
                ""Description"" TEXT NOT NULL,
                ""UserId"" INTEGER NULL,
                ""IpAddress"" TEXT NULL,
                ""UserAgent"" TEXT NULL,
                ""Severity"" TEXT NOT NULL,
                ""Success"" INTEGER NOT NULL
            );

            -- Create LoginAttempts table
            CREATE TABLE IF NOT EXISTS ""LoginAttempts"" (
                ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_LoginAttempts"" PRIMARY KEY AUTOINCREMENT,
                ""Email"" TEXT NOT NULL,
                ""IpAddress"" TEXT NOT NULL,
                ""AttemptTime"" TEXT NOT NULL,
                ""Success"" INTEGER NOT NULL,
                ""FailureReason"" TEXT NULL
            );

            -- Create AccountLockouts table
            CREATE TABLE IF NOT EXISTS ""AccountLockouts"" (
                ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_AccountLockouts"" PRIMARY KEY AUTOINCREMENT,
                ""Email"" TEXT NOT NULL,
                ""LockoutTime"" TEXT NOT NULL,
                ""UnlockTime"" TEXT NULL,
                ""FailedAttempts"" INTEGER NOT NULL,
                ""IsActive"" INTEGER NOT NULL
            );

            -- Create indexes for better performance
            CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_Timestamp"" ON ""SecurityAuditLogs"" (""Timestamp"");
            CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_EventType"" ON ""SecurityAuditLogs"" (""EventType"");
            CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_UserId"" ON ""SecurityAuditLogs"" (""UserId"");
            CREATE INDEX IF NOT EXISTS ""IX_LoginAttempts_Email"" ON ""LoginAttempts"" (""Email"");
            CREATE INDEX IF NOT EXISTS ""IX_LoginAttempts_AttemptTime"" ON ""LoginAttempts"" (""AttemptTime"");
            CREATE INDEX IF NOT EXISTS ""IX_AccountLockouts_Email"" ON ""AccountLockouts"" (""Email"");
            CREATE INDEX IF NOT EXISTS ""IX_AccountLockouts_IsActive"" ON ""AccountLockouts"" (""IsActive"");
        ";

        context.Database.ExecuteSqlRaw(sql);
        Console.WriteLine("✅ Security tables created successfully!");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ Error creating security tables: {ex.Message}");
    }
}

app.Run();

