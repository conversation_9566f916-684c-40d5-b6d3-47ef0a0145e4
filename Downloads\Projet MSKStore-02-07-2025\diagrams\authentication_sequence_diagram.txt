@startuml
actor Utilisateur
participant Login
participant Home_UserController as "Home/UserController"
participant User as UserDB

Utilisateur -> Login: Saisit les informations de connexion
activate Login
Login -> Home_UserController: HTTP post login informations
activate Home_UserController

alternate email valide
  Home_UserController -> UserDB: Recherche utilisateur par email
  activate UserDB
  UserDB --> Home_UserController: Renvoie l'objet utilisateur
  deactivate UserDB

  Home_UserController -> Home_UserController: Valide le mot de passe

  alt mot de passe valide
    Home_UserController -> Login: Envoie un token jwt
    Login -> Login: Enregistre le token jwt en local storage du navigateur
    Login -> Utilisateur: Redirige vers la page d'accueil et reçoit un message de succès
  else mot de passe invalide
    Home_UserController -> Utilisateur: Envoie un message d'erreur de connexion
    Home_UserController -> Login: Affiche un message d'erreur de connexion et initialise les champs
  end
else email invalide
  Home_UserController -> Utilisateur: Envoie un message d'erreur de connexion
  Home_UserController -> Login: Affiche un message d'erreur de connexion et initialise les champs
end
deactivate Home_UserController
deactivate Login
@enduml

