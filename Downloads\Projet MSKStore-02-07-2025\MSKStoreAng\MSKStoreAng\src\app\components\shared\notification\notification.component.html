<div class="notification-container" role="region" aria-label="Notifications" aria-live="polite">
  <div 
    *ngFor="let notification of notifications; trackBy: trackByFn" 
    class="notification"
    [ngClass]="'notification-' + notification.type"
    role="alert"
    [attr.aria-labelledby]="'notification-title-' + notification.id"
    [attr.aria-describedby]="'notification-message-' + notification.id">
    
    <i 
      [attr.data-feather]="getIconName(notification.type)" 
      class="notification-icon"
      [attr.aria-hidden]="true">
    </i>
    
    <div class="notification-content">
      <div 
        class="notification-title" 
        [id]="'notification-title-' + notification.id">
        {{ notification.title }}
      </div>
      <div 
        class="notification-message" 
        [id]="'notification-message-' + notification.id">
        {{ notification.message }}
      </div>
    </div>
    
    <button 
      type="button"
      class="notification-close"
      (click)="closeNotification(notification.id)"
      [attr.aria-label]="'Close notification: ' + notification.title"
      title="Close notification">
      <i data-feather="x" class="close-icon" aria-hidden="true"></i>
    </button>
  </div>
</div>
