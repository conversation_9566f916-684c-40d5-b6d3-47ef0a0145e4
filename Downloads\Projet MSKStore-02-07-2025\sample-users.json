{"users": [{"name": "Admin", "lastName": "User", "email": "<EMAIL>", "password": "Admin123!", "phone": "1234567890", "role": "Admin", "photo": null}, {"name": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "password": "Employee123!", "phone": "0987654321", "role": "Employee", "cin": 12345678, "typecontrat": "CDI", "salaire": 3000.0, "salairebrut": 3500.0, "permission": 7, "photo": null}, {"name": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "password": "Manager123!", "phone": "1122334455", "role": "Employee", "cin": 87654321, "typecontrat": "CDI", "salaire": 4000.0, "salairebrut": 4500.0, "permission": 3, "photo": null}, {"name": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "password": "Sales123!", "phone": "5566778899", "role": "Employee", "cin": 11223344, "typecontrat": "CDD", "salaire": 2500.0, "salairebrut": 2800.0, "permission": 1, "photo": null}], "authentication_examples": [{"description": "<PERSON><PERSON>", "authemail": "<EMAIL>", "authpassword": "Admin123!"}, {"description": "Emp<PERSON><PERSON> (All Permissions)", "authemail": "<EMAIL>", "authpassword": "Employee123!"}, {"description": "Manager <PERSON><PERSON> (Sale + Deposit)", "authemail": "<EMAIL>", "authpassword": "Manager123!"}, {"description": "Sales Employee Login (Sale Only)", "authemail": "<EMAIL>", "authpassword": "Sales123!"}], "permission_levels": {"1": "Sale Permission Only", "2": "Deposit Permission Only", "3": "Sale + Deposit Permissions", "4": "Provider Permission Only", "5": "Sale + Provider Permissions", "6": "Deposit + Provider Permissions", "7": "All Permissions (Sale + Deposit + Provider)"}, "api_endpoints": {"create_user": "POST /api/Users", "authenticate": "POST /api/Users/<USER>", "get_users": "GET /api/Users", "get_user": "GET /api/Users/<USER>", "update_user": "PUT /api/Users/<USER>", "delete_user": "DELETE /api/Users/<USER>"}, "swagger_testing_steps": ["1. Start backend: dotnet run", "2. Open Swagger: https://localhost:7258/swagger", "3. POST /api/Users - Create admin user with data above", "4. POST /api/Users/<USER>", "5. Copy JWT token from response", "6. <PERSON><PERSON> 'Authorize' button in Swagger", "7. Enter: Bearer YOUR_JWT_TOKEN", "8. Test protected endpoints like GET /api/Users"]}