"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */function patchJasmine(e){e.__load_patch("jasmine",((e,t,n)=>{if(!t)throw new Error("Missing: zone.js");if("undefined"!=typeof jest)return;if("undefined"==typeof jasmine||jasmine.__zone_patch__)return;jasmine.__zone_patch__=!0;const s=t.SyncTestZoneSpec,r=t.ProxyZoneSpec;if(!s)throw new Error("Missing: SyncTestZoneSpec");if(!r)throw new Error("Missing: ProxyZoneSpec");const i=t.current,o=t.__symbol__,c=!0===e[o("fakeAsyncDisablePatchingClock")],a=!c&&(!0===e[o("fakeAsyncPatchLock")]||!0===e[o("fakeAsyncAutoFakeAsyncWhenClockPatched")]);if(!0!==e[o("ignoreUnhandledRejection")]){const t=jasmine.GlobalErrors;t&&!jasmine[o("GlobalErrors")]&&(jasmine[o("GlobalErrors")]=t,jasmine.GlobalErrors=function(){const n=new t,s=n.install;return s&&!n[o("install")]&&(n[o("install")]=s,n.install=function(){const t="undefined"!=typeof process&&!!process.on,n=t?process.listeners("unhandledRejection"):e.eventListeners("unhandledrejection"),r=s.apply(this,arguments);return t?process.removeAllListeners("unhandledRejection"):e.removeAllListeners("unhandledrejection"),n&&n.forEach((n=>{t?process.on("unhandledRejection",n):e.addEventListener("unhandledrejection",n)})),r}),n})}const l=jasmine.getEnv();if(["describe","xdescribe","fdescribe"].forEach((e=>{let t=l[e];l[e]=function(e,n){return t.call(this,e,function r(e,t){return function(){return i.fork(new s(`jasmine.describe#${e}`)).run(t,this,arguments)}}(e,n))}})),["it","xit","fit"].forEach((e=>{let t=l[e];l[o(e)]=t,l[e]=function(e,n,s){return arguments[1]=h(n),t.apply(this,arguments)}})),["beforeEach","afterEach","beforeAll","afterAll"].forEach((e=>{let t=l[e];l[o(e)]=t,l[e]=function(e,n){return arguments[0]=h(e),t.apply(this,arguments)}})),!c){const e=jasmine[o("clock")]=jasmine.clock;jasmine.clock=function(){const n=e.apply(this,arguments);if(!n[o("patched")]){n[o("patched")]=o("patched");const e=n[o("tick")]=n.tick;n.tick=function(){const n=t.current.get("FakeAsyncTestZoneSpec");return n?n.tick.apply(n,arguments):e.apply(this,arguments)};const s=n[o("mockDate")]=n.mockDate;n.mockDate=function(){const e=t.current.get("FakeAsyncTestZoneSpec");if(e){const t=arguments.length>0?arguments[0]:new Date;return e.setFakeBaseSystemTime.apply(e,t&&"function"==typeof t.getTime?[t.getTime()]:arguments)}return s.apply(this,arguments)},a&&["install","uninstall"].forEach((e=>{const s=n[o(e)]=n[e];n[e]=function(){if(!t.FakeAsyncTestZoneSpec)return s.apply(this,arguments);jasmine[o("clockInstalled")]="install"===e}}))}return n}}if(!jasmine[t.__symbol__("createSpyObj")]){const e=jasmine.createSpyObj;jasmine[t.__symbol__("createSpyObj")]=e,jasmine.createSpyObj=function(){const t=Array.prototype.slice.call(arguments);let n;if(t.length>=3&&t[2]){const s=Object.defineProperty;Object.defineProperty=function(e,t,n){return s.call(this,e,t,{...n,configurable:!0,enumerable:!0})};try{n=e.apply(this,t)}finally{Object.defineProperty=s}}else n=e.apply(this,t);return n}}function u(e,n,s,r){const i=!!jasmine[o("clockInstalled")],c=s.testProxyZone;if(i&&a){const n=t[t.__symbol__("fakeAsyncTest")];n&&"function"==typeof n.fakeAsync&&(e=n.fakeAsync(e))}return r?c.run(e,n,[r]):c.run(e,n)}function h(e){return e&&(e.length?function(t){return u(e,this,this.queueRunner,t)}:function(){return u(e,this,this.queueRunner)})}const p=jasmine.QueueRunner;jasmine.QueueRunner=function(n){function s(s){s.onComplete&&(s.onComplete=(e=>()=>{this.testProxyZone=null,this.testProxyZoneSpec=null,i.scheduleMicroTask("jasmine.onComplete",e)})(s.onComplete));const r=e[t.__symbol__("setTimeout")],o=e[t.__symbol__("clearTimeout")];r&&(s.timeout={setTimeout:r||e.setTimeout,clearTimeout:o||e.clearTimeout}),jasmine.UserContext?(s.userContext||(s.userContext=new jasmine.UserContext),s.userContext.queueRunner=this):(s.userContext||(s.userContext={}),s.userContext.queueRunner=this);const c=s.onException;s.onException=function(e){if(e&&"Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL."===e.message){const t=this&&this.testProxyZoneSpec;if(t){const n=t.getAndClearPendingTasksInfo();try{e.message+=n}catch(e){}}}c&&c.call(this,e)},n.call(this,s)}return function(e,t){for(const n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);function n(){this.constructor=e}e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(s,n),s.prototype.execute=function(){let e=t.current,s=!1;for(;e;){if(e===i){s=!0;break}e=e.parent}if(!s)throw new Error("Unexpected Zone: "+t.current.name);this.testProxyZoneSpec=new r,this.testProxyZone=i.fork(this.testProxyZoneSpec),t.currentTask?n.prototype.execute.call(this):t.current.scheduleMicroTask("jasmine.execute().forceTask",(()=>p.prototype.execute.call(this)))},s}(p)}))}function patchJest(e){e.__load_patch("jest",((e,t,n)=>{if("undefined"==typeof jest||jest.__zone_patch__)return;t[n.symbol("ignoreConsoleErrorUncaughtError")]=!0,jest.__zone_patch__=!0;const s=t.ProxyZoneSpec,r=t.SyncTestZoneSpec;if(!s)throw new Error("Missing ProxyZoneSpec");const i=t.current,o=i.fork(new r("jest.describe")),c=new s,a=i.fork(c);function l(e){return function(...t){return o.run(e,this,t)}}function u(e,s=!1){if("function"!=typeof e)return e;const r=function(){if(!0===t[n.symbol("useFakeTimersCalled")]&&e&&!e.isFakeAsync){const n=t[t.__symbol__("fakeAsyncTest")];n&&"function"==typeof n.fakeAsync&&(e=n.fakeAsync(e))}return c.isTestFunc=s,a.run(e,null,arguments)};return Object.defineProperty(r,"length",{configurable:!0,writable:!0,enumerable:!1}),r.length=e.length,r}["describe","xdescribe","fdescribe"].forEach((n=>{let s=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=s,e[n]=function(...e){return e[1]=l(e[1]),s.apply(this,e)},e[n].each=function r(e){return function(...t){const n=e.apply(this,t);return function(...e){return e[1]=l(e[1]),n.apply(this,e)}}}(s.each))})),e.describe.only=e.fdescribe,e.describe.skip=e.xdescribe,["it","xit","fit","test","xtest"].forEach((n=>{let s=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=s,e[n]=function(...e){return e[1]=u(e[1],!0),s.apply(this,e)},e[n].each=function r(e){return function(...t){return function(...n){return n[1]=u(n[1]),e.apply(this,t).apply(this,n)}}}(s.each),e[n].todo=s.todo,e[n].failing=s.failing)})),e.it.only=e.fit,e.it.skip=e.xit,e.test.only=e.fit,e.test.skip=e.xit,["beforeEach","afterEach","beforeAll","afterAll"].forEach((n=>{let s=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=s,e[n]=function(...e){return e[0]=u(e[0]),s.apply(this,e)})})),t.patchJestObject=function e(s,r=!1){function i(){return!!t.current.get("FakeAsyncTestZoneSpec")}function o(){const e=t.current.get("ProxyZoneSpec");return e&&e.isTestFunc}s[n.symbol("fakeTimers")]||(s[n.symbol("fakeTimers")]=!0,n.patchMethod(s,"_checkFakeTimers",(e=>function(t,n){return!!i()||e.apply(t,n)})),n.patchMethod(s,"useFakeTimers",(e=>function(s,i){return t[n.symbol("useFakeTimersCalled")]=!0,r||o()?e.apply(s,i):s})),n.patchMethod(s,"useRealTimers",(e=>function(s,i){return t[n.symbol("useFakeTimersCalled")]=!1,r||o()?e.apply(s,i):s})),n.patchMethod(s,"setSystemTime",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");if(!r||!i())return e.apply(n,s);r.setFakeBaseSystemTime(s[0])})),n.patchMethod(s,"getRealSystemTime",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");return r&&i()?r.getRealSystemTime():e.apply(n,s)})),n.patchMethod(s,"runAllTicks",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");if(!r)return e.apply(n,s);r.flushMicrotasks()})),n.patchMethod(s,"runAllTimers",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");if(!r)return e.apply(n,s);r.flush(100,!0)})),n.patchMethod(s,"advanceTimersByTime",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");if(!r)return e.apply(n,s);r.tick(s[0])})),n.patchMethod(s,"runOnlyPendingTimers",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");if(!r)return e.apply(n,s);r.flushOnlyPendingTimers()})),n.patchMethod(s,"advanceTimersToNextTimer",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");if(!r)return e.apply(n,s);r.tickToNext(s[0])})),n.patchMethod(s,"clearAllTimers",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");if(!r)return e.apply(n,s);r.removeAllTimers()})),n.patchMethod(s,"getTimerCount",(e=>function(n,s){const r=t.current.get("FakeAsyncTestZoneSpec");return r?r.getTimerCount():e.apply(n,s)})))}}))}function patchMocha(e){e.__load_patch("mocha",((e,t)=>{const n=e.Mocha;if(void 0===n)return;if(void 0===t)throw new Error("Missing Zone.js");const s=t.ProxyZoneSpec,r=t.SyncTestZoneSpec;if(!s)throw new Error("Missing ProxyZoneSpec");if(n.__zone_patch__)throw new Error('"Mocha" has already been patched with "Zone".');n.__zone_patch__=!0;const i=t.current,o=i.fork(new r("Mocha.describe"));let c=null;const a=i.fork(new s),l={after:e.after,afterEach:e.afterEach,before:e.before,beforeEach:e.beforeEach,describe:e.describe,it:e.it};function u(e,t,n){for(let s=0;s<e.length;s++){let r=e[s];"function"==typeof r&&(e[s]=0===r.length?t(r):n(r),e[s].toString=function(){return r.toString()})}return e}function h(e){return u(e,(function(e){return function(){return o.run(e,this,arguments)}}))}function p(e){return u(e,(function(e){return function(){return c.run(e,this)}}),(function(e){return function(t){return c.run(e,this,[t])}}))}function d(e){return u(e,(function(e){return function(){return a.run(e,this)}}),(function(e){return function(t){return a.run(e,this,[t])}}))}var f,T;e.describe=e.suite=function(){return l.describe.apply(this,h(arguments))},e.xdescribe=e.suite.skip=e.describe.skip=function(){return l.describe.skip.apply(this,h(arguments))},e.describe.only=e.suite.only=function(){return l.describe.only.apply(this,h(arguments))},e.it=e.specify=e.test=function(){return l.it.apply(this,p(arguments))},e.xit=e.xspecify=e.it.skip=function(){return l.it.skip.apply(this,p(arguments))},e.it.only=e.test.only=function(){return l.it.only.apply(this,p(arguments))},e.after=e.suiteTeardown=function(){return l.after.apply(this,d(arguments))},e.afterEach=e.teardown=function(){return l.afterEach.apply(this,p(arguments))},e.before=e.suiteSetup=function(){return l.before.apply(this,d(arguments))},e.beforeEach=e.setup=function(){return l.beforeEach.apply(this,p(arguments))},f=n.Runner.prototype.runTest,T=n.Runner.prototype.run,n.Runner.prototype.runTest=function(e){t.current.scheduleMicroTask("mocha.forceTask",(()=>{f.call(this,e)}))},n.Runner.prototype.run=function(e){return this.on("test",(e=>{c=i.fork(new s)})),this.on("fail",((e,t)=>{const n=c&&c.get("ProxyZoneSpec");if(n&&t)try{t.message+=n.getAndClearPendingTasksInfo()}catch(e){}})),T.call(this,e)}}))}const global$2=globalThis;function __symbol__(e){return(global$2.__Zone_symbol_prefix||"__zone_symbol__")+e}const __global="undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global;class AsyncTestZoneSpec{finishCallback;failCallback;static get symbolParentUnresolved(){return __symbol__("parentUnresolved")}_pendingMicroTasks=!1;_pendingMacroTasks=!1;_alreadyErrored=!1;_isSync=!1;_existingFinishTimer=null;entryFunction=null;runZone=Zone.current;unresolvedChainedPromiseCount=0;supportWaitUnresolvedChainedPromise=!1;constructor(e,t,n){this.finishCallback=e,this.failCallback=t,this.name="asyncTestZone for "+n,this.properties={AsyncTestZoneSpec:this},this.supportWaitUnresolvedChainedPromise=!0===__global[__symbol__("supportWaitUnResolvedChainedPromise")]}isUnresolvedChainedPromisePending(){return this.unresolvedChainedPromiseCount>0}_finishCallbackIfDone(){null!==this._existingFinishTimer&&(clearTimeout(this._existingFinishTimer),this._existingFinishTimer=null),this._pendingMicroTasks||this._pendingMacroTasks||this.supportWaitUnresolvedChainedPromise&&this.isUnresolvedChainedPromisePending()||this.runZone.run((()=>{this._existingFinishTimer=setTimeout((()=>{this._alreadyErrored||this._pendingMicroTasks||this._pendingMacroTasks||this.finishCallback()}),0)}))}patchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("patchPromiseForTest")];e&&e()}unPatchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("unPatchPromiseForTest")];e&&e()}name;properties;onScheduleTask(e,t,n,s){return"eventTask"!==s.type&&(this._isSync=!1),"microTask"===s.type&&s.data&&s.data instanceof Promise&&!0===s.data[AsyncTestZoneSpec.symbolParentUnresolved]&&this.unresolvedChainedPromiseCount--,e.scheduleTask(n,s)}onInvokeTask(e,t,n,s,r,i){return"eventTask"!==s.type&&(this._isSync=!1),e.invokeTask(n,s,r,i)}onCancelTask(e,t,n,s){return"eventTask"!==s.type&&(this._isSync=!1),e.cancelTask(n,s)}onInvoke(e,t,n,s,r,i,o){this.entryFunction||(this.entryFunction=s);try{return this._isSync=!0,e.invoke(n,s,r,i,o)}finally{this._isSync&&this.entryFunction===s&&this._finishCallbackIfDone()}}onHandleError(e,t,n,s){return e.handleError(n,s)&&(this.failCallback(s),this._alreadyErrored=!0),!1}onHasTask(e,t,n,s){e.hasTask(n,s),t===n&&("microTask"==s.change?(this._pendingMicroTasks=s.microTask,this._finishCallbackIfDone()):"macroTask"==s.change&&(this._pendingMacroTasks=s.macroTask,this._finishCallbackIfDone()))}}function patchAsyncTest(e){e.AsyncTestZoneSpec=AsyncTestZoneSpec,e.__load_patch("asynctest",((e,t,n)=>{function s(e,n,s,r){const i=t.current,o=t.AsyncTestZoneSpec;if(void 0===o)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/async-test");const c=t.ProxyZoneSpec;if(!c)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const a=c.get();c.assertPresent();const l=t.current.getZoneWith("ProxyZoneSpec"),u=a.getDelegate();return l.parent.run((()=>{const e=new o((()=>{a.getDelegate()==e&&a.setDelegate(u),e.unPatchPromiseForTest(),i.run((()=>{s()}))}),(t=>{a.getDelegate()==e&&a.setDelegate(u),e.unPatchPromiseForTest(),i.run((()=>{r(t)}))}),"test");a.setDelegate(e),e.patchPromiseForTest()})),t.current.runGuarded(e,n)}t[n.symbol("asyncTest")]=function t(n){return e.jasmine?function(e){e||((e=function(){}).fail=function(e){throw e}),s(n,this,e,(t=>{if("string"==typeof t)return e.fail(new Error(t));e.fail(t)}))}:function(){return new Promise(((e,t)=>{s(n,this,e,t)}))}}}))}const global$1="object"==typeof window&&window||"object"==typeof self&&self||globalThis.global,OriginalDate=global$1.Date;function FakeDate(){if(0===arguments.length){const e=new OriginalDate;return e.setTime(FakeDate.now()),e}{const e=Array.prototype.slice.call(arguments);return new OriginalDate(...e)}}let patchedTimers;FakeDate.now=function(){const e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():OriginalDate.now.apply(this,arguments)},FakeDate.UTC=OriginalDate.UTC,FakeDate.parse=OriginalDate.parse;const timeoutCallback=function(){};class Scheduler{static nextNodeJSId=1;static nextId=-1;_schedulerQueue=[];_currentTickTime=0;_currentFakeBaseSystemTime=OriginalDate.now();_currentTickRequeuePeriodicEntries=[];constructor(){}static getNextId(){const e=patchedTimers.nativeSetTimeout.call(global$1,timeoutCallback,0);return patchedTimers.nativeClearTimeout.call(global$1,e),"number"==typeof e?e:Scheduler.nextNodeJSId++}getCurrentTickTime(){return this._currentTickTime}getFakeSystemTime(){return this._currentFakeBaseSystemTime+this._currentTickTime}setFakeBaseSystemTime(e){this._currentFakeBaseSystemTime=e}getRealSystemTime(){return OriginalDate.now()}scheduleFunction(e,t,n){let s=(n={args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1,...n}).id<0?Scheduler.nextId:n.id;Scheduler.nextId=Scheduler.getNextId();let r={endTime:this._currentTickTime+t,id:s,func:e,args:n.args,delay:t,isPeriodic:n.isPeriodic,isRequestAnimationFrame:n.isRequestAnimationFrame};n.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(r);let i=0;for(;i<this._schedulerQueue.length&&!(r.endTime<this._schedulerQueue[i].endTime);i++);return this._schedulerQueue.splice(i,0,r),s}removeScheduledFunctionWithId(e){for(let t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}}removeAll(){this._schedulerQueue=[]}getTimerCount(){return this._schedulerQueue.length}tickToNext(e=1,t,n){this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,n)}tick(e=0,t,n){let s=this._currentTickTime+e,r=0;const i=(n=Object.assign({processNewMacroTasksSynchronously:!0},n)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===i.length&&t)t(e);else{for(;i.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(s<i[0].endTime));){let e=i.shift();if(!n.processNewMacroTasksSynchronously){const t=this._schedulerQueue.indexOf(e);t>=0&&this._schedulerQueue.splice(t,1)}if(r=this._currentTickTime,this._currentTickTime=e.endTime,t&&t(this._currentTickTime-r),!e.func.apply(global$1,e.isRequestAnimationFrame?[this._currentTickTime]:e.args))break;n.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((e=>{let t=0;for(;t<i.length&&!(e.endTime<i[t].endTime);t++);i.splice(t,0,e)}))}r=this._currentTickTime,this._currentTickTime=s,t&&t(this._currentTickTime-r)}}flushOnlyPendingTimers(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t}flush(e=20,t=!1,n){return t?this.flushPeriodic(n):this.flushNonPeriodic(e,n)}flushPeriodic(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t}flushNonPeriodic(e,t){const n=this._currentTickTime;let s=0,r=0;for(;this._schedulerQueue.length>0;){if(r++,r>e)throw new Error("flush failed after reaching the limit of "+e+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((e=>!e.isPeriodic&&!e.isRequestAnimationFrame)).length)break;const n=this._schedulerQueue.shift();if(s=this._currentTickTime,this._currentTickTime=n.endTime,t&&t(this._currentTickTime-s),!n.func.apply(global$1,n.args))break}return this._currentTickTime-n}}class FakeAsyncTestZoneSpec{trackPendingRequestAnimationFrame;macroTaskOptions;static assertInZone(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")}_scheduler=new Scheduler;_microtasks=[];_lastError=null;_uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")];pendingPeriodicTimers=[];pendingTimers=[];patchDateLocked=!1;constructor(e,t=!1,n){this.trackPendingRequestAnimationFrame=t,this.macroTaskOptions=n,this.name="fakeAsyncTestZone for "+e,this.macroTaskOptions||(this.macroTaskOptions=global$1[Zone.__symbol__("FakeAsyncTestMacroTask")])}_fnAndFlush(e,t){return(...n)=>(e.apply(global$1,n),null===this._lastError?(null!=t.onSuccess&&t.onSuccess.apply(global$1),this.flushMicrotasks()):null!=t.onError&&t.onError.apply(global$1),null===this._lastError)}static _removeTimer(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}_dequeueTimer(e){return()=>{FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers,e)}}_requeuePeriodicTimer(e,t,n,s){return()=>{-1!==this.pendingPeriodicTimers.indexOf(s)&&this._scheduler.scheduleFunction(e,t,{args:n,isPeriodic:!0,id:s,isRequeuePeriodic:!0})}}_dequeuePeriodicTimer(e){return()=>{FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers,e)}}_setTimeout(e,t,n,s=!0){let r=this._dequeueTimer(Scheduler.nextId),i=this._fnAndFlush(e,{onSuccess:r,onError:r}),o=this._scheduler.scheduleFunction(i,t,{args:n,isRequestAnimationFrame:!s});return s&&this.pendingTimers.push(o),o}_clearTimeout(e){FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_setInterval(e,t,n){let s=Scheduler.nextId,r={onSuccess:null,onError:this._dequeuePeriodicTimer(s)},i=this._fnAndFlush(e,r);return r.onSuccess=this._requeuePeriodicTimer(i,t,n,s),this._scheduler.scheduleFunction(i,t,{args:n,isPeriodic:!0}),this.pendingPeriodicTimers.push(s),s}_clearInterval(e){FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_resetLastErrorAndThrow(){let e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e}getCurrentTickTime(){return this._scheduler.getCurrentTickTime()}getFakeSystemTime(){return this._scheduler.getFakeSystemTime()}setFakeBaseSystemTime(e){this._scheduler.setFakeBaseSystemTime(e)}getRealSystemTime(){return this._scheduler.getRealSystemTime()}static patchDate(){global$1[Zone.__symbol__("disableDatePatching")]||global$1.Date!==FakeDate&&(global$1.Date=FakeDate,FakeDate.prototype=OriginalDate.prototype,FakeAsyncTestZoneSpec.checkTimerPatch())}static resetDate(){global$1.Date===FakeDate&&(global$1.Date=OriginalDate)}static checkTimerPatch(){if(!patchedTimers)throw new Error("Expected timers to have been patched.");global$1.setTimeout!==patchedTimers.setTimeout&&(global$1.setTimeout=patchedTimers.setTimeout,global$1.clearTimeout=patchedTimers.clearTimeout),global$1.setInterval!==patchedTimers.setInterval&&(global$1.setInterval=patchedTimers.setInterval,global$1.clearInterval=patchedTimers.clearInterval)}lockDatePatch(){this.patchDateLocked=!0,FakeAsyncTestZoneSpec.patchDate()}unlockDatePatch(){this.patchDateLocked=!1,FakeAsyncTestZoneSpec.resetDate()}tickToNext(e=1,t,n={processNewMacroTasksSynchronously:!0}){e<=0||(FakeAsyncTestZoneSpec.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(e,t,n),null!==this._lastError&&this._resetLastErrorAndThrow())}tick(e=0,t,n={processNewMacroTasksSynchronously:!0}){FakeAsyncTestZoneSpec.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(e,t,n),null!==this._lastError&&this._resetLastErrorAndThrow()}flushMicrotasks(){for(FakeAsyncTestZoneSpec.assertInZone();this._microtasks.length>0;){let e=this._microtasks.shift();e.func.apply(e.target,e.args)}(()=>{(null!==this._lastError||this._uncaughtPromiseErrors.length)&&this._resetLastErrorAndThrow()})()}flush(e,t,n){FakeAsyncTestZoneSpec.assertInZone(),this.flushMicrotasks();const s=this._scheduler.flush(e,t,n);return null!==this._lastError&&this._resetLastErrorAndThrow(),s}flushOnlyPendingTimers(e){FakeAsyncTestZoneSpec.assertInZone(),this.flushMicrotasks();const t=this._scheduler.flushOnlyPendingTimers(e);return null!==this._lastError&&this._resetLastErrorAndThrow(),t}removeAllTimers(){FakeAsyncTestZoneSpec.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]}getTimerCount(){return this._scheduler.getTimerCount()+this._microtasks.length}name;properties={FakeAsyncTestZoneSpec:this};onScheduleTask(e,t,n,s){switch(s.type){case"microTask":let t,r=s.data&&s.data.args;if(r){let e=s.data.cbIdx;"number"==typeof r.length&&r.length>e+1&&(t=Array.prototype.slice.call(r,e+1))}this._microtasks.push({func:s.invoke,args:t,target:s.data&&s.data.target});break;case"macroTask":switch(s.source){case"setTimeout":s.data.handleId=this._setTimeout(s.invoke,s.data.delay,Array.prototype.slice.call(s.data.args,2));break;case"setImmediate":s.data.handleId=this._setTimeout(s.invoke,0,Array.prototype.slice.call(s.data.args,1));break;case"setInterval":s.data.handleId=this._setInterval(s.invoke,s.data.delay,Array.prototype.slice.call(s.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+s.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":s.data.handleId=this._setTimeout(s.invoke,16,s.data.args,this.trackPendingRequestAnimationFrame);break;default:const e=this.findMacroTaskOption(s);if(e){const t=s.data&&s.data.args,n=t&&t.length>1?t[1]:0;let r=e.callbackArgs?e.callbackArgs:t;e.isPeriodic?(s.data.handleId=this._setInterval(s.invoke,n,r),s.data.isPeriodic=!0):s.data.handleId=this._setTimeout(s.invoke,n,r);break}throw new Error("Unknown macroTask scheduled in fake async test: "+s.source)}break;case"eventTask":s=e.scheduleTask(n,s)}return s}onCancelTask(e,t,n,s){switch(s.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(s.data.handleId);case"setInterval":return this._clearInterval(s.data.handleId);default:const t=this.findMacroTaskOption(s);if(t){const e=s.data.handleId;return t.isPeriodic?this._clearInterval(e):this._clearTimeout(e)}return e.cancelTask(n,s)}}onInvoke(e,t,n,s,r,i,o){try{return FakeAsyncTestZoneSpec.patchDate(),e.invoke(n,s,r,i,o)}finally{this.patchDateLocked||FakeAsyncTestZoneSpec.resetDate()}}findMacroTaskOption(e){if(!this.macroTaskOptions)return null;for(let t=0;t<this.macroTaskOptions.length;t++){const n=this.macroTaskOptions[t];if(n.source===e.source)return n}return null}onHandleError(e,t,n,s){return this._lastError=s,!1}}let _fakeAsyncTestZoneSpec=null;function getProxyZoneSpec(){return Zone&&Zone.ProxyZoneSpec}let _sharedProxyZoneSpec=null,_sharedProxyZone=null;function resetFakeAsyncZone(){_fakeAsyncTestZoneSpec&&_fakeAsyncTestZoneSpec.unlockDatePatch(),_fakeAsyncTestZoneSpec=null,getProxyZoneSpec()?.get()?.resetDelegate(),_sharedProxyZoneSpec?.resetDelegate()}function fakeAsync(e,t={}){const{flush:n=!0}=t,s=function(...t){const s=getProxyZoneSpec();if(!s)throw new Error("ProxyZoneSpec is needed for the fakeAsync() test helper but could not be found. Make sure that your environment includes zone-testing.js");const r=s.assertPresent();if(Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!_fakeAsyncTestZoneSpec){const e=Zone&&Zone.FakeAsyncTestZoneSpec;if(r.getDelegate()instanceof e)throw new Error("fakeAsync() calls can not be nested");_fakeAsyncTestZoneSpec=new e}let s;const i=r.getDelegate();r.setDelegate(_fakeAsyncTestZoneSpec),_fakeAsyncTestZoneSpec.lockDatePatch();try{s=e.apply(this,t),n?_fakeAsyncTestZoneSpec.flush(20,!0):flushMicrotasks()}finally{r.setDelegate(i)}if(!n){if(_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length>0)throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} periodic timer(s) still in the queue.`);if(_fakeAsyncTestZoneSpec.pendingTimers.length>0)throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`)}return s}finally{resetFakeAsyncZone()}};return s.isFakeAsync=!0,s}function _getFakeAsyncZoneSpec(){if(null==_fakeAsyncTestZoneSpec&&(_fakeAsyncTestZoneSpec=Zone.current.get("FakeAsyncTestZoneSpec"),null==_fakeAsyncTestZoneSpec))throw new Error("The code should be running in the fakeAsync zone to call this function");return _fakeAsyncTestZoneSpec}function tick(e=0,t=!1){_getFakeAsyncZoneSpec().tick(e,null,t)}function flush(e){return _getFakeAsyncZoneSpec().flush(e)}function discardPeriodicTasks(){_getFakeAsyncZoneSpec().pendingPeriodicTimers.length=0}function withProxyZone(e){return function(...t){const n=getProxyZoneSpec();if(void 0===n)throw new Error("ProxyZoneSpec is needed for the withProxyZone() test helper but could not be found. Make sure that your environment includes zone-testing.js");return(void 0!==n.get()?Zone.current:getOrCreateRootProxy()).run(e,this,t)}}function getOrCreateRootProxy(){const e=getProxyZoneSpec();if(void 0===e)throw new Error("ProxyZoneSpec is needed for withProxyZone but could not be found. Make sure that your environment includes zone-testing.js");return null===_sharedProxyZoneSpec&&(_sharedProxyZoneSpec=new e),_sharedProxyZone=Zone.root.fork(_sharedProxyZoneSpec),_sharedProxyZone}function flushMicrotasks(){_getFakeAsyncZoneSpec().flushMicrotasks()}function patchFakeAsyncTest(e){e.FakeAsyncTestZoneSpec=FakeAsyncTestZoneSpec,e.__load_patch("fakeasync",((e,t,n)=>{t[n.symbol("fakeAsyncTest")]={resetFakeAsyncZone:resetFakeAsyncZone,flushMicrotasks:flushMicrotasks,discardPeriodicTasks:discardPeriodicTasks,tick:tick,flush:flush,fakeAsync:fakeAsync,withProxyZone:withProxyZone}}),!0),patchedTimers={setTimeout:global$1.setTimeout,setInterval:global$1.setInterval,clearTimeout:global$1.clearTimeout,clearInterval:global$1.clearInterval,nativeSetTimeout:global$1[e.__symbol__("setTimeout")],nativeClearTimeout:global$1[e.__symbol__("clearTimeout")]},Scheduler.nextId=Scheduler.getNextId()}function patchLongStackTrace(e){const t={},n="__creationTrace__",s="STACKTRACE TRACKING",r="__SEP_TAG__";let i=r+"@[native]";class o{error=h();timestamp=new Date}function c(){return new Error(s)}function a(){try{throw c()}catch(e){return e}}const l=c(),u=a(),h=l.stack?c:u.stack?a:c;function p(e){return e.stack?e.stack.split("\n"):[]}function d(e,n){let s=p(n);for(let n=0;n<s.length;n++)t.hasOwnProperty(s[n])||e.push(s[n])}function f(e,t){const n=[t?t.trim():""];if(e){let t=(new Date).getTime();for(let s=0;s<e.length;s++){const o=e[s],c=o.timestamp;let a=`____________________Elapsed ${t-c.getTime()} ms; At: ${c}`;a=a.replace(/[^\w\d]/g,"_"),n.push(i.replace(r,a)),d(n,o.error),t=c.getTime()}}return n.join("\n")}function T(){return Error.stackTraceLimit>0}function m(e,t){t>0&&(e.push(p((new o).error)),m(e,t-1))}e.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(t){if(!t)return;const n=t[e.__symbol__("currentTaskTrace")];return n?f(n,t.stack):t.stack},onScheduleTask:function(t,s,r,i){if(T()){const t=e.currentTask;let s=t&&t.data&&t.data[n]||[];s=[new o].concat(s),s.length>this.longStackTraceLimit&&(s.length=this.longStackTraceLimit),i.data||(i.data={}),"eventTask"===i.type&&(i.data={...i.data}),i.data[n]=s}return t.scheduleTask(r,i)},onHandleError:function(t,s,r,i){if(T()){const t=e.currentTask||i.task;if(i instanceof Error&&t){const e=f(t.data&&t.data[n],i.stack);try{i.stack=i.longStack=e}catch(e){}}}return t.handleError(r,i)}},function y(){if(!T())return;const e=[];m(e,2);const n=e[0],o=e[1];for(let e=0;e<n.length;e++){const t=n[e];if(-1==t.indexOf(s)){let e=t.match(/^\s*at\s+/);if(e){i=e[0]+r+" (http://localhost)";break}}}for(let e=0;e<n.length;e++){const s=n[e];if(s!==o[e])break;t[s]=!0}}()}class ProxyZoneSpec{defaultSpecDelegate;name="ProxyZone";_delegateSpec=null;properties={ProxyZoneSpec:this};propertyKeys=null;lastTaskState=null;isNeedToTriggerHasTask=!1;tasks=[];static get(){return Zone.current.get("ProxyZoneSpec")}static isLoaded(){return ProxyZoneSpec.get()instanceof ProxyZoneSpec}static assertPresent(){const e=ProxyZoneSpec.get();if(void 0===e)throw new Error("Expected to be running in 'ProxyZone', but it was not found.");return e}constructor(e=null){this.defaultSpecDelegate=e,this.setDelegate(e)}setDelegate(e){const t=this._delegateSpec!==e;this._delegateSpec=e,this.propertyKeys&&this.propertyKeys.forEach((e=>delete this.properties[e])),this.propertyKeys=null,e&&e.properties&&(this.propertyKeys=Object.keys(e.properties),this.propertyKeys.forEach((t=>this.properties[t]=e.properties[t]))),t&&this.lastTaskState&&(this.lastTaskState.macroTask||this.lastTaskState.microTask)&&(this.isNeedToTriggerHasTask=!0)}getDelegate(){return this._delegateSpec}resetDelegate(){this.getDelegate(),this.setDelegate(this.defaultSpecDelegate)}tryTriggerHasTask(e,t,n){this.isNeedToTriggerHasTask&&this.lastTaskState&&(this.isNeedToTriggerHasTask=!1,this.onHasTask(e,t,n,this.lastTaskState))}removeFromTasks(e){if(this.tasks)for(let t=0;t<this.tasks.length;t++)if(this.tasks[t]===e)return void this.tasks.splice(t,1)}getAndClearPendingTasksInfo(){if(0===this.tasks.length)return"";const e="--Pending async tasks are: ["+this.tasks.map((e=>{const t=e.data&&Object.keys(e.data).map((t=>t+":"+e.data[t])).join(",");return`type: ${e.type}, source: ${e.source}, args: {${t}}`}))+"]";return this.tasks=[],e}onFork(e,t,n,s){return this._delegateSpec&&this._delegateSpec.onFork?this._delegateSpec.onFork(e,t,n,s):e.fork(n,s)}onIntercept(e,t,n,s,r){return this._delegateSpec&&this._delegateSpec.onIntercept?this._delegateSpec.onIntercept(e,t,n,s,r):e.intercept(n,s,r)}onInvoke(e,t,n,s,r,i,o){return this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onInvoke?this._delegateSpec.onInvoke(e,t,n,s,r,i,o):e.invoke(n,s,r,i,o)}onHandleError(e,t,n,s){return this._delegateSpec&&this._delegateSpec.onHandleError?this._delegateSpec.onHandleError(e,t,n,s):e.handleError(n,s)}onScheduleTask(e,t,n,s){return"eventTask"!==s.type&&this.tasks.push(s),this._delegateSpec&&this._delegateSpec.onScheduleTask?this._delegateSpec.onScheduleTask(e,t,n,s):e.scheduleTask(n,s)}onInvokeTask(e,t,n,s,r,i){return"eventTask"!==s.type&&this.removeFromTasks(s),this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onInvokeTask?this._delegateSpec.onInvokeTask(e,t,n,s,r,i):e.invokeTask(n,s,r,i)}onCancelTask(e,t,n,s){return"eventTask"!==s.type&&this.removeFromTasks(s),this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onCancelTask?this._delegateSpec.onCancelTask(e,t,n,s):e.cancelTask(n,s)}onHasTask(e,t,n,s){this.lastTaskState=s,this._delegateSpec&&this._delegateSpec.onHasTask?this._delegateSpec.onHasTask(e,t,n,s):e.hasTask(n,s)}}function patchProxyZoneSpec(e){e.ProxyZoneSpec=ProxyZoneSpec}function patchSyncTest(e){e.SyncTestZoneSpec=class{runZone=e.current;constructor(e){this.name="syncTestZone for "+e}name;onScheduleTask(e,t,n,s){switch(s.type){case"microTask":case"macroTask":throw new Error(`Cannot call ${s.source} from within a sync test (${this.name}).`);case"eventTask":s=e.scheduleTask(n,s)}return s}}}function patchPromiseTesting(e){e.__load_patch("promisefortest",((e,t,n)=>{const s=n.symbol("state"),r=n.symbol("parentUnresolved");Promise[n.symbol("patchPromiseForTest")]=function e(){let n=Promise[t.__symbol__("ZonePromiseThen")];n||(n=Promise[t.__symbol__("ZonePromiseThen")]=Promise.prototype.then,Promise.prototype.then=function(){const e=n.apply(this,arguments);if(null===this[s]){const n=t.current.get("AsyncTestZoneSpec");n&&(n.unresolvedChainedPromiseCount++,e[r]=!0)}return e})},Promise[n.symbol("unPatchPromiseForTest")]=function e(){const n=Promise[t.__symbol__("ZonePromiseThen")];n&&(Promise.prototype.then=n,Promise[t.__symbol__("ZonePromiseThen")]=void 0)}}))}function rollupTesting(e){patchLongStackTrace(e),patchProxyZoneSpec(e),patchSyncTest(e),patchJasmine(e),patchJest(e),patchMocha(e),patchAsyncTest(e),patchFakeAsyncTest(e),patchPromiseTesting(e)}rollupTesting(Zone);