try {
    Write-Host "Checking login attempts in database..."
    
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Database/check-security-tables" -Method GET
    
    Write-Host "Database check response:"
    Write-Host ($response | ConvertTo-Json)
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "Status Code: $statusCode"
        
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody"
        } catch {
            Write-Host "Could not read response body"
        }
    }
}
