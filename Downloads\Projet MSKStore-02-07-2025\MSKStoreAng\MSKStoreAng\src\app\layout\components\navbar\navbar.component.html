<div *ngIf="horizontalMenu" class="modern-navbar-header d-xl-block d-none">
  <!-- Navbar brand -->
  <ul class="nav navbar-nav flex-row">
    <li class="nav-item">
      <a class="modern-navbar-brand" [routerLink]="['/']">
        <span class="modern-brand-logo">
          <img src="{{ coreConfig.app.appLogoImage }}" alt="brand-logo" width="36" />
        </span>
        <h2 class="modern-brand-text mb-0">{{ coreConfig.app.appName }}</h2>
      </a>
    </li>
  </ul>
  <!--/ Navbar brand -->
</div>

<div class="modern-navbar-container d-flex content">
  <div class="modern-navbar-left d-flex align-items-center">
    <!-- Menu Toggler | Menu icon will be hidden in case of layout without menu -->
    <ul class="nav navbar-nav d-xl-none" *ngIf="!coreConfig.layout.menu.hidden">
      <li class="nav-item">
        <a class="modern-nav-link modern-menu-toggle" (click)="toggleSidebar('menu')">
          <span [data-feather]="'menu'" [class]="'modern-nav-icon'"></span>
        </a>
      </li>
    </ul>
    <!--/ Menu Toggler -->

    <!-- Toggle skin -->
    <li class="nav-item d-none d-lg-block">
      <a type="button" class="modern-nav-link modern-theme-toggle" (click)="toggleDarkSkin()">
        <span [ngClass]="currentSkin === 'dark' ? 'icon-sun' : 'icon-moon'" class="modern-nav-icon feather"></span>
      </a>
    </li>
    <!--/ Toggle skin -->
  </div>


  <!-- Quick Actions -->
  <div class="modern-quick-actions d-flex align-items-center gap-2">
    <!-- quick add Client -->
    <button *ngIf="currentUser && ['Admin', '1', '3', '5', '7'].includes(currentUser.role)"
            class="btn-modern btn-outline btn-sm modern-quick-btn"
            (click)="openAddClient()">
      <i data-feather="user-plus" class="btn-icon"></i>
      <span class="d-none d-md-inline">{{ 'QuickAddClient' | translate }}</span>
    </button>
    <!--/quick add Client -->

    <!-- quick add produit -->
    <button *ngIf="currentUser && ['Admin','2','3','6','7'].includes(currentUser.role)"
            class="btn-modern btn-outline btn-sm modern-quick-btn"
            (click)="openAddProduit()">
      <i data-feather="package" class="btn-icon"></i>
      <span class="d-none d-md-inline">{{ 'QuickAddproduit' | translate }}</span>
    </button>
    <!--/quick add produit -->
  </div>

  <ul class="modern-navbar-nav nav navbar-nav align-items-center ml-auto">
    <!-- Language selection -->
     <li ngbDropdown class="nav-item modern-dropdown dropdown-language">
      <a class="modern-nav-link dropdown-toggle" id="dropdown-flag" ngbDropdownToggle>
        <i class="flag-icon flag-icon-{{ languageOptions[_translateService.currentLang].flag }}"></i
        ><span class="selected-language">{{ languageOptions[_translateService.currentLang].title }}</span></a
      >
      <div ngbDropdownMenu aria-labelledby="dropdown-flag">
        <a *ngFor="let lang of _translateService.getLangs()" ngbDropdownItem (click)="setLanguage(lang)">
          <i class="flag-icon flag-icon-{{ languageOptions[lang]?.flag }}"></i> {{ languageOptions[lang]?.title }}
        </a>
        
      </div>
    </li> 
    <!--/ Language selection -->

    <!-- User Dropdown -->
    <li ngbDropdown class="nav-item dropdown-user">
      <a
        class="nav-link dropdown-toggle dropdown-user-link"
        id="dropdown-user"
        ngbDropdownToggle
        id="navbarUserDropdown"
        aria-haspopup="true"
        aria-expanded="false"
      >
      <div class="user-nav d-sm-flex d-none">
        <span class="user-name font-weight-bolder">{{currentUser?.given_name}} {{currentUser?.family_name}}</span>
        <span *ngIf="currentUser" class="user-status">{{getuserrole()}}</span>
      </div>
        <span class="avatar" *ngIf="!user?.photo"
          ><img
            class="round"
            src="../../../../assets/images/portrait/small/avatar-s-11.jpg"
            alt="avatar"
            height="40"
            width="40" /><span class="avatar-status-online"></span
        ></span>
        <span class="avatar" *ngIf="user?.photo">
          <img class="round" src="{{user?.photo}}" alt="avatar" height="40" width="40" />
          <span class="avatar-status-online"></span>
        </span>
      </a>
      <div ngbDropdownMenu aria-labelledby="navbarUserDropdown" class="dropdown-menu dropdown-menu-right">
        <a ngbDropdownItem [routerLink]="['/user-profile']"><span [data-feather]="'user'" [class]="'mr-50'"></span> {{ 'Profile' | translate }}</a>
        
        <div class="dropdown-divider"></div>
        <a ngbDropdownItem (click)="logout()"><span [data-feather]="'power'" [class]="'mr-50'"></span> {{ 'Logout' | translate }}</a>
      </div>
    </li>
    <!--/ User Dropdown -->
  </ul>
</div>
