import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CoreTranslationService } from '@core/services/translation.service';
import { SecurityService } from '../../services/security.service';
import { Subject, interval } from 'rxjs';
import { takeUntil, startWith, switchMap } from 'rxjs/operators';
import { locale as english } from 'app/main/sample/i18n/en';
import { locale as french } from 'app/main/sample/i18n/fr';
import { locale as arabic } from 'app/main/sample/i18n/ar';

interface SecurityStats {
  status: string;
  alerts: number;
  events: number;
  lockouts: number;
}

interface SecurityEvent {
  eventType: string;
  severity: string;
  timestamp: Date;
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit, OnDestroy {
  private _unsubscribeAll: Subject<any> = new Subject<any>();

  // Dashboard data
  public currentDate = new Date();
  public totalSales = 0;
  public totalOrders = 0;
  public totalProducts = 0;
  public totalCustomers = 0;

  // Security data
  public securityStats: SecurityStats = {
    status: 'Secure',
    alerts: 0,
    events: 0,
    lockouts: 0
  };
  public recentSecurityEvents: SecurityEvent[] = [];

  public contentHeader: object;

  constructor(
    private _translateService: CoreTranslationService,
    private _securityService: SecurityService
  ) {
    this._translateService.translate(english, french, arabic);
  }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit() {
    this.contentHeader = {
      headerTitle: 'Dashboard',
      actionButton: true,
      breadcrumb: {
        type: '',
        links: [
          {
            name: 'Home',
            isLink: true,
            link: '/'
          },
          {
            name: 'Dashboard',
            isLink: false
          }
        ]
      }
    };

    // Load dashboard data
    this.loadDashboardData();
    this.loadSecurityData();

    // Auto-refresh security data every 30 seconds
    interval(30000)
      .pipe(
        startWith(0),
        switchMap(() => this._securityService.getSecurityEvents(1, 5)),
        takeUntil(this._unsubscribeAll)
      )
      .subscribe(
        (response: any) => {
          if (response && response.items) {
            this.recentSecurityEvents = response.items.map((event: any) => ({
              eventType: event.eventType,
              severity: event.severity,
              timestamp: new Date(event.timestamp)
            }));
          }
        },
        (error) => {
          console.error('Error loading security events:', error);
        }
      );
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  // Private Methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Load dashboard statistics
   */
  private loadDashboardData(): void {
    // Mock data - in real implementation, these would come from services
    this.totalSales = 15420;
    this.totalOrders = 89;
    this.totalProducts = 1250;
    this.totalCustomers = 342;
  }

  /**
   * Load security statistics
   */
  private loadSecurityData(): void {
    this._securityService.getSecurityDashboard()
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(
        (data: any) => {
          this.securityStats = {
            status: data.activeLockouts > 0 ? 'Warning' : 'Secure',
            alerts: data.criticalEvents + data.highSeverityEvents,
            events: data.totalEvents,
            lockouts: data.activeLockouts
          };
        },
        (error) => {
          console.error('Error loading security dashboard:', error);
          // Set default values on error
          this.securityStats = {
            status: 'Unknown',
            alerts: 0,
            events: 0,
            lockouts: 0
          };
        }
      );
  }

  // Public Methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get event icon based on event type
   */
  public getEventIcon(eventType: string): string {
    const iconMap: { [key: string]: string } = {
      'LOGIN_SUCCESS': 'log-in',
      'LOGIN_FAILED': 'log-out',
      'PASSWORD_CHANGE': 'key',
      'ACCOUNT_LOCKED': 'lock',
      'SECURITY_SCAN': 'search',
      'RATE_LIMIT_EXCEEDED': 'alert-triangle',
      'SUSPICIOUS_ACTIVITY': 'alert-circle',
      'DATA_ACCESS': 'database',
      'SYSTEM_ERROR': 'x-circle'
    };
    return iconMap[eventType] || 'info';
  }

  /**
   * Get event icon class based on severity
   */
  public getEventIconClass(severity: string): string {
    const classMap: { [key: string]: string } = {
      'CRITICAL': 'bg-light-danger',
      'HIGH': 'bg-light-warning',
      'MEDIUM': 'bg-light-info',
      'LOW': 'bg-light-success'
    };
    return classMap[severity] || 'bg-light-secondary';
  }
}
