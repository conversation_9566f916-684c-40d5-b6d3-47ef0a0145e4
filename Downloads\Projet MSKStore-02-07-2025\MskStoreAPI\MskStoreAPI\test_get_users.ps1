try {
    Write-Host "Getting all users from database..."
    
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method GET
    
    Write-Host "Users found:"
    foreach ($user in $response) {
        Write-Host "- Email: $($user.email), Name: $($user.name) $($user.lastName), Role: $($user.role)"
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "Status Code: $statusCode"
        
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody"
        } catch {
            Write-Host "Could not read response body"
        }
    }
}
