@startuml
actor employé
rectangle "Système" {
  usecase "Gérer les produits" as UC_GererProduits
  usecase "Ajouter un produit" as UC_AjouterProduit
  usecase "Consulter les produits" as UC_ConsulterProduits
  usecase "Supprimer un produit" as UC_SupprimerProduit
  usecase "Modifier un produit" as UC_ModifierProduit
  usecase "Gérer les catégories" as UC_GererCategories
  usecase "Ajouter une catégorie" as UC_AjouterCategorie
  usecase "Consulter les catégories" as UC_ConsulterCategories
  usecase "Supprimer une catégorie" as UC_SupprimerCategorie
  usecase "Modifier une catégorie" as UC_ModifierCategorie
  usecase "Gérer les colonnes" as UC_GererColonnes
  usecase "Ajouter une colonne" as UC_AjouterColonne
  usecase "Consulter les colonnes" as UC_ConsulterColonnes
  usecase "Supprimer une colonne" as UC_SupprimerColonne
  usecase "Modifier une colonne" as UC_ModifierColonne
  usecase "Gérer les rangements" as UC_GererRangements
  usecase "Ajouter un rangement" as UC_AjouterRangement
  usecase "Consulter les rangements" as UC_ConsulterRangements
  usecase "Supprimer un rangement" as UC_SupprimerRangement
  usecase "Modifier un rangement" as UC_ModifierRangement

  employé -- UC_GererProduits
  employé -- UC_GererCategories
  employé -- UC_GererColonnes
  employé -- UC_GererRangements

  UC_GererProduits <.. UC_AjouterProduit : <<include>>
  UC_GererProduits <.. UC_ConsulterProduits : <<extends>>
  UC_ConsulterProduits <.. UC_SupprimerProduit : <<extends>>
  UC_ConsulterProduits <.. UC_ModifierProduit : <<extends>>

  UC_GererCategories <.. UC_AjouterCategorie : <<include>>
  UC_GererCategories <.. UC_ConsulterCategories : <<extends>>
  UC_ConsulterCategories <.. UC_SupprimerCategorie : <<extends>>
  UC_ConsulterCategories <.. UC_ModifierCategorie : <<extends>>

  UC_GererColonnes <.. UC_AjouterColonne : <<include>>
  UC_GererColonnes <.. UC_ConsulterColonnes : <<extends>>
  UC_ConsulterColonnes <.. UC_SupprimerColonne : <<extends>>
  UC_ConsulterColonnes <.. UC_ModifierColonne : <<extends>>

  UC_GererRangements <.. UC_AjouterRangement : <<include>>
  UC_GererRangements <.. UC_ConsulterRangements : <<extends>>
  UC_ConsulterRangements <.. UC_SupprimerRangement : <<extends>>
  UC_ConsulterRangements <.. UC_ModifierRangement : <<extends>>
}
@enduml

