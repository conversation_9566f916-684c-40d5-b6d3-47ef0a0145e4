import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter, map } from 'rxjs/operators';

export interface BreadcrumbItem {
  label: string;
  url: string;
  icon?: string;
  active?: boolean;
}

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss']
})
export class BreadcrumbComponent implements OnInit {
  breadcrumbs: BreadcrumbItem[] = [];

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        map(() => this.buildBreadcrumbs(this.activatedRoute.root))
      )
      .subscribe(breadcrumbs => {
        this.breadcrumbs = breadcrumbs;
      });

    // Initial breadcrumb build
    this.breadcrumbs = this.buildBreadcrumbs(this.activatedRoute.root);
  }

  private buildBreadcrumbs(route: ActivatedRoute, url: string = '', breadcrumbs: BreadcrumbItem[] = []): BreadcrumbItem[] {
    // Add home breadcrumb if this is the first call
    if (breadcrumbs.length === 0) {
      breadcrumbs.push({
        label: 'Home',
        url: '/',
        icon: 'home'
      });
    }

    const children: ActivatedRoute[] = route.children;

    if (children.length === 0) {
      return breadcrumbs;
    }

    for (const child of children) {
      const routeURL: string = child.snapshot.url.map(segment => segment.path).join('/');
      if (routeURL !== '') {
        url += `/${routeURL}`;
        
        // Get breadcrumb label from route data or use default
        const label = child.snapshot.data['breadcrumb'] || this.getDefaultLabel(routeURL);
        const icon = child.snapshot.data['breadcrumbIcon'];
        
        breadcrumbs.push({
          label,
          url,
          icon
        });
      }
      
      return this.buildBreadcrumbs(child, url, breadcrumbs);
    }

    return breadcrumbs;
  }

  private getDefaultLabel(routeURL: string): string {
    // Convert route segments to readable labels
    const labelMap: { [key: string]: string } = {
      'listerclient': 'Clients',
      'ajouterclient': 'Add Client',
      'modifierclient': 'Edit Client',
      'afficherclient': 'View Client',
      'listerproduit': 'Products',
      'ajouterproduit': 'Add Product',
      'modifierproduit': 'Edit Product',
      'afficherproduit': 'View Product',
      'listercommandeclient': 'Orders',
      'affichercommandeclient': 'View Order',
      'listerfacture': 'Invoices',
      'afficherfactureclient': 'View Invoice',
      'listerutilisateur': 'Users',
      'afficherutilisateur': 'View User',
      'listertypeticketresto': 'Ticket Types',
      'affichertypeticketresto': 'View Ticket Type'
    };

    return labelMap[routeURL] || routeURL.charAt(0).toUpperCase() + routeURL.slice(1);
  }
}
