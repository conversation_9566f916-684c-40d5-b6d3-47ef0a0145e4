import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { SecurityService } from '../services/security.service';

@Injectable()
export class SecurityInterceptor implements HttpInterceptor {
  private readonly SECURITY_HEADERS = {
    'X-Requested-With': 'XMLHttpRequest',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };

  constructor(
    private securityService: SecurityService,
    private router: Router
  ) {}

  intercept(request: HttpRequest<any>, next: <PERSON>tt<PERSON><PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    // Clone the request and add security headers
    let secureRequest = this.addSecurityHeaders(request);
    
    // Add authentication token if available
    secureRequest = this.addAuthToken(secureRequest);
    
    // Add request validation
    secureRequest = this.validateRequest(secureRequest);
    
    // Log outgoing request
    this.logRequest(secureRequest);

    return next.handle(secureRequest).pipe(
      tap((event: HttpEvent<any>) => {
        if (event instanceof HttpResponse) {
          this.handleSuccessResponse(event);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        return this.handleErrorResponse(error);
      })
    );
  }

  /**
   * Add security headers to request
   */
  private addSecurityHeaders(request: HttpRequest<any>): HttpRequest<any> {
    let headers = request.headers;
    
    // Add security headers
    Object.entries(this.SECURITY_HEADERS).forEach(([key, value]) => {
      headers = headers.set(key, value);
    });

    // Add CSRF protection for state-changing requests
    if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
      const csrfToken = this.generateCSRFToken();
      headers = headers.set('X-CSRF-Token', csrfToken);
    }

    // Add request timestamp for replay attack prevention
    headers = headers.set('X-Request-Timestamp', Date.now().toString());

    // Add request ID for tracking
    headers = headers.set('X-Request-ID', this.generateRequestId());

    return request.clone({ headers });
  }

  /**
   * Add authentication token to request
   */
  private addAuthToken(request: HttpRequest<any>): HttpRequest<any> {
    const token = this.securityService.getSecureToken();
    
    if (token && !this.isPublicEndpoint(request.url)) {
      return request.clone({
        setHeaders: {
          'Authorization': `Bearer ${token}`
        }
      });
    }
    
    return request;
  }

  /**
   * Validate request before sending
   */
  private validateRequest(request: HttpRequest<any>): HttpRequest<any> {
    // Validate URL
    if (!this.isValidUrl(request.url)) {
      this.securityService.logSecurityEvent({
        type: 'INVALID_REQUEST_URL',
        description: `Invalid request URL: ${request.url}`,
        timestamp: new Date(),
        severity: 'HIGH'
      });
      throw new Error('Invalid request URL');
    }

    // Validate request body for potential XSS/injection
    if (request.body && typeof request.body === 'object') {
      const sanitizedBody = this.sanitizeRequestBody(request.body);
      return request.clone({ body: sanitizedBody });
    }

    return request;
  }

  /**
   * Sanitize request body to prevent XSS and injection attacks
   */
  private sanitizeRequestBody(body: any): any {
    if (typeof body === 'string') {
      return this.sanitizeString(body);
    }
    
    if (Array.isArray(body)) {
      return body.map(item => this.sanitizeRequestBody(item));
    }
    
    if (typeof body === 'object' && body !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(body)) {
        sanitized[key] = this.sanitizeRequestBody(value);
      }
      return sanitized;
    }
    
    return body;
  }

  /**
   * Sanitize string input
   */
  private sanitizeString(input: string): string {
    if (typeof input !== 'string') return input;
    
    // Remove potentially dangerous patterns
    return input
      .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/data:/gi, '') // Remove data: protocol
      .replace(/vbscript:/gi, '') // Remove vbscript: protocol
      .trim();
  }

  /**
   * Check if URL is valid and allowed
   */
  private isValidUrl(url: string): boolean {
    try {
      // Allow relative URLs and specific domains
      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
        return true;
      }
      
      const urlObj = new URL(url);
      const allowedDomains = ['localhost', '127.0.0.1', 'mskstore.com'];
      
      return allowedDomains.some(domain => 
        urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
      );
    } catch {
      return false;
    }
  }

  /**
   * Check if endpoint is public (doesn't require authentication)
   */
  private isPublicEndpoint(url: string): boolean {
    const publicEndpoints = [
      '/api/users/authenticate',
      '/api/auth/login',
      '/api/auth/register',
      '/api/health',
      '/api/public'
    ];
    
    return publicEndpoints.some(endpoint => url.includes(endpoint));
  }

  /**
   * Generate CSRF token
   */
  private generateCSRFToken(): string {
    return 'csrf_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return 'req_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  /**
   * Log outgoing request
   */
  private logRequest(request: HttpRequest<any>): void {
    this.securityService.logSecurityEvent({
      type: 'HTTP_REQUEST',
      description: `${request.method} request to ${request.url}`,
      timestamp: new Date(),
      severity: 'LOW'
    });
  }

  /**
   * Handle successful response
   */
  private handleSuccessResponse(response: HttpResponse<any>): void {
    // Validate response headers for security
    this.validateResponseHeaders(response);
    
    // Log successful response
    this.securityService.logSecurityEvent({
      type: 'HTTP_RESPONSE_SUCCESS',
      description: `Successful response from ${response.url}`,
      timestamp: new Date(),
      severity: 'LOW'
    });
  }

  /**
   * Handle error response
   */
  private handleErrorResponse(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Une erreur est survenue';
    let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM';

    switch (error.status) {
      case 401:
        errorMessage = 'Session expirée. Veuillez vous reconnecter.';
        severity = 'HIGH';
        this.handleUnauthorized();
        break;
      case 403:
        errorMessage = 'Accès refusé.';
        severity = 'HIGH';
        break;
      case 404:
        errorMessage = 'Ressource non trouvée.';
        severity = 'LOW';
        break;
      case 429:
        errorMessage = 'Trop de requêtes. Veuillez patienter.';
        severity = 'MEDIUM';
        break;
      case 500:
        errorMessage = 'Erreur serveur interne.';
        severity = 'HIGH';
        break;
      case 0:
        errorMessage = 'Erreur de connexion réseau.';
        severity = 'CRITICAL';
        break;
      default:
        errorMessage = `Erreur ${error.status}: ${error.message}`;
        severity = 'MEDIUM';
    }

    // Log security event
    this.securityService.logSecurityEvent({
      type: 'HTTP_ERROR',
      description: `HTTP ${error.status} error: ${error.message}`,
      timestamp: new Date(),
      severity
    });

    return throwError(() => new Error(errorMessage));
  }

  /**
   * Handle unauthorized response
   */
  private handleUnauthorized(): void {
    this.securityService.clearSecureToken();
    this.router.navigate(['/auth/login'], {
      queryParams: { reason: 'unauthorized' }
    });
  }

  /**
   * Validate response headers for security
   */
  private validateResponseHeaders(response: HttpResponse<any>): void {
    const headers = response.headers;
    
    // Check for security headers
    const requiredHeaders = [
      'X-Content-Type-Options',
      'X-Frame-Options',
      'Cache-Control'
    ];
    
    const missingHeaders = requiredHeaders.filter(header => !headers.has(header));
    
    if (missingHeaders.length > 0) {
      this.securityService.logSecurityEvent({
        type: 'MISSING_SECURITY_HEADERS',
        description: `Missing security headers: ${missingHeaders.join(', ')}`,
        timestamp: new Date(),
        severity: 'MEDIUM'
      });
    }
  }
}
