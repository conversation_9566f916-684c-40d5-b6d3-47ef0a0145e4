<form (submit)="onSubmit()">
 

    
  
    <div class="form-row">
      <div class="form-group col-md-6">
        <label for="produitId">{{ 'barcode' | translate }}:</label>
        <input type="number" class="form-control" id="produitId" name="produitId" [(ngModel)]="barcode" >
      </div>
    
      <div class="form-group col-md-5">
        <label for="quantite">{{ 'Quantité' | translate }}:</label>
        <input type="number" class="form-control" name="quantite" required [(ngModel)]="devisligneproduit.quantite">
      </div>
    
  
    
      <div class="form-group col-md-1 align-self-end">
        <button type="button" class="btn btn-primary btn-block" (click)="addDevisLigneproduit()">{{ 'Add' | translate }}</button>
  
      </div>
    </div>
  
  
    <table class="table">
      <thead>
        <tr>
          <th>{{ 'Produit' | translate }}</th>
          <th>{{ 'Quantité' | translate }}</th>
          <th>{{ 'Prixunitaire' | translate }}</th>
          <th>{{ 'Total' | translate }}</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let devisligneproduit of devisligneproduits">
          <td>{{devisligneproduit.produit.nom}}</td>
          <td><input type="number" [(ngModel)]="devisligneproduit.quantite" (ngModelChange)="updateTotal()"
            [ngModelOptions]="{standalone: true}" /></td>
          <td> <input type="number" [(ngModel)]="devisligneproduit.prix" (ngModelChange)="updateTotal()"
            [ngModelOptions]="{standalone: true}" /></td>
          
          <td>{{devisligneproduit.quantite*devisligneproduit.prix}}</td>
          <td>
            <button type="button" class="btn btn-danger btn-sm" (click)="removeLigne(devisligneproduit)">{{ 'Remove' | translate }}</button>
          </td>
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <td></td>
          <td></td>
          <td></td>
          <td><h4>{{ total }}</h4></td>
          <td></td>
        </tr>
      </tfoot>
    </table>
  <hr> <!-- optional visual separator -->
    <div class="form-group">
      <button type="submit" class="btn btn-primary" >{{ 'Adddevis' | translate }}</button>
    </div>
  </form>
  