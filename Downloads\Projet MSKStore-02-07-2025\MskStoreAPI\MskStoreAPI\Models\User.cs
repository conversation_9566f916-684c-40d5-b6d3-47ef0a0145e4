﻿using MskStoreAPI.Data;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MskStoreAPI.Models
{
    public class User
    {
        [Key]
        public int id { get; set; }
        public string? name { get; set; }
        public string? lastName { get; set; }
        public string? email { get; set; }
        public string? password { get; set; }

        public string? phone { get; set; }
        public string? role { get; set; }
        public string? photo { get; set; }
        public string? token { get; set; }

        // Security-related properties
        public string? authemail { get; set; }
        public int? authid { get; set; }
        public DateTime? authpasswordchangedate { get; set; }
        public bool? authrequirepasswordchange { get; set; }
    }




    public class Employee : User
    {
        public int? cin { get; set; }
        public string? typecontrat { get; set; }
        public decimal? salaire { get; set; }
        public decimal? salairebrut { get; set; }
        public int? permission { get; set; }
    }

    public class Admin : User
    {
    }
}
