$body = @{
    name = "Test"
    lastName = "User"
    email = "<EMAIL>"
    password = "Test123!"
    phone = "1234567890"
    role = "Admin"
} | ConvertTo-Json

try {
    Write-Host "Testing HTTPS endpoint..."
    $response = Invoke-RestMethod -Uri "https://localhost:7258/api/Users" -Method POST -ContentType "application/json" -Body $body -SkipCertificateCheck
    Write-Host "SUCCESS! User created:"
    Write-Host ($response | ConvertTo-Json)
} catch {
    Write-Host "HTTPS ERROR: $($_.Exception.Message)"
    
    try {
        Write-Host "Testing HTTP endpoint..."
        $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $body
        Write-Host "SUCCESS! User created:"
        Write-Host ($response | ConvertTo-Json)
    } catch {
        Write-Host "HTTP ERROR: $($_.Exception.Message)"
    }
}
