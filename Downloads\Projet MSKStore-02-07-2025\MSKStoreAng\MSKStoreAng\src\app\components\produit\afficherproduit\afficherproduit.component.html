  <div class="modern-product-profile">
    <div class="product-header">
      <div class="product-title-section">
        <h2 class="product-name">
          <i data-feather="package" class="product-icon"></i>
          {{ produit?.nom }}
        </h2>
        <div class="product-badges">
          <span class="product-badge barcode-badge">
            <i data-feather="hash" class="badge-icon"></i>
            {{ produit?.codebar }}
          </span>
          <span class="product-badge category-badge" *ngIf="produit?.categorieid">
            <i data-feather="folder" class="badge-icon"></i>
            Category: {{ produit?.categorieid }}
          </span>
          <span class="product-badge piece-badge" *ngIf="produit?.ispiece">
            <i data-feather="layers" class="badge-icon"></i>
            Piece Product
          </span>
        </div>
      </div>

      <div class="product-actions">
        <button class="btn-modern btn-primary" (click)="onModify()">
          <i data-feather="edit" class="btn-icon"></i>
          {{ 'Modify' | translate }}
        </button>
        <button class="btn-modern btn-danger" (click)="onDelete()">
          <i data-feather="trash-2" class="btn-icon"></i>
          {{ 'Delete' | translate }}
        </button>
      </div>
    </div>

    <div class="product-content">
      <div class="product-main-info">
        <!-- Product Images Section -->
        <div class="product-images" *ngIf="produit?.photoproduits?.length > 0; else noImages">
          <ngb-carousel [interval]="false" class="modern-carousel">
            <ng-template ngbSlide *ngFor="let photo of produit.photoproduits">
              <div class="carousel-image-container">
                <img [src]="photo.imageUrl" [alt]="produit?.nom" class="product-image">
              </div>
            </ng-template>
          </ngb-carousel>
        </div>

        <ng-template #noImages>
          <div class="no-images-placeholder">
            <i data-feather="image" class="placeholder-icon"></i>
            <span>No images available</span>
          </div>
        </ng-template>

        <!-- Barcode Section -->
        <div class="barcode-section" *ngIf="produit?.codebar">
          <div class="barcode-header">
            <h4 class="section-title">
              <i data-feather="hash" class="section-icon"></i>
              Barcode Information
            </h4>
            <div class="barcode-actions">
              <button class="btn-modern btn-secondary btn-sm" (click)="printqrcode()">
                <i data-feather="printer" class="btn-icon"></i>
                {{ 'printQRcode' | translate }}
              </button>
              <button class="btn-modern btn-secondary btn-sm" (click)="printbarcode()">
                <i data-feather="printer" class="btn-icon"></i>
                {{ 'printbarcode' | translate }}
              </button>
            </div>
          </div>

          <div class="barcode-display">
            <div class="qr-code-container">
              <div class="code-label">QR Code</div>
              <div id="qrcode" #qrcode class="qr-code">
                <qrcode
                  [qrdata]="produit?.codebar?.toString()"
                  [elementType]="'svg'"
                  [width]="120"
                  [errorCorrectionLevel]="'M'">
                </qrcode>
              </div>
            </div>

            <div class="barcode-container">
              <div class="code-label">Barcode</div>
              <div id="barcode" class="barcode">
                <ngx-barcode
                  [bc-value]="produit?.codebar"
                  bc-format="code128"
                  [bc-height]="77"
                  [bc-width]="1.3">
                </ngx-barcode>
              </div>
            </div>
          </div>
        </div>

        <!-- Product Information Sections -->
        <div class="product-info-sections">
          <!-- Basic Information -->
          <div class="info-section">
            <h4 class="section-title">
              <i data-feather="info" class="section-icon"></i>
              Basic Information
            </h4>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="palette" class="info-icon"></i>
                  {{ 'Color' | translate }}
                </div>
                <div class="info-value">{{ produit?.color || 'Not specified' }}</div>
              </div>
              <div class="info-item full-width">
                <div class="info-label">
                  <i data-feather="file-text" class="info-icon"></i>
                  {{ 'Description' | translate }}
                </div>
                <div class="info-value">{{ produit?.description || 'No description available' }}</div>
              </div>
            </div>
          </div>

          <!-- Pricing Information -->
          <div class="info-section">
            <h4 class="section-title">
              <i data-feather="dollar-sign" class="section-icon"></i>
              Pricing Information
            </h4>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="shopping-cart" class="info-icon"></i>
                  {{ 'ProviderPrice' | translate }}
                </div>
                <div class="info-value price-value">{{ produit?.prixFournisseur | currency }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="tag" class="info-icon"></i>
                  {{ 'PacketPrice' | translate }}
                </div>
                <div class="info-value price-value selling-price">{{ produit?.prixvente | currency }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="percent" class="info-icon"></i>
                  {{ 'PriceReduction' | translate }}
                </div>
                <div class="info-value">{{ produit?.reductionprix | currency }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="calculator" class="info-icon"></i>
                  {{ 'Tax' | translate }}
                </div>
                <div class="info-value">{{ produit?.taxe | currency }}</div>
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="info-section">
            <h4 class="section-title">
              <i data-feather="calendar" class="section-icon"></i>
              Additional Information
            </h4>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="calendar" class="info-icon"></i>
                  {{ 'ExpirationDate' | translate }}
                </div>
                <div class="info-value">{{ produit?.dateexpiration | date:'dd/MM/yyyy' || 'No expiration date' }}</div>
              </div>
              <div class="info-item" *ngIf="produit?.ispiece">
                <div class="info-label">
                  <i data-feather="link" class="info-icon"></i>
                  {{ 'ispieceof' | translate }}
                </div>
                <div class="info-value">{{ produit?.produitparent }} - {{ produitparent?.nom }}</div>
              </div>
              <div class="info-item" *ngIf="produit?.ispiece">
                <div class="info-label">
                  <i data-feather="package" class="info-icon"></i>
                  {{ 'QuantityinPacket' | translate }}
                </div>
                <div class="info-value">{{ produit?.qteInPacket }}</div>
              </div>
            </div>
          </div>

          <!-- Stock Information -->
          <div class="info-section" *ngIf="!produit?.ispiece && rangements?.length > 0">
            <h4 class="section-title">
              <i data-feather="archive" class="section-icon"></i>
              {{ 'Rangements' | translate }}
            </h4>
            <div class="rangements-list">
              <div class="rangement-item" *ngFor="let rangement of rangements">
                <div class="rangement-info">
                  <div class="rangement-location">
                    <i data-feather="map-pin" class="location-icon"></i>
                    <span>{{ getDepotnom(rangement.idcolonne) }} / {{ getColonnenom(rangement.idcolonne) }}</span>
                  </div>
                  <div class="rangement-name">
                    <a [routerLink]="['/afficherrangement', rangement.id]" class="rangement-link">
                      {{ rangement.nom }}
                    </a>
                  </div>
                </div>
                <div class="stock-info">
                  <span class="stock-label">{{ 'stock' | translate }}</span>
                  <span class="stock-value">{{ rangement.stock }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>