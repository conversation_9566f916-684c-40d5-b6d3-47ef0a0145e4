-- MSKStore Default Users Creation Script
-- Run this after database migration is complete

USE storedbAdel;
GO

-- Check if users already exist
IF NOT EXISTS (SELECT 1 FROM [User] WHERE email = '<EMAIL>')
BEGIN
    -- Create Admin User
    -- Password: Admin123! (BCrypt hashed)
    INSERT INTO [User] (name, lastName, email, password, phone, role, photo, token)
    VALUES (
        'Admin', 
        'User', 
        '<EMAIL>', 
        '$2a$11$rQZJKAx6vs7hGcgdEXEOUeJ5kPyKxM5oKxvfJKxvfJKxvfJKxvfJK', -- Admin123!
        '1234567890', 
        'Admin', 
        NULL, 
        NULL
    );
    PRINT 'Admin user created successfully';
END
ELSE
BEGIN
    PRINT 'Admin user already exists';
END

-- Check if employee exists
IF NOT EXISTS (SELECT 1 FROM [User] WHERE email = '<EMAIL>')
BEGIN
    -- Create Employee User with all permissions
    -- Password: Employee123! (BCrypt hashed)
    INSERT INTO [User] (name, lastName, email, password, phone, role, photo, token, cin, typecontrat, salaire, salairebrut, permission)
    VALUES (
        'John', 
        'Doe', 
        '<EMAIL>', 
        '$2a$11$rQZJKAx6vs7hGcgdEXEOUeJ5kPyKxM5oKxvfJKxvfJKxvfJKxvfJK', -- Employee123!
        '0987654321', 
        'Employee', 
        NULL, 
        NULL,
        12345678,
        'CDI',
        3000.00,
        3500.00,
        7  -- All permissions (1+2+4)
    );
    PRINT 'Employee user created successfully';
END
ELSE
BEGIN
    PRINT 'Employee user already exists';
END

-- Create Manager Employee (limited permissions)
IF NOT EXISTS (SELECT 1 FROM [User] WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO [User] (name, lastName, email, password, phone, role, photo, token, cin, typecontrat, salaire, salairebrut, permission)
    VALUES (
        'Jane', 
        'Smith', 
        '<EMAIL>', 
        '$2a$11$rQZJKAx6vs7hGcgdEXEOUeJ5kPyKxM5oKxvfJKxvfJKxvfJKxvfJK', -- Manager123!
        '1122334455', 
        'Employee', 
        NULL, 
        NULL,
        87654321,
        'CDI',
        4000.00,
        4500.00,
        3  -- Sale + Deposit permissions (1+2)
    );
    PRINT 'Manager user created successfully';
END
ELSE
BEGIN
    PRINT 'Manager user already exists';
END

-- Display created users
SELECT 
    id,
    name,
    lastName,
    email,
    role,
    phone,
    CASE 
        WHEN role = 'Employee' THEN 
            CASE permission
                WHEN 1 THEN 'Sale Only'
                WHEN 2 THEN 'Deposit Only'
                WHEN 3 THEN 'Sale + Deposit'
                WHEN 4 THEN 'Provider Only'
                WHEN 5 THEN 'Sale + Provider'
                WHEN 6 THEN 'Deposit + Provider'
                WHEN 7 THEN 'All Permissions'
                ELSE 'No Permissions'
            END
        ELSE 'Admin (All Access)'
    END as Permissions
FROM [User]
ORDER BY role, name;

PRINT '';
PRINT '=== DEFAULT USERS CREATED ===';
PRINT 'Admin User:';
PRINT '  Email: <EMAIL>';
PRINT '  Password: Admin123!';
PRINT '';
PRINT 'Employee User (All Permissions):';
PRINT '  Email: <EMAIL>';
PRINT '  Password: Employee123!';
PRINT '';
PRINT 'Manager User (Sale + Deposit):';
PRINT '  Email: <EMAIL>';
PRINT '  Password: Manager123!';
PRINT '';
PRINT 'Use these credentials to login via Swagger or Frontend';

-- Note: In production, you should:
-- 1. Use proper BCrypt hashed passwords
-- 2. Force password change on first login
-- 3. Use stronger passwords
-- 4. Remove default users after creating real ones
