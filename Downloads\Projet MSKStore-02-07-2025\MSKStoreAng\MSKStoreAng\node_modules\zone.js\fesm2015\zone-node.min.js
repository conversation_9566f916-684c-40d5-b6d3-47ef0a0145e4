"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */const global=globalThis;function __symbol__(e){return(global.__Zone_symbol_prefix||"__zone_symbol__")+e}function initZone(){const e=global.performance;function t(t){e&&e.mark&&e.mark(t)}function n(t,n){e&&e.measure&&e.measure(t,n)}t("Zone");class o{static __symbol__=__symbol__;static assertZonePatched(){if(global.Promise!==Z.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=o.current;for(;e.parent;)e=e.parent;return e}static get current(){return D.zone}static get currentTask(){return z}static __load_patch(e,r,s=!1){if(Z.hasOwnProperty(e)){const t=!0===global[__symbol__("forceDuplicateZoneCheck")];if(!s&&t)throw Error("Already loaded patch: "+e)}else if(!global["__Zone_disable_"+e]){const s="Zone:"+e;t(s),Z[e]=r(global,o,P),n(s,s)}}get parent(){return this._parent}get name(){return this._name}_parent;_name;_properties;_zoneDelegate;constructor(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new s(this,this._parent&&this._parent._zoneDelegate,t)}get(e){const t=this.getZoneWith(e);if(t)return t._properties[e]}getZoneWith(e){let t=this;for(;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const n=this._zoneDelegate.intercept(this,e,t),o=this;return function(){return o.runGuarded(n,this,arguments,t)}}run(e,t,n,o){D={parent:D,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,o)}finally{D=D.parent}}runGuarded(e,t=null,n,o){D={parent:D,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,o)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{D=D.parent}}runTask(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");const o=e,{type:r,data:{isPeriodic:s=!1,isRefreshable:a=!1}={}}=e;if(e.state===g&&(r===w||r===S))return;const i=e.state!=T;i&&o._transitionTo(T,y);const l=z;z=o,D={parent:D,zone:this};try{r!=S||!e.data||s||a||(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,o,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{const t=e.state;if(t!==g&&t!==v)if(r==w||s||a&&t===m)i&&o._transitionTo(y,T,m);else{const e=o._zoneDelegates;this._updateTaskCount(o,-1),i&&o._transitionTo(g,T,g),a&&(o._zoneDelegates=e)}D=D.parent,z=l}}scheduleTask(e){if(e.zone&&e.zone!==this){let t=this;for(;t;){if(t===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);t=t.parent}}e._transitionTo(m,g);const t=[];e._zoneDelegates=t,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(v,m,g),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===t&&this._updateTaskCount(e,1),e.state==m&&e._transitionTo(y,m),e}scheduleMicroTask(e,t,n,o){return this.scheduleTask(new a(E,e,t,n,o,void 0))}scheduleMacroTask(e,t,n,o,r){return this.scheduleTask(new a(S,e,t,n,o,r))}scheduleEventTask(e,t,n,o,r){return this.scheduleTask(new a(w,e,t,n,o,r))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");if(e.state===y||e.state===T){e._transitionTo(b,y,T);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(v,b),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(g,b),e.runCount=-1,e}}_updateTaskCount(e,t){const n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(let o=0;o<n.length;o++)n[o]._updateTaskCount(e.type,t)}}const r={name:"",onHasTask:(e,t,n,o)=>e.hasTask(n,o),onScheduleTask:(e,t,n,o)=>e.scheduleTask(n,o),onInvokeTask:(e,t,n,o,r,s)=>e.invokeTask(n,o,r,s),onCancelTask:(e,t,n,o)=>e.cancelTask(n,o)};class s{get zone(){return this._zone}_zone;_taskCounts={microTask:0,macroTask:0,eventTask:0};_parentDelegate;_forkDlgt;_forkZS;_forkCurrZone;_interceptDlgt;_interceptZS;_interceptCurrZone;_invokeDlgt;_invokeZS;_invokeCurrZone;_handleErrorDlgt;_handleErrorZS;_handleErrorCurrZone;_scheduleTaskDlgt;_scheduleTaskZS;_scheduleTaskCurrZone;_invokeTaskDlgt;_invokeTaskZS;_invokeTaskCurrZone;_cancelTaskDlgt;_cancelTaskZS;_cancelTaskCurrZone;_hasTaskDlgt;_hasTaskDlgtOwner;_hasTaskZS;_hasTaskCurrZone;constructor(e,t,n){this._zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this._zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this._zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this._zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this._zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this._zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this._zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this._zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const o=n&&n.onHasTask;(o||t&&t._hasTaskZS)&&(this._hasTaskZS=o?n:r,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,n.onScheduleTask||(this._scheduleTaskZS=r,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this._zone),n.onInvokeTask||(this._invokeTaskZS=r,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this._zone),n.onCancelTask||(this._cancelTaskZS=r,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this._zone))}fork(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new o(e,t)}intercept(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t}invoke(e,t,n,o,r){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,o,r):t.apply(n,o)}handleError(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)}scheduleTask(e,t){let n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t),n||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=E)throw new Error("Task is missing scheduleFn.");_(t)}return n}invokeTask(e,t,n,o){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,o):t.callback.apply(n,o)}cancelTask(e,t){let n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n}hasTask(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}}_updateTaskCount(e,t){const n=this._taskCounts,o=n[e],r=n[e]=o+t;if(r<0)throw new Error("More tasks executed then were scheduled.");0!=o&&0!=r||this.hasTask(this._zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})}}class a{type;source;invoke;callback;data;scheduleFn;cancelFn;_zone=null;runCount=0;_zoneDelegates=null;_state="notScheduled";constructor(e,t,n,o,r,s){if(this.type=e,this.source=t,this.data=o,this.scheduleFn=r,this.cancelFn=s,!n)throw new Error("callback is not defined");this.callback=n;const i=this;this.invoke=e===w&&o&&o.useG?a.invokeTask:function(){return a.invokeTask.call(global,i,this,arguments)}}static invokeTask(e,t,n){e||(e=this),O++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==O&&d(),O--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(g,m)}_transitionTo(e,t,n){if(this._state!==t&&this._state!==n)throw new Error(`${this.type} '${this.source}': can not transition to '${e}', expecting state '${t}'${n?" or '"+n+"'":""}, was '${this._state}'.`);this._state=e,e==g&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const i=__symbol__("setTimeout"),l=__symbol__("Promise"),c=__symbol__("then");let h,u=[],p=!1;function f(e){if(h||global[l]&&(h=global[l].resolve(0)),h){let t=h[c];t||(t=h.then),t.call(h,e)}else global[i](e,0)}function _(e){0===O&&0===u.length&&f(d),e&&u.push(e)}function d(){if(!p){for(p=!0;u.length;){const e=u;u=[];for(let t=0;t<e.length;t++){const n=e[t];try{n.zone.runTask(n,null,null)}catch(e){P.onUnhandledError(e)}}}P.microtaskDrainDone(),p=!1}}const k={name:"NO ZONE"},g="notScheduled",m="scheduling",y="scheduled",T="running",b="canceling",v="unknown",E="microTask",S="macroTask",w="eventTask",Z={},P={symbol:__symbol__,currentZoneFrame:()=>D,onUnhandledError:C,microtaskDrainDone:C,scheduleMicroTask:_,showUncaughtError:()=>!o[__symbol__("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:C,patchMethod:()=>C,bindArguments:()=>[],patchThen:()=>C,patchMacroTask:()=>C,patchEventPrototype:()=>C,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>C,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>C,wrapWithCurrentZone:()=>C,filterProperties:()=>[],attachOriginToPatched:()=>C,_redefineProperty:()=>C,patchCallbacks:()=>C,nativeScheduleMicroTask:f};let D={parent:null,zone:new o(null,null)},z=null,O=0;function C(){}return n("Zone","Zone"),o}const ObjectGetOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,ObjectDefineProperty=Object.defineProperty,ObjectGetPrototypeOf=Object.getPrototypeOf,ArraySlice=Array.prototype.slice,ADD_EVENT_LISTENER_STR="addEventListener",REMOVE_EVENT_LISTENER_STR="removeEventListener",TRUE_STR="true",FALSE_STR="false",ZONE_SYMBOL_PREFIX=__symbol__("");function wrapWithCurrentZone(e,t){return Zone.current.wrap(e,t)}function scheduleMacroTaskWithCurrentZone(e,t,n,o,r){return Zone.current.scheduleMacroTask(e,t,n,o,r)}const zoneSymbol=__symbol__,isWindowExists="undefined"!=typeof window,internalWindow=isWindowExists?window:void 0,_global=isWindowExists&&internalWindow||globalThis,REMOVE_ATTRIBUTE="removeAttribute";function bindArguments(e,t){for(let n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=wrapWithCurrentZone(e[n],t+"_"+n));return e}function isPropertyWritable(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}const isWebWorker="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,isNode=!("nw"in _global)&&void 0!==_global.process&&"[object process]"===_global.process.toString(),isBrowser=!isNode&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),isMix=void 0!==_global.process&&"[object process]"===_global.process.toString()&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),zoneSymbolEventNames$1={},enableBeforeunloadSymbol=zoneSymbol("enable_beforeunload"),wrapFn=function(e){if(!(e=e||_global.event))return;let t=zoneSymbolEventNames$1[e.type];t||(t=zoneSymbolEventNames$1[e.type]=zoneSymbol("ON_PROPERTY"+e.type));const n=this||e.target||_global,o=n[t];let r;return isBrowser&&n===internalWindow&&"error"===e.type?(r=o&&o.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===r&&e.preventDefault()):(r=o&&o.apply(this,arguments),"beforeunload"===e.type&&_global[enableBeforeunloadSymbol]&&"string"==typeof r?e.returnValue=r:null==r||r||e.preventDefault()),r};function patchProperty(e,t,n){let o=ObjectGetOwnPropertyDescriptor(e,t);if(!o&&n&&ObjectGetOwnPropertyDescriptor(n,t)&&(o={enumerable:!0,configurable:!0}),!o||!o.configurable)return;const r=zoneSymbol("on"+t+"patched");if(e.hasOwnProperty(r)&&e[r])return;delete o.writable,delete o.value;const s=o.get,a=o.set,i=t.slice(2);let l=zoneSymbolEventNames$1[i];l||(l=zoneSymbolEventNames$1[i]=zoneSymbol("ON_PROPERTY"+i)),o.set=function(t){let n=this;n||e!==_global||(n=_global),n&&("function"==typeof n[l]&&n.removeEventListener(i,wrapFn),a?.call(n,null),n[l]=t,"function"==typeof t&&n.addEventListener(i,wrapFn,!1))},o.get=function(){let n=this;if(n||e!==_global||(n=_global),!n)return null;const r=n[l];if(r)return r;if(s){let e=s.call(this);if(e)return o.set.call(this,e),"function"==typeof n[REMOVE_ATTRIBUTE]&&n.removeAttribute(t),e}return null},ObjectDefineProperty(e,t,o),e[r]=!0}function patchOnProperties(e,t,n){if(t)for(let o=0;o<t.length;o++)patchProperty(e,"on"+t[o],n);else{const t=[];for(const n in e)"on"==n.slice(0,2)&&t.push(n);for(let o=0;o<t.length;o++)patchProperty(e,t[o],n)}}function copySymbolProperties(e,t){"function"==typeof Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((n=>{const o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,{get:function(){return e[n]},set:function(t){(!o||o.writable&&"function"==typeof o.set)&&(e[n]=t)},enumerable:!o||o.enumerable,configurable:!o||o.configurable})}))}let shouldCopySymbolProperties=!1;function setShouldCopySymbolProperties(e){shouldCopySymbolProperties=e}function patchMethod(e,t,n){let o=e;for(;o&&!o.hasOwnProperty(t);)o=ObjectGetPrototypeOf(o);!o&&e[t]&&(o=e);const r=zoneSymbol(t);let s=null;if(o&&(!(s=o[r])||!o.hasOwnProperty(r))&&(s=o[r]=o[t],isPropertyWritable(o&&ObjectGetOwnPropertyDescriptor(o,t)))){const e=n(s,r,t);o[t]=function(){return e(this,arguments)},attachOriginToPatched(o[t],s),shouldCopySymbolProperties&&copySymbolProperties(s,o[t])}return s}function patchMacroTask(e,t,n){let o=null;function r(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},o.apply(t.target,t.args),e}o=patchMethod(e,t,(e=>function(t,o){const s=n(t,o);return s.cbIdx>=0&&"function"==typeof o[s.cbIdx]?scheduleMacroTaskWithCurrentZone(s.name,o[s.cbIdx],s,r):e.apply(t,o)}))}function patchMicroTask(e,t,n){let o=null;function r(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},o.apply(t.target,t.args),e}o=patchMethod(e,t,(e=>function(t,o){const s=n(t,o);return s.cbIdx>=0&&"function"==typeof o[s.cbIdx]?Zone.current.scheduleMicroTask(s.name,o[s.cbIdx],s,r):e.apply(t,o)}))}function attachOriginToPatched(e,t){e[zoneSymbol("OriginalDelegate")]=t}function isFunction(e){return"function"==typeof e}function isNumber(e){return"number"==typeof e}function patchPromise(e){e.__load_patch("ZoneAwarePromise",((e,t,n)=>{const o=Object.getOwnPropertyDescriptor,r=Object.defineProperty,s=n.symbol,a=[],i=!1!==e[s("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],l=s("Promise"),c=s("then");n.onUnhandledError=e=>{if(n.showUncaughtError()){const t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=()=>{for(;a.length;){const e=a.shift();try{e.zone.runGuarded((()=>{if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){u(e)}}};const h=s("unhandledPromiseRejectionHandler");function u(e){n.onUnhandledError(e);try{const n=t[h];"function"==typeof n&&n.call(this,e)}catch(e){}}function p(e){return e&&"function"==typeof e.then}function f(e){return e}function _(e){return j.reject(e)}const d=s("state"),k=s("value"),g=s("finally"),m=s("parentPromiseValue"),y=s("parentPromiseState"),T=null,b=!0,v=!1;function E(e,t){return n=>{try{P(e,t,n)}catch(t){P(e,!1,t)}}}const S=function(){let e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},w="Promise resolved with itself",Z=s("currentTaskTrace");function P(e,o,s){const l=S();if(e===s)throw new TypeError(w);if(e[d]===T){let c=null;try{"object"!=typeof s&&"function"!=typeof s||(c=s&&s.then)}catch(t){return l((()=>{P(e,!1,t)}))(),e}if(o!==v&&s instanceof j&&s.hasOwnProperty(d)&&s.hasOwnProperty(k)&&s[d]!==T)z(s),P(e,s[d],s[k]);else if(o!==v&&"function"==typeof c)try{c.call(s,l(E(e,o)),l(E(e,!1)))}catch(t){l((()=>{P(e,!1,t)}))()}else{e[d]=o;const l=e[k];if(e[k]=s,e[g]===g&&o===b&&(e[d]=e[y],e[k]=e[m]),o===v&&s instanceof Error){const e=t.currentTask&&t.currentTask.data&&t.currentTask.data.__creationTrace__;e&&r(s,Z,{configurable:!0,enumerable:!1,writable:!0,value:e})}for(let t=0;t<l.length;)O(e,l[t++],l[t++],l[t++],l[t++]);if(0==l.length&&o==v){e[d]=0;let o=s;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(e){o=e}i&&(o.throwOriginal=!0),o.rejection=s,o.promise=e,o.zone=t.current,o.task=t.currentTask,a.push(o),n.scheduleMicroTask()}}}return e}const D=s("rejectionHandledHandler");function z(e){if(0===e[d]){try{const n=t[D];n&&"function"==typeof n&&n.call(this,{rejection:e[k],promise:e})}catch(e){}e[d]=v;for(let t=0;t<a.length;t++)e===a[t].promise&&a.splice(t,1)}}function O(e,t,n,o,r){z(e);const s=e[d],a=s?"function"==typeof o?o:f:"function"==typeof r?r:_;t.scheduleMicroTask("Promise.then",(()=>{try{const o=e[k],r=!!n&&g===n[g];r&&(n[m]=o,n[y]=s);const i=t.run(a,void 0,r&&a!==_&&a!==f?[]:[o]);P(n,!0,i)}catch(e){P(n,!1,e)}}),n)}const C=function(){},N=e.AggregateError;class j{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(e){return e instanceof j?e:P(new this(null),b,e)}static reject(e){return P(new this(null),v,e)}static withResolvers(){const e={};return e.promise=new j(((t,n)=>{e.resolve=t,e.reject=n})),e}static any(e){if(!e||"function"!=typeof e[Symbol.iterator])return Promise.reject(new N([],"All promises were rejected"));const t=[];let n=0;try{for(let o of e)n++,t.push(j.resolve(o))}catch(e){return Promise.reject(new N([],"All promises were rejected"))}if(0===n)return Promise.reject(new N([],"All promises were rejected"));let o=!1;const r=[];return new j(((e,s)=>{for(let a=0;a<t.length;a++)t[a].then((t=>{o||(o=!0,e(t))}),(e=>{r.push(e),n--,0===n&&(o=!0,s(new N(r,"All promises were rejected")))}))}))}static race(e){let t,n,o=new this(((e,o)=>{t=e,n=o}));function r(e){t(e)}function s(e){n(e)}for(let t of e)p(t)||(t=this.resolve(t)),t.then(r,s);return o}static all(e){return j.allWithCallback(e)}static allSettled(e){return(this&&this.prototype instanceof j?this:j).allWithCallback(e,{thenCallback:e=>({status:"fulfilled",value:e}),errorCallback:e=>({status:"rejected",reason:e})})}static allWithCallback(e,t){let n,o,r=new this(((e,t)=>{n=e,o=t})),s=2,a=0;const i=[];for(let r of e){p(r)||(r=this.resolve(r));const e=a;try{r.then((o=>{i[e]=t?t.thenCallback(o):o,s--,0===s&&n(i)}),(r=>{t?(i[e]=t.errorCallback(r),s--,0===s&&n(i)):o(r)}))}catch(e){o(e)}s++,a++}return s-=2,0===s&&n(i),r}constructor(e){const t=this;if(!(t instanceof j))throw new Error("Must be an instanceof Promise.");t[d]=T,t[k]=[];try{const n=S();e&&e(n(E(t,b)),n(E(t,v)))}catch(e){P(t,!1,e)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return j}then(e,n){let o=this.constructor?.[Symbol.species];o&&"function"==typeof o||(o=this.constructor||j);const r=new o(C),s=t.current;return this[d]==T?this[k].push(s,r,e,n):O(this,s,r,e,n),r}catch(e){return this.then(null,e)}finally(e){let n=this.constructor?.[Symbol.species];n&&"function"==typeof n||(n=j);const o=new n(C);o[g]=g;const r=t.current;return this[d]==T?this[k].push(r,o,e,e):O(this,r,o,e,e),o}}j.resolve=j.resolve,j.reject=j.reject,j.race=j.race,j.all=j.all;const M=e[l]=e.Promise;e.Promise=j;const I=s("thenPatched");function A(e){const t=e.prototype,n=o(t,"then");if(n&&(!1===n.writable||!n.configurable))return;const r=t.then;t[c]=r,e.prototype.then=function(e,t){return new j(((e,t)=>{r.call(this,e,t)})).then(e,t)},e[I]=!0}return n.patchThen=A,M&&(A(M),patchMethod(e,"fetch",(e=>function t(e){return function(t,n){let o=e.apply(t,n);if(o instanceof j)return o;let r=o.constructor;return r[I]||A(r),o}}(e)))),Promise[t.__symbol__("uncaughtPromiseErrors")]=a,j}))}function patchToString(e){e.__load_patch("toString",(e=>{const t=Function.prototype.toString,n=zoneSymbol("OriginalDelegate"),o=zoneSymbol("Promise"),r=zoneSymbol("Error"),s=function s(){if("function"==typeof this){const s=this[n];if(s)return"function"==typeof s?t.call(s):Object.prototype.toString.call(s);if(this===Promise){const n=e[o];if(n)return t.call(n)}if(this===Error){const n=e[r];if(n)return t.call(n)}}return t.call(this)};s[n]=t,Function.prototype.toString=s;const a=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":a.call(this)}}))}function loadZone(){const e=globalThis,t=!0===e[__symbol__("forceDuplicateZoneCheck")];if(e.Zone&&(t||"function"!=typeof e.Zone.__symbol__))throw new Error("Zone already loaded.");return e.Zone??=initZone(),e.Zone}const OPTIMIZED_ZONE_EVENT_TASK_DATA={useG:!0},zoneSymbolEventNames={},globalSources={},EVENT_NAME_SYMBOL_REGX=new RegExp("^"+ZONE_SYMBOL_PREFIX+"(\\w+)(true|false)$"),IMMEDIATE_PROPAGATION_SYMBOL=zoneSymbol("propagationStopped");function prepareEventNames(e,t){const n=(t?t(e):e)+"false",o=(t?t(e):e)+"true",r=ZONE_SYMBOL_PREFIX+n,s=ZONE_SYMBOL_PREFIX+o;zoneSymbolEventNames[e]={},zoneSymbolEventNames[e].false=r,zoneSymbolEventNames[e].true=s}function patchEventTarget(e,t,n,o){const r=o&&o.add||"addEventListener",s=o&&o.rm||"removeEventListener",a=o&&o.listeners||"eventListeners",i=o&&o.rmAll||"removeAllListeners",l=zoneSymbol(r),c="."+r+":",h="prependListener",u="."+h+":",p=function(e,t,n){if(e.isRemoved)return;const o=e.callback;let r;"object"==typeof o&&o.handleEvent&&(e.callback=e=>o.handleEvent(e),e.originalDelegate=o);try{e.invoke(e,t,[n])}catch(e){r=e}const a=e.options;return a&&"object"==typeof a&&a.once&&t[s].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,a),r};function f(n,o,r){if(!(o=o||e.event))return;const s=n||o.target||e,a=s[zoneSymbolEventNames[o.type][r?"true":"false"]];if(a){const e=[];if(1===a.length){const t=p(a[0],s,o);t&&e.push(t)}else{const t=a.slice();for(let n=0;n<t.length&&(!o||!0!==o[IMMEDIATE_PROPAGATION_SYMBOL]);n++){const r=p(t[n],s,o);r&&e.push(r)}}if(1===e.length)throw e[0];for(let n=0;n<e.length;n++){const o=e[n];t.nativeScheduleMicroTask((()=>{throw o}))}}}const _=function(e){return f(this,e,!1)},d=function(e){return f(this,e,!0)};function k(t,n){if(!t)return!1;let o=!0;n&&void 0!==n.useG&&(o=n.useG);const p=n&&n.vh;let f=!0;n&&void 0!==n.chkDup&&(f=n.chkDup);let k=!1;n&&void 0!==n.rt&&(k=n.rt);let g=t;for(;g&&!g.hasOwnProperty(r);)g=ObjectGetPrototypeOf(g);if(!g&&t[r]&&(g=t),!g)return!1;if(g[l])return!1;const m=n&&n.eventNameToString,y={},T=g[l]=g[r],b=g[zoneSymbol(s)]=g[s],v=g[zoneSymbol(a)]=g[a],E=g[zoneSymbol(i)]=g[i];let S;n&&n.prepend&&(S=g[zoneSymbol(n.prepend)]=g[n.prepend]);const w=o?function(e){if(!y.isExisting)return T.call(y.target,y.eventName,y.capture?d:_,y.options)}:function(e){return T.call(y.target,y.eventName,e.invoke,y.options)},Z=o?function(e){if(!e.isRemoved){const t=zoneSymbolEventNames[e.eventName];let n;t&&(n=t[e.capture?"true":"false"]);const o=n&&e.target[n];if(o)for(let t=0;t<o.length;t++)if(o[t]===e){o.splice(t,1),e.isRemoved=!0,e.removeAbortListener&&(e.removeAbortListener(),e.removeAbortListener=null),0===o.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return b.call(e.target,e.eventName,e.capture?d:_,e.options)}:function(e){return b.call(e.target,e.eventName,e.invoke,e.options)},P=n?.diff||function(e,t){const n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},D=Zone[zoneSymbol("UNPATCHED_EVENTS")],z=e[zoneSymbol("PASSIVE_EVENTS")],O=function(t,r,s,a,i=!1,l=!1){return function(){const c=this||e;let h=arguments[0];n&&n.transferEventName&&(h=n.transferEventName(h));let u=arguments[1];if(!u)return t.apply(this,arguments);if(isNode&&"uncaughtException"===h)return t.apply(this,arguments);let _=!1;if("function"!=typeof u){if(!u.handleEvent)return t.apply(this,arguments);_=!0}if(p&&!p(t,u,c,arguments))return;const d=!!z&&-1!==z.indexOf(h),k=function T(e){if("object"==typeof e&&null!==e){const t={...e};return e.signal&&(t.signal=e.signal),t}return e}(function g(e,t){return t?"boolean"==typeof e?{capture:e,passive:!0}:e?"object"==typeof e&&!1!==e.passive?{...e,passive:!0}:e:{passive:!0}:e}(arguments[2],d)),b=k?.signal;if(b?.aborted)return;if(D)for(let e=0;e<D.length;e++)if(h===D[e])return d?t.call(c,h,u,k):t.apply(this,arguments);const v=!!k&&("boolean"==typeof k||k.capture),E=!(!k||"object"!=typeof k)&&k.once,S=Zone.current;let w=zoneSymbolEventNames[h];w||(prepareEventNames(h,m),w=zoneSymbolEventNames[h]);const Z=w[v?"true":"false"];let O,C=c[Z],N=!1;if(C){if(N=!0,f)for(let e=0;e<C.length;e++)if(P(C[e],u))return}else C=c[Z]=[];const j=c.constructor.name,M=globalSources[j];M&&(O=M[h]),O||(O=j+r+(m?m(h):h)),y.options=k,E&&(y.options.once=!1),y.target=c,y.capture=v,y.eventName=h,y.isExisting=N;const I=o?OPTIMIZED_ZONE_EVENT_TASK_DATA:void 0;I&&(I.taskData=y),b&&(y.options.signal=void 0);const A=S.scheduleEventTask(O,u,I,s,a);if(b){y.options.signal=b;const e=()=>A.zone.cancelTask(A);t.call(b,"abort",e,{once:!0}),A.removeAbortListener=()=>b.removeEventListener("abort",e)}return y.target=null,I&&(I.taskData=null),E&&(y.options.once=!0),"boolean"!=typeof A.options&&(A.options=k),A.target=c,A.capture=v,A.eventName=h,_&&(A.originalDelegate=u),l?C.unshift(A):C.push(A),i?c:void 0}};return g[r]=O(T,c,w,Z,k),S&&(g[h]=O(S,u,(function(e){return S.call(y.target,y.eventName,e.invoke,y.options)}),Z,k,!0)),g[s]=function(){const t=this||e;let o=arguments[0];n&&n.transferEventName&&(o=n.transferEventName(o));const r=arguments[2],s=!!r&&("boolean"==typeof r||r.capture),a=arguments[1];if(!a)return b.apply(this,arguments);if(p&&!p(b,a,t,arguments))return;const i=zoneSymbolEventNames[o];let l;i&&(l=i[s?"true":"false"]);const c=l&&t[l];if(c)for(let e=0;e<c.length;e++){const n=c[e];if(P(n,a))return c.splice(e,1),n.isRemoved=!0,0!==c.length||(n.allRemoved=!0,t[l]=null,s||"string"!=typeof o)||(t[ZONE_SYMBOL_PREFIX+"ON_PROPERTY"+o]=null),n.zone.cancelTask(n),k?t:void 0}return b.apply(this,arguments)},g[a]=function(){const t=this||e;let o=arguments[0];n&&n.transferEventName&&(o=n.transferEventName(o));const r=[],s=findEventTasks(t,m?m(o):o);for(let e=0;e<s.length;e++){const t=s[e];r.push(t.originalDelegate?t.originalDelegate:t.callback)}return r},g[i]=function(){const t=this||e;let o=arguments[0];if(o){n&&n.transferEventName&&(o=n.transferEventName(o));const e=zoneSymbolEventNames[o];if(e){const n=t[e.false],r=t[e.true];if(n){const e=n.slice();for(let t=0;t<e.length;t++){const n=e[t];this[s].call(this,o,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}if(r){const e=r.slice();for(let t=0;t<e.length;t++){const n=e[t];this[s].call(this,o,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}}}else{const e=Object.keys(t);for(let t=0;t<e.length;t++){const n=EVENT_NAME_SYMBOL_REGX.exec(e[t]);let o=n&&n[1];o&&"removeListener"!==o&&this[i].call(this,o)}this[i].call(this,"removeListener")}if(k)return this},attachOriginToPatched(g[r],T),attachOriginToPatched(g[s],b),E&&attachOriginToPatched(g[i],E),v&&attachOriginToPatched(g[a],v),!0}let g=[];for(let e=0;e<n.length;e++)g[e]=k(n[e],o);return g}function findEventTasks(e,t){if(!t){const n=[];for(let o in e){const r=EVENT_NAME_SYMBOL_REGX.exec(o);let s=r&&r[1];if(s&&(!t||s===t)){const t=e[o];if(t)for(let e=0;e<t.length;e++)n.push(t[e])}}return n}let n=zoneSymbolEventNames[t];n||(prepareEventNames(t),n=zoneSymbolEventNames[t]);const o=e[n.false],r=e[n.true];return o?r?o.concat(r):o.slice():r?r.slice():[]}function patchQueueMicrotask(e,t){t.patchMethod(e,"queueMicrotask",(e=>function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}))}const taskSymbol=zoneSymbol("zoneTask");function patchTimer(e,t,n,o){let r=null,s=null;n+=o;const a={};function i(t){const n=t.data;n.args[0]=function(){return t.invoke.apply(this,arguments)};const o=r.apply(e,n.args);return isNumber(o)?n.handleId=o:(n.handle=o,n.isRefreshable=isFunction(o.refresh)),t}function l(t){const{handle:n,handleId:o}=t.data;return s.call(e,n??o)}r=patchMethod(e,t+=o,(n=>function(r,s){if(isFunction(s[0])){const e={isRefreshable:!1,isPeriodic:"Interval"===o,delay:"Timeout"===o||"Interval"===o?s[1]||0:void 0,args:s},n=s[0];s[0]=function t(){try{return n.apply(this,arguments)}finally{const{handle:t,handleId:n,isPeriodic:o,isRefreshable:r}=e;o||r||(n?delete a[n]:t&&(t[taskSymbol]=null))}};const r=scheduleMacroTaskWithCurrentZone(t,s[0],e,i,l);if(!r)return r;const{handleId:c,handle:h,isRefreshable:u,isPeriodic:p}=r.data;if(c)a[c]=r;else if(h&&(h[taskSymbol]=r,u&&!p)){const e=h.refresh;h.refresh=function(){const{zone:t,state:n}=r;return"notScheduled"===n?(r._state="scheduled",t._updateTaskCount(r,1)):"running"===n&&(r._state="scheduling"),e.call(this)}}return h??c??r}return n.apply(e,s)})),s=patchMethod(e,n,(t=>function(n,o){const r=o[0];let s;isNumber(r)?(s=a[r],delete a[r]):(s=r?.[taskSymbol],s?r[taskSymbol]=null:s=r),s?.type?s.cancelFn&&s.zone.cancelTask(s):t.apply(e,o)}))}function patchEvents(e){e.__load_patch("EventEmitter",((e,t,n)=>{const o="addListener",r="removeListener",s=function(e,t){return e.callback===t||e.callback.listener===t},a=function(e){return"string"==typeof e?e:e?e.toString().replace("(","_").replace(")","_"):""};let i;try{i=require("events")}catch(e){}i&&i.EventEmitter&&function l(t){const i=patchEventTarget(e,n,[t],{useG:!1,add:o,rm:r,prepend:"prependListener",rmAll:"removeAllListeners",listeners:"listeners",chkDup:!1,rt:!0,diff:s,eventNameToString:a});i&&i[0]&&(t.on=t[o],t.off=t[r])}(i.EventEmitter.prototype)}))}function patchFs(e){e.__load_patch("fs",((e,t,n)=>{let o;try{o=require("fs")}catch(e){}if(!o)return;["access","appendFile","chmod","chown","close","exists","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","lutimes","link","lstat","mkdir","mkdtemp","open","opendir","read","readdir","readFile","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","write","writeFile","writev"].filter((e=>!!o[e]&&"function"==typeof o[e])).forEach((e=>{patchMacroTask(o,e,((t,n)=>({name:"fs."+e,args:n,cbIdx:n.length>0?n.length-1:-1,target:t})))}));const r=o.realpath?.[n.symbol("OriginalDelegate")];r?.native&&(o.realpath.native=r.native,patchMacroTask(o.realpath,"native",((e,t)=>({args:t,target:e,cbIdx:t.length>0?t.length-1:-1,name:"fs.realpath.native"}))))}))}function patchNodeUtil(e){e.__load_patch("node_util",((e,t,n)=>{n.patchOnProperties=patchOnProperties,n.patchMethod=patchMethod,n.bindArguments=bindArguments,n.patchMacroTask=patchMacroTask,setShouldCopySymbolProperties(!0)}))}const set="set",clear="clear";function patchNode(e){patchNodeUtil(e),patchEvents(e),patchFs(e),e.__load_patch("node_timers",((e,t)=>{let n=!1;try{const t=require("timers");if(e.setTimeout!==t.setTimeout&&!isMix){const o=t.setTimeout;t.setTimeout=function(){return n=!0,o.apply(this,arguments)};const r=e.setTimeout((()=>{}),100);clearTimeout(r),t.setTimeout=o}patchTimer(t,set,clear,"Timeout"),patchTimer(t,set,clear,"Interval"),patchTimer(t,set,clear,"Immediate")}catch(e){}isMix||(n?(e[t.__symbol__("setTimeout")]=e.setTimeout,e[t.__symbol__("setInterval")]=e.setInterval,e[t.__symbol__("setImmediate")]=e.setImmediate):(patchTimer(e,set,clear,"Timeout"),patchTimer(e,set,clear,"Interval"),patchTimer(e,set,clear,"Immediate")))})),e.__load_patch("nextTick",(()=>{patchMicroTask(process,"nextTick",((e,t)=>({name:"process.nextTick",args:t,cbIdx:t.length>0&&"function"==typeof t[0]?0:-1,target:process})))})),e.__load_patch("handleUnhandledPromiseRejection",((e,t,n)=>{function o(e){return function(t){findEventTasks(process,e).forEach((n=>{"unhandledRejection"===e?n.invoke(t.rejection,t.promise):"rejectionHandled"===e&&n.invoke(t.promise)}))}}t[n.symbol("unhandledPromiseRejectionHandler")]=o("unhandledRejection"),t[n.symbol("rejectionHandledHandler")]=o("rejectionHandled")})),e.__load_patch("crypto",(()=>{let e;try{e=require("crypto")}catch(e){}e&&["randomBytes","pbkdf2"].forEach((t=>{patchMacroTask(e,t,((n,o)=>({name:"crypto."+t,args:o,cbIdx:o.length>0&&"function"==typeof o[o.length-1]?o.length-1:-1,target:e})))}))})),e.__load_patch("console",((e,t)=>{["dir","log","info","error","warn","assert","debug","timeEnd","trace"].forEach((e=>{const n=console[t.__symbol__(e)]=console[e];n&&(console[e]=function(){const e=ArraySlice.call(arguments);return t.current===t.root?n.apply(this,e):t.root.run(n,this,e)})}))})),e.__load_patch("queueMicrotask",((e,t,n)=>{patchQueueMicrotask(e,n)}))}function rollupMain(){const e=loadZone();return patchNode(e),patchPromise(e),patchToString(e),e}rollupMain();