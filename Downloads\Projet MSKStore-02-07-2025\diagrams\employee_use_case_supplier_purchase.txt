@startuml
actor employé
rectangle "Système" {
  usecase "Gérer les fournisseurs" as UC_GererFournisseurs
  usecase "Ajouter un fournisseur" as UC_AjouterFournisseur
  usecase "Consulter les fournisseurs" as UC_ConsulterFournisseurs
  usecase "Supprimer un fournisseur" as UC_SupprimerFournisseur
  usecase "Modifier un fournisseur" as UC_ModifierFournisseur
  usecase "Gérer les achats fournisseur" as UC_GererAchatsFournisseur
  usecase "Ajouter une commande" as UC_AjouterCommande
  usecase "Consulter les commandes" as UC_ConsulterCommandes
  usecase "Modifier une commande" as UC_ModifierCommande
  usecase "Supprimer une commande" as UC_SupprimerCommande

  employé -- UC_GererFournisseurs
  employé -- UC_GererAchatsFournisseur

  UC_GererFournisseurs <.. UC_AjouterFournisseur : <<extends>>
  UC_GererFournisseurs <.. UC_ConsulterFournisseurs : <<extends>>
  UC_ConsulterFournisseurs <.. UC_SupprimerFournisseur : <<extends>>
  UC_ConsulterFournisseurs <.. UC_ModifierFournisseur : <<extends>>
  UC_ConsulterFournisseurs <.. UC_AjouterCommande : <<include>>

  UC_GererAchatsFournisseur <.. UC_AjouterCommande : <<extends>>
  UC_GererAchatsFournisseur <.. UC_ConsulterCommandes : <<extends>>
  UC_ConsulterCommandes <.. UC_ModifierCommande : <<extends>>
  UC_ConsulterCommandes <.. UC_SupprimerCommande : <<extends>>
}
@enduml

