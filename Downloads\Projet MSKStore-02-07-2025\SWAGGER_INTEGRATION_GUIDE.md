# 🔗 Swagger API Integration Guide

## 🎯 Swagger is Already Fully Integrated!

Your MSKStore backend comes with **complete Swagger integration** including:

### 🔐 JWT Authentication Support
```csharp
// Already configured in Program.cs:
options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
{
    Type = SecuritySchemeType.Http,
    Scheme = "bearer",
    BearerFormat = "JWT"
});
```

### 📋 Available API Endpoints in Swagger

#### 👥 **User Management** (`/api/Users`)
- `POST /api/Users/<USER>
- `GET /api/Users` - List all users
- `GET /api/Users/<USER>
- `POST /api/Users` - Create new user
- `PUT /api/Users/<USER>
- `DELETE /api/Users/<USER>

#### 📦 **Product Management** (`/api/Produits`)
- `GET /api/Produits` - List all products
- `GET /api/Produits/paginated` - Paginated products
- `GET /api/Produits/{id}` - Get specific product
- `POST /api/Produits` - Create new product
- `PUT /api/Produits/{id}` - Update product
- `DELETE /api/Produits/{id}` - Delete product

#### 🏪 **Categories** (`/api/Categories`)
- `GET /api/Categories` - List all categories
- `POST /api/Categories` - Create category
- `PUT /api/Categories/{id}` - Update category
- `DELETE /api/Categories/{id}` - Delete category

#### 📋 **Orders & Invoices**
- `/api/Commandeclients` - Client orders
- `/api/Commandefournisseurs` - Supplier orders
- `/api/Factureclients` - Client invoices
- `/api/Depots` - Warehouse management

## 🚀 How to Use Swagger After Backend Starts

### Step 1: Start the Backend
```bash
cd MskStoreAPI\MskStoreAPI
dotnet run
```

### Step 2: Access Swagger UI
Open browser and go to: **`https://localhost:7258/swagger`**

### Step 3: What You'll See
```
🌐 Swagger UI Interface:
┌─────────────────────────────────────────┐
│  MSKStore API Documentation            │
│  ┌─────────────────────────────────────┐│
│  │ 🔐 Authorize [Button]              ││
│  └─────────────────────────────────────┘│
│                                         │
│  📂 Users                              │
│    POST /api/Users/<USER>
│    GET  /api/Users                      │
│    POST /api/Users                      │
│                                         │
│  📂 Produits                           │
│    GET  /api/Produits                   │
│    POST /api/Produits                   │
│    GET  /api/Produits/paginated         │
│                                         │
│  📂 Categories                          │
│    GET  /api/Categories                 │
│    POST /api/Categories                 │
└─────────────────────────────────────────┘
```

### Step 4: Test Authentication
1. **Click** on `POST /api/Users/<USER>
2. **Click** "Try it out"
3. **Enter** test credentials:
   ```json
   {
     "authemail": "<EMAIL>",
     "authpassword": "password123"
   }
   ```
4. **Click** "Execute"
5. **Copy** the JWT token from response

### Step 5: Authorize for Protected Endpoints
1. **Click** the "🔐 Authorize" button at top
2. **Enter**: `Bearer YOUR_JWT_TOKEN_HERE`
3. **Click** "Authorize"
4. **Now** you can test protected endpoints!

## 🔧 Swagger Features Available

### ✅ **Interactive Testing**
- Test all API endpoints directly from browser
- See real request/response data
- No need for external tools like Postman

### ✅ **JWT Authentication**
- Built-in authorization support
- Automatic token handling
- Protected endpoint testing

### ✅ **Request/Response Examples**
- See expected data formats
- Understand API contracts
- Copy-paste ready examples

### ✅ **Error Handling**
- See all possible response codes
- Understand error formats
- Debug API issues easily

## 🎯 Testing Workflow

### 1. **Public Endpoints** (No Auth Required)
```
✅ GET /api/Produits - List products
✅ GET /api/Categories - List categories
✅ POST /api/Users/<USER>
```

### 2. **Protected Endpoints** (JWT Required)
```
🔐 POST /api/Produits - Create product
🔐 PUT /api/Produits/{id} - Update product
🔐 DELETE /api/Produits/{id} - Delete product
🔐 GET /api/Users - List users
```

## 🔗 Integration with Frontend

### Angular ↔ API Communication
```typescript
// Frontend already configured to use:
apiUrl: 'https://localhost:7258'

// Authentication service automatically:
// 1. Sends login requests to /api/Users/<USER>
// 2. Stores JWT token
// 3. Includes token in all API requests
// 4. Handles token expiration
```

## 🎉 You're All Set!

Once you run `dotnet run`, you'll have:
- ✅ **Full API** running on `https://localhost:7258`
- ✅ **Swagger UI** at `https://localhost:7258/swagger`
- ✅ **JWT Authentication** ready to test
- ✅ **All endpoints** documented and testable
- ✅ **Frontend integration** working automatically

### 🚀 Quick Start Commands:
```bash
# 1. Navigate to backend
cd MskStoreAPI\MskStoreAPI

# 2. Run the API
dotnet run

# 3. Open Swagger in browser
start https://localhost:7258/swagger

# 4. Open Frontend in browser  
start http://localhost:4200
```

**Your complete MSKStore application with Swagger API documentation is ready to go!** 🎊
