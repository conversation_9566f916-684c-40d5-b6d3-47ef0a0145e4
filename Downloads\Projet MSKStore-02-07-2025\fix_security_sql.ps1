# PowerShell script to fix the SecurityAuditLogs table creation SQL
$programFile = "MSKStoreAPI\MskStoreAPI\Program.cs"

# Read the file content
$content = Get-Content $programFile -Raw

# Replace the problematic SQL section
$oldSql = @'
                ""Severity"" TEXT NOT NULL,
                ""Success"" INTEGER NOT NULL,
                ""AdditionalData"" TEXT NULL,
                ""IsSuccessful"" INTEGER NOT NULL DEFAULT 0
'@

$newSql = @'
                ""Severity"" TEXT NOT NULL,
                ""AdditionalData"" TEXT NULL,
                ""IsSuccessful"" INTEGER NOT NULL DEFAULT 1
'@

$content = $content -replace [regex]::Escape($oldSql), $newSql

# Write back to file
Set-Content $programFile -Value $content -Encoding UTF8

Write-Host "Fixed SecurityAuditLogs table creation SQL"
