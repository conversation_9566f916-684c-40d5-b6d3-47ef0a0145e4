<div class="content-wrapper container-xxl p-0">
  <div class="content-body">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-3">
      <div class="row align-items-center">
        <div class="col-md-8">
          <h2 class="dashboard-title mb-0">{{ 'Bienvenue' | translate }}</h2>
          <p class="dashboard-subtitle text-muted mb-0">{{ 'p1' | translate }}</p>
        </div>
        <div class="col-md-4 text-md-end">
          <div class="dashboard-date">
            <i data-feather="calendar" class="me-1"></i>
            {{ currentDate | date:'fullDate' }}
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="row mb-4">
      <div class="col-xl-3 col-md-6 col-12">
        <div class="card stats-card bg-gradient-primary">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h3 class="text-white mb-1">{{ totalSales | currency:'EUR':'symbol':'1.0-0' }}</h3>
                <p class="text-white mb-0">Total Sales Today</p>
              </div>
              <div class="stats-icon">
                <i data-feather="trending-up" class="text-white"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-xl-3 col-md-6 col-12">
        <div class="card stats-card bg-gradient-success">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h3 class="text-white mb-1">{{ totalOrders }}</h3>
                <p class="text-white mb-0">Orders Today</p>
              </div>
              <div class="stats-icon">
                <i data-feather="shopping-cart" class="text-white"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-xl-3 col-md-6 col-12">
        <div class="card stats-card bg-gradient-warning">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h3 class="text-white mb-1">{{ totalProducts }}</h3>
                <p class="text-white mb-0">Products in Stock</p>
              </div>
              <div class="stats-icon">
                <i data-feather="package" class="text-white"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-xl-3 col-md-6 col-12">
        <div class="card stats-card bg-gradient-info">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h3 class="text-white mb-1">{{ totalCustomers }}</h3>
                <p class="text-white mb-0">Active Customers</p>
              </div>
              <div class="stats-icon">
                <i data-feather="users" class="text-white"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Security Monitoring Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card security-overview">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
              <i data-feather="shield" class="me-2"></i>
              Security Overview
            </h4>
            <a routerLink="/security/dashboard" class="btn btn-outline-primary btn-sm">
              View Details
              <i data-feather="arrow-right" class="ms-1"></i>
            </a>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3 col-6">
                <div class="security-stat">
                  <div class="security-stat-icon bg-light-success">
                    <i data-feather="check-circle" class="text-success"></i>
                  </div>
                  <div class="security-stat-content">
                    <h5 class="mb-0">{{ securityStats.status }}</h5>
                    <small class="text-muted">System Status</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-6">
                <div class="security-stat">
                  <div class="security-stat-icon bg-light-warning">
                    <i data-feather="alert-triangle" class="text-warning"></i>
                  </div>
                  <div class="security-stat-content">
                    <h5 class="mb-0">{{ securityStats.alerts }}</h5>
                    <small class="text-muted">Active Alerts</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-6">
                <div class="security-stat">
                  <div class="security-stat-icon bg-light-info">
                    <i data-feather="activity" class="text-info"></i>
                  </div>
                  <div class="security-stat-content">
                    <h5 class="mb-0">{{ securityStats.events }}</h5>
                    <small class="text-muted">Events Today</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-6">
                <div class="security-stat">
                  <div class="security-stat-icon bg-light-danger">
                    <i data-feather="lock" class="text-danger"></i>
                  </div>
                  <div class="security-stat-content">
                    <h5 class="mb-0">{{ securityStats.lockouts }}</h5>
                    <small class="text-muted">Account Lockouts</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions & Recent Activity -->
    <div class="row">
      <div class="col-lg-8 col-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title">Quick Actions</h4>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-4 col-6 mb-3">
                <a routerLink="/caisse" class="quick-action-card">
                  <div class="quick-action-icon bg-light-primary">
                    <i data-feather="dollar-sign" class="text-primary"></i>
                  </div>
                  <h6 class="mt-2 mb-0">Point of Sale</h6>
                  <small class="text-muted">Process sales</small>
                </a>
              </div>
              <div class="col-md-4 col-6 mb-3">
                <a routerLink="/produit/ajouter" class="quick-action-card">
                  <div class="quick-action-icon bg-light-success">
                    <i data-feather="plus" class="text-success"></i>
                  </div>
                  <h6 class="mt-2 mb-0">Add Product</h6>
                  <small class="text-muted">New inventory</small>
                </a>
              </div>
              <div class="col-md-4 col-6 mb-3">
                <a routerLink="/client/ajouter" class="quick-action-card">
                  <div class="quick-action-icon bg-light-info">
                    <i data-feather="user-plus" class="text-info"></i>
                  </div>
                  <h6 class="mt-2 mb-0">Add Customer</h6>
                  <small class="text-muted">New client</small>
                </a>
              </div>
              <div class="col-md-4 col-6 mb-3">
                <a routerLink="/inventaire" class="quick-action-card">
                  <div class="quick-action-icon bg-light-warning">
                    <i data-feather="clipboard" class="text-warning"></i>
                  </div>
                  <h6 class="mt-2 mb-0">Inventory</h6>
                  <small class="text-muted">Stock check</small>
                </a>
              </div>
              <div class="col-md-4 col-6 mb-3">
                <a routerLink="/rapport" class="quick-action-card">
                  <div class="quick-action-icon bg-light-secondary">
                    <i data-feather="bar-chart-2" class="text-secondary"></i>
                  </div>
                  <h6 class="mt-2 mb-0">Reports</h6>
                  <small class="text-muted">Analytics</small>
                </a>
              </div>
              <div class="col-md-4 col-6 mb-3">
                <a routerLink="/security/dashboard" class="quick-action-card">
                  <div class="quick-action-icon bg-light-danger">
                    <i data-feather="shield" class="text-danger"></i>
                  </div>
                  <h6 class="mt-2 mb-0">Security</h6>
                  <small class="text-muted">Monitor system</small>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title">Recent Security Events</h4>
          </div>
          <div class="card-body">
            <div class="recent-events" *ngIf="recentSecurityEvents.length > 0; else noEvents">
              <div class="event-item" *ngFor="let event of recentSecurityEvents">
                <div class="event-icon" [ngClass]="getEventIconClass(event.severity)">
                  <i [attr.data-feather]="getEventIcon(event.eventType)"></i>
                </div>
                <div class="event-content">
                  <h6 class="mb-0">{{ event.eventType }}</h6>
                  <small class="text-muted">{{ event.timestamp | date:'short' }}</small>
                </div>
              </div>
            </div>
            <ng-template #noEvents>
              <div class="text-center py-3">
                <i data-feather="shield-check" class="text-success mb-2" style="width: 48px; height: 48px;"></i>
                <p class="text-muted mb-0">No recent security events</p>
              </div>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

