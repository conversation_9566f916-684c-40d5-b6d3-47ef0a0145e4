@startuml
actor employé
rectangle "Système" {
  usecase "Gérer les dépôts" as UC_GererDepots
  usecase "Ajouter un dépôt" as UC_AjouterDepot
  usecase "Consulter les dépôts" as UC_ConsulterDepots
  usecase "Supprimer un dépôt" as UC_SupprimerDepot
  usecase "Modifier un dépôt" as UC_ModifierDepot

  employé -- UC_GererDepots

  UC_GererDepots <.. UC_AjouterDepot : <<include>>
  UC_GererDepots <.. UC_ConsulterDepots : <<extends>>
  UC_ConsulterDepots <.. UC_SupprimerDepot : <<extends>>
  UC_ConsulterDepots <.. UC_ModifierDepot : <<extends>>
}
@enduml

