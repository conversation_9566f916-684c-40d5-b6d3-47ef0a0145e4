using System.ComponentModel.DataAnnotations;

namespace MskStoreAPI.Models.Security
{
    public class PasswordPolicy
    {
        [Key]
        public int Id { get; set; }
        
        // Password complexity requirements
        public int MinimumLength { get; set; } = 8;
        public int MaximumLength { get; set; } = 128;
        public bool RequireUppercase { get; set; } = true;
        public bool RequireLowercase { get; set; } = true;
        public bool RequireDigits { get; set; } = true;
        public bool RequireSpecialCharacters { get; set; } = true;
        public string AllowedSpecialCharacters { get; set; } = "!@#$%^&*()_+-=[]{}|;:,.<>?";
        
        // Password history and expiration
        public int PasswordHistoryCount { get; set; } = 5; // Remember last 5 passwords
        public int PasswordExpirationDays { get; set; } = 90; // Password expires after 90 days
        public int PasswordWarningDays { get; set; } = 7; // Warn user 7 days before expiration
        
        // Account lockout policies
        public int MaxFailedAttempts { get; set; } = 5;
        public int LockoutDurationMinutes { get; set; } = 30;
        public bool EnableProgressiveLockout { get; set; } = true; // Increase lockout time with repeated failures
        
        // Session security
        public int SessionTimeoutMinutes { get; set; } = 60;
        public int MaxConcurrentSessions { get; set; } = 3;
        public bool ForceLogoutOnPasswordChange { get; set; } = true;
        
        // Security settings
        public bool EnableTwoFactorAuthentication { get; set; } = false;
        public bool RequirePasswordChangeOnFirstLogin { get; set; } = true;
        public bool PreventPasswordReuse { get; set; } = true;
        
        // Validation patterns
        public string ForbiddenPatterns { get; set; } = "password,123456,qwerty,admin,user"; // Comma-separated
        public bool CheckCommonPasswords { get; set; } = true;
        public bool CheckPersonalInformation { get; set; } = true; // Prevent using name, email, etc.
        
        // Audit settings
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;
    }

    public class PasswordHistory
    {
        [Key]
        public int Id { get; set; }
        public int UserId { get; set; }
        public string HashedPassword { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string CreatedByIp { get; set; } = string.Empty;
    }

    public class PasswordValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public int StrengthScore { get; set; } // 0-100
        public string StrengthLevel { get; set; } = "Weak"; // Weak, Fair, Good, Strong, Excellent
        public List<string> Suggestions { get; set; } = new List<string>();
    }

    public class PasswordChangeRequest
    {
        [Required]
        public string CurrentPassword { get; set; } = string.Empty;
        
        [Required]
        public string NewPassword { get; set; } = string.Empty;
        
        [Required]
        public string ConfirmPassword { get; set; } = string.Empty;
        
        public bool ForceChange { get; set; } = false;
    }
}
