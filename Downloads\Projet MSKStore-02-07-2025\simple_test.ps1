$body = @{
    name = "Test"
    lastName = "User"
    email = "<EMAIL>"
    password = "Test123!"
    phone = "1234567890"
    role = "Admin"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $body
    Write-Host "SUCCESS! User created:"
    Write-Host ($response | ConvertTo-Json)
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
}
