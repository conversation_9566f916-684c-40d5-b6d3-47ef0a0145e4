@startuml
actor employé
rectangle "Système" {
  usecase "Gérer la caisse" as UC_GererCaisse
  usecase "Ajouter une vente" as UC_AjouterVente
  usecase "Gérer les factures" as UC_GererFactures
  usecase "Consulter les factures" as UC_ConsulterFactures
  usecase "Gérer les crédits" as UC_GererCredits
  usecase "Ajouter un client" as UC_AjouterClient
  usecase "Gérer les clients" as UC_GererClients
  usecase "Consulter les clients" as UC_ConsulterClients
  usecase "Supprimer un client" as UC_SupprimerClient
  usecase "Modifier un client" as UC_ModifierClient
  usecase "Gérer la fermeture de caisse" as UC_GererFermetureCaisse
  usecase "Ajouter une fermeture de caisse" as UC_AjouterFermetureCaisse
  usecase "Consulter les fermetures de caisse" as UC_ConsulterFermetureCaisse
  usecase "Gérer les types ticket resto" as UC_GererTypesTicketResto
  usecase "Ajouter un type ticket resto" as UC_AjouterTypeTicketResto
  usecase "Consulter les types ticket resto" as UC_ConsulterTypesTicketResto
  usecase "Supprimer un type ticket resto" as UC_SupprimerTypeTicketResto
  usecase "Modifier un type ticket resto" as UC_ModifierTypeTicketResto
  usecase "Ajouter une facture" as UC_AjouterFacture
  usecase "Modifier rangement" as UC_ModifierRangement

  employé -- UC_GererCaisse
  employé -- UC_GererFactures
  employé -- UC_GererCredits
  employé -- UC_GererClients
  employé -- UC_GererFermetureCaisse
  employé -- UC_GererTypesTicketResto

  UC_GererCaisse <.. UC_AjouterVente : <<extends>>
  UC_AjouterVente <.. UC_AjouterFacture : <<include>>
  UC_AjouterVente <.. UC_ModifierRangement : <<include>>
  UC_GererFactures <.. UC_ConsulterFactures : <<extends>>
  UC_GererClients <.. UC_AjouterClient : <<extends>>
  UC_GererClients <.. UC_ConsulterClients : <<extends>>
  UC_ConsulterClients <.. UC_SupprimerClient : <<extends>>
  UC_ConsulterClients <.. UC_ModifierClient : <<extends>>
  UC_GererFermetureCaisse <.. UC_AjouterFermetureCaisse : <<extends>>
  UC_GererFermetureCaisse <.. UC_ConsulterFermetureCaisse : <<extends>>
  UC_GererTypesTicketResto <.. UC_AjouterTypeTicketResto : <<extends>>
  UC_GererTypesTicketResto <.. UC_ConsulterTypesTicketResto : <<extends>>
  UC_ConsulterTypesTicketResto <.. UC_SupprimerTypeTicketResto : <<extends>>
  UC_ConsulterTypesTicketResto <.. UC_ModifierTypeTicketResto : <<extends>>
}
@enduml

