# Bypass SSL certificate validation
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12

try {
    Write-Host "Testing GET /api/Users endpoint..."
    
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method GET
    Write-Host "SUCCESS! Users retrieved:"
    Write-Host ($response | ConvertTo-Json)
} catch {
    Write-Host "Expected 401 Unauthorized (authentication required):"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
}
