using MskStoreAPI.Data;
using MskStoreAPI.Models.Security;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;
using System.Security.Cryptography;
using System.Text;
using BCryptNet = BCrypt.Net.BCrypt;
using System.Web;

namespace MskStoreAPI.Services
{
    public class PasswordValidationService : IPasswordValidationService
    {
        private readonly DataContext _context;
        private readonly ISecurityService _securityService;
        private readonly ILogger<PasswordValidationService> _logger;

        // Common weak passwords list (subset)
        private readonly HashSet<string> _commonPasswords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "password", "123456", "password123", "admin", "qwerty", "letmein", "welcome",
            "monkey", "1234567890", "abc123", "111111", "123123", "password1", "1234",
            "12345", "dragon", "master", "login", "passw0rd", "football", "baseball",
            "superman", "access", "shadow", "trustno1", "jordan23", "harley", "ranger",
            "sunshine", "princess", "azerty", "654321", "daniel", "thomas", "hannah"
        };

        // Malicious patterns for input validation
        private readonly List<Regex> _maliciousPatterns = new List<Regex>
        {
            new Regex(@"<script[^>]*>.*?</script>", RegexOptions.IgnoreCase | RegexOptions.Singleline),
            new Regex(@"javascript:", RegexOptions.IgnoreCase),
            new Regex(@"vbscript:", RegexOptions.IgnoreCase),
            new Regex(@"onload\s*=", RegexOptions.IgnoreCase),
            new Regex(@"onerror\s*=", RegexOptions.IgnoreCase),
            new Regex(@"onclick\s*=", RegexOptions.IgnoreCase),
            new Regex(@"<iframe[^>]*>", RegexOptions.IgnoreCase),
            new Regex(@"<object[^>]*>", RegexOptions.IgnoreCase),
            new Regex(@"<embed[^>]*>", RegexOptions.IgnoreCase),
            new Regex(@"eval\s*\(", RegexOptions.IgnoreCase),
            new Regex(@"expression\s*\(", RegexOptions.IgnoreCase),
            new Regex(@"union\s+select", RegexOptions.IgnoreCase),
            new Regex(@"drop\s+table", RegexOptions.IgnoreCase),
            new Regex(@"insert\s+into", RegexOptions.IgnoreCase),
            new Regex(@"delete\s+from", RegexOptions.IgnoreCase),
            new Regex(@"update\s+set", RegexOptions.IgnoreCase),
            new Regex(@"--\s*$", RegexOptions.Multiline),
            new Regex(@"/\*.*?\*/", RegexOptions.Singleline),
            new Regex(@"xp_cmdshell", RegexOptions.IgnoreCase),
            new Regex(@"sp_executesql", RegexOptions.IgnoreCase)
        };

        public PasswordValidationService(DataContext context, ISecurityService securityService, ILogger<PasswordValidationService> logger)
        {
            _context = context;
            _securityService = securityService;
            _logger = logger;
        }

        public async Task<PasswordValidationResult> ValidatePasswordAsync(string password, int? userId = null, string? email = null, string? name = null)
        {
            var result = new PasswordValidationResult();
            var policy = await GetPasswordPolicyAsync();

            try
            {
                // Basic validation
                if (string.IsNullOrWhiteSpace(password))
                {
                    result.Errors.Add("Password cannot be empty");
                    return result;
                }

                // Length validation
                if (password.Length < policy.MinimumLength)
                {
                    result.Errors.Add($"Password must be at least {policy.MinimumLength} characters long");
                }

                if (password.Length > policy.MaximumLength)
                {
                    result.Errors.Add($"Password cannot exceed {policy.MaximumLength} characters");
                }

                // Character requirements
                if (policy.RequireUppercase && !password.Any(char.IsUpper))
                {
                    result.Errors.Add("Password must contain at least one uppercase letter");
                }

                if (policy.RequireLowercase && !password.Any(char.IsLower))
                {
                    result.Errors.Add("Password must contain at least one lowercase letter");
                }

                if (policy.RequireDigits && !password.Any(char.IsDigit))
                {
                    result.Errors.Add("Password must contain at least one digit");
                }

                if (policy.RequireSpecialCharacters && !password.Any(c => policy.AllowedSpecialCharacters.Contains(c)))
                {
                    result.Errors.Add($"Password must contain at least one special character from: {policy.AllowedSpecialCharacters}");
                }

                // Check against common passwords
                if (policy.CheckCommonPasswords && _commonPasswords.Contains(password))
                {
                    result.Errors.Add("Password is too common. Please choose a more unique password");
                }

                // Check forbidden patterns
                if (!string.IsNullOrEmpty(policy.ForbiddenPatterns))
                {
                    var forbiddenWords = policy.ForbiddenPatterns.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var word in forbiddenWords)
                    {
                        if (password.ToLower().Contains(word.Trim().ToLower()))
                        {
                            result.Errors.Add($"Password cannot contain the word '{word.Trim()}'");
                        }
                    }
                }

                // Check against personal information
                if (policy.CheckPersonalInformation)
                {
                    if (!string.IsNullOrEmpty(email) && password.ToLower().Contains(email.Split('@')[0].ToLower()))
                    {
                        result.Errors.Add("Password cannot contain your email username");
                    }

                    if (!string.IsNullOrEmpty(name) && password.ToLower().Contains(name.ToLower()))
                    {
                        result.Errors.Add("Password cannot contain your name");
                    }
                }

                // Check password reuse
                if (userId.HasValue && policy.PreventPasswordReuse)
                {
                    if (await IsPasswordReusedAsync(userId.Value, password))
                    {
                        result.Errors.Add($"Password has been used recently. Please choose a different password");
                    }
                }

                // Calculate strength score
                result.StrengthScore = CalculatePasswordStrength(password);
                result.StrengthLevel = GetStrengthLevel(result.StrengthScore);

                // Add suggestions for improvement
                AddPasswordSuggestions(password, result, policy);

                result.IsValid = result.Errors.Count == 0;

                // Log validation attempt
                await _securityService.LogSecurityEventAsync(
                    "PASSWORD_VALIDATION",
                    $"Password validation performed. Valid: {result.IsValid}, Strength: {result.StrengthLevel}",
                    userId,
                    result.IsValid ? "INFO" : "WARNING",
                    result.IsValid
                );

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password validation");
                result.Errors.Add("An error occurred during password validation");
                return result;
            }
        }

        public async Task<PasswordValidationResult> ValidatePasswordChangeAsync(int userId, PasswordChangeRequest request)
        {
            var result = new PasswordValidationResult();

            try
            {
                // Validate new password matches confirmation
                if (request.NewPassword != request.ConfirmPassword)
                {
                    result.Errors.Add("New password and confirmation do not match");
                    return result;
                }

                // Get user information for validation
                var user = await _context.User.FindAsync(userId);
                if (user == null)
                {
                    result.Errors.Add("User not found");
                    return result;
                }

                // Verify current password (unless force change)
                if (!request.ForceChange)
                {
                    if (!BCryptNet.Verify(request.CurrentPassword, user.password))
                    {
                        result.Errors.Add("Current password is incorrect");
                        await _securityService.LogSecurityEventAsync(
                            "PASSWORD_CHANGE_FAILED",
                            "Invalid current password provided during password change",
                            userId,
                            "WARNING",
                            false
                        );
                        return result;
                    }
                }

                // Validate new password
                var passwordValidation = await ValidatePasswordAsync(request.NewPassword, userId, user.email, user.name);
                result.Errors.AddRange(passwordValidation.Errors);
                result.Warnings.AddRange(passwordValidation.Warnings);
                result.Suggestions.AddRange(passwordValidation.Suggestions);
                result.StrengthScore = passwordValidation.StrengthScore;
                result.StrengthLevel = passwordValidation.StrengthLevel;

                result.IsValid = result.Errors.Count == 0;

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password change validation");
                result.Errors.Add("An error occurred during password change validation");
                return result;
            }
        }

        public async Task<bool> IsPasswordReusedAsync(int userId, string password)
        {
            try
            {
                var policy = await GetPasswordPolicyAsync();
                if (!policy.PreventPasswordReuse || policy.PasswordHistoryCount <= 0)
                    return false;

                var recentPasswords = await _context.Set<PasswordHistory>()
                    .Where(ph => ph.UserId == userId)
                    .OrderByDescending(ph => ph.CreatedAt)
                    .Take(policy.PasswordHistoryCount)
                    .Select(ph => ph.HashedPassword)
                    .ToListAsync();

                return recentPasswords.Any(hashedPassword => BCryptNet.Verify(password, hashedPassword));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking password reuse for user {UserId}", userId);
                return false;
            }
        }

        public async Task AddPasswordToHistoryAsync(int userId, string hashedPassword, string ipAddress)
        {
            try
            {
                var policy = await GetPasswordPolicyAsync();
                if (policy.PasswordHistoryCount <= 0)
                    return;

                // Add new password to history
                var passwordHistory = new PasswordHistory
                {
                    UserId = userId,
                    HashedPassword = hashedPassword,
                    CreatedAt = DateTime.UtcNow,
                    CreatedByIp = ipAddress
                };

                _context.Set<PasswordHistory>().Add(passwordHistory);

                // Remove old passwords beyond the history limit
                var oldPasswords = await _context.Set<PasswordHistory>()
                    .Where(ph => ph.UserId == userId)
                    .OrderByDescending(ph => ph.CreatedAt)
                    .Skip(policy.PasswordHistoryCount)
                    .ToListAsync();

                if (oldPasswords.Any())
                {
                    _context.Set<PasswordHistory>().RemoveRange(oldPasswords);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding password to history for user {UserId}", userId);
            }
        }

        public async Task<PasswordPolicy> GetPasswordPolicyAsync()
        {
            try
            {
                var policy = await _context.Set<PasswordPolicy>()
                    .Where(p => p.IsActive)
                    .FirstOrDefaultAsync();

                if (policy == null)
                {
                    // Create default policy if none exists
                    policy = new PasswordPolicy();
                    _context.Set<PasswordPolicy>().Add(policy);
                    await _context.SaveChangesAsync();
                }

                return policy;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting password policy");
                return new PasswordPolicy(); // Return default policy
            }
        }

        public async Task<bool> UpdatePasswordPolicyAsync(PasswordPolicy policy)
        {
            try
            {
                // Deactivate current policy
                var currentPolicies = await _context.Set<PasswordPolicy>()
                    .Where(p => p.IsActive)
                    .ToListAsync();

                foreach (var currentPolicy in currentPolicies)
                {
                    currentPolicy.IsActive = false;
                    currentPolicy.UpdatedAt = DateTime.UtcNow;
                }

                // Add new policy
                policy.IsActive = true;
                policy.CreatedAt = DateTime.UtcNow;
                policy.UpdatedAt = DateTime.UtcNow;
                _context.Set<PasswordPolicy>().Add(policy);

                await _context.SaveChangesAsync();

                await _securityService.LogSecurityEventAsync(
                    "PASSWORD_POLICY_UPDATED",
                    "Password policy has been updated",
                    null,
                    "INFO",
                    true
                );

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating password policy");
                return false;
            }
        }

        public int CalculatePasswordStrength(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return 0;

            int score = 0;

            // Length scoring
            if (password.Length >= 8) score += 10;
            if (password.Length >= 12) score += 10;
            if (password.Length >= 16) score += 10;

            // Character variety scoring
            if (password.Any(char.IsLower)) score += 10;
            if (password.Any(char.IsUpper)) score += 10;
            if (password.Any(char.IsDigit)) score += 10;
            if (password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c))) score += 15;

            // Pattern complexity
            if (HasMixedCase(password)) score += 5;
            if (HasNumbersAndLetters(password)) score += 5;
            if (HasSpecialCharacterVariety(password)) score += 10;

            // Penalty for common patterns
            if (HasRepeatingCharacters(password)) score -= 10;
            if (HasSequentialCharacters(password)) score -= 10;
            if (_commonPasswords.Contains(password)) score -= 25;

            return Math.Max(0, Math.Min(100, score));
        }

        public string GenerateSecurePassword(int length = 12)
        {
            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digits = "0123456789";
            const string special = "!@#$%^&*()_+-=[]{}|;:,.<>?";

            var password = new StringBuilder();
            using (var rng = RandomNumberGenerator.Create())
            {
                // Ensure at least one character from each required set
                password.Append(GetRandomCharacter(lowercase, rng));
                password.Append(GetRandomCharacter(uppercase, rng));
                password.Append(GetRandomCharacter(digits, rng));
                password.Append(GetRandomCharacter(special, rng));

                // Fill remaining length with random characters from all sets
                string allChars = lowercase + uppercase + digits + special;
                for (int i = 4; i < length; i++)
                {
                    password.Append(GetRandomCharacter(allChars, rng));
                }
            }

            // Shuffle the password to avoid predictable patterns
            return ShuffleString(password.ToString());
        }

        public async Task<bool> IsPasswordNearExpirationAsync(int userId)
        {
            try
            {
                var policy = await GetPasswordPolicyAsync();
                if (policy.PasswordExpirationDays <= 0)
                    return false;

                var lastPasswordChange = await GetLastPasswordChangeAsync(userId);
                var daysSinceChange = (DateTime.UtcNow - lastPasswordChange).Days;
                var daysUntilExpiration = policy.PasswordExpirationDays - daysSinceChange;

                return daysUntilExpiration <= policy.PasswordWarningDays;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking password expiration for user {UserId}", userId);
                return false;
            }
        }

        public async Task<int> GetDaysUntilPasswordExpiresAsync(int userId)
        {
            try
            {
                var policy = await GetPasswordPolicyAsync();
                if (policy.PasswordExpirationDays <= 0)
                    return -1; // No expiration

                var lastPasswordChange = await GetLastPasswordChangeAsync(userId);
                var daysSinceChange = (DateTime.UtcNow - lastPasswordChange).Days;
                return Math.Max(0, policy.PasswordExpirationDays - daysSinceChange);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting days until password expires for user {UserId}", userId);
                return -1;
            }
        }

        public async Task<bool> ForcePasswordChangeAsync(int userId)
        {
            try
            {
                var user = await _context.User.FindAsync(userId);
                if (user == null)
                    return false;

                // Add a flag to force password change (you might need to add this field to User model)
                // For now, we'll log the event
                await _securityService.LogSecurityEventAsync(
                    "PASSWORD_CHANGE_FORCED",
                    $"Password change forced for user {userId}",
                    userId,
                    "INFO",
                    true
                );

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error forcing password change for user {UserId}", userId);
                return false;
            }
        }

        public bool ValidateInputSecurity(string input, string inputType = "general")
        {
            if (string.IsNullOrWhiteSpace(input))
                return true;

            // Check for malicious patterns
            foreach (var pattern in _maliciousPatterns)
            {
                if (pattern.IsMatch(input))
                {
                    return false;
                }
            }

            // Additional validation based on input type
            switch (inputType.ToLower())
            {
                case "email":
                    return IsValidEmail(input);
                case "phone":
                    return IsValidPhone(input);
                case "name":
                    return IsValidName(input);
                default:
                    return true;
            }
        }

        public string SanitizeInput(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            // HTML encode to prevent XSS
            string sanitized = HttpUtility.HtmlEncode(input);

            // Remove or replace potentially dangerous characters
            sanitized = sanitized.Replace("<", "&lt;")
                                 .Replace(">", "&gt;")
                                 .Replace("\"", "&quot;")
                                 .Replace("'", "&#x27;")
                                 .Replace("/", "&#x2F;");

            return sanitized.Trim();
        }

        // Helper methods
        private string GetStrengthLevel(int score)
        {
            return score switch
            {
                >= 80 => "Excellent",
                >= 60 => "Strong",
                >= 40 => "Good",
                >= 20 => "Fair",
                _ => "Weak"
            };
        }

        private void AddPasswordSuggestions(string password, PasswordValidationResult result, PasswordPolicy policy)
        {
            if (password.Length < policy.MinimumLength)
                result.Suggestions.Add($"Use at least {policy.MinimumLength} characters");

            if (policy.RequireUppercase && !password.Any(char.IsUpper))
                result.Suggestions.Add("Add uppercase letters");

            if (policy.RequireLowercase && !password.Any(char.IsLower))
                result.Suggestions.Add("Add lowercase letters");

            if (policy.RequireDigits && !password.Any(char.IsDigit))
                result.Suggestions.Add("Add numbers");

            if (policy.RequireSpecialCharacters && !password.Any(c => policy.AllowedSpecialCharacters.Contains(c)))
                result.Suggestions.Add("Add special characters");

            if (result.StrengthScore < 60)
                result.Suggestions.Add("Consider using a longer password with more character variety");
        }

        private bool HasMixedCase(string password)
        {
            return password.Any(char.IsUpper) && password.Any(char.IsLower);
        }

        private bool HasNumbersAndLetters(string password)
        {
            return password.Any(char.IsDigit) && password.Any(char.IsLetter);
        }

        private bool HasSpecialCharacterVariety(string password)
        {
            var specialChars = password.Where(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)).Distinct();
            return specialChars.Count() >= 2;
        }

        private bool HasRepeatingCharacters(string password)
        {
            for (int i = 0; i < password.Length - 2; i++)
            {
                if (password[i] == password[i + 1] && password[i + 1] == password[i + 2])
                    return true;
            }
            return false;
        }

        private bool HasSequentialCharacters(string password)
        {
            for (int i = 0; i < password.Length - 2; i++)
            {
                if (password[i] + 1 == password[i + 1] && password[i + 1] + 1 == password[i + 2])
                    return true;
                if (password[i] - 1 == password[i + 1] && password[i + 1] - 1 == password[i + 2])
                    return true;
            }
            return false;
        }

        private char GetRandomCharacter(string chars, RandomNumberGenerator rng)
        {
            byte[] randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            int randomIndex = Math.Abs(BitConverter.ToInt32(randomBytes, 0)) % chars.Length;
            return chars[randomIndex];
        }

        private string ShuffleString(string input)
        {
            var chars = input.ToCharArray();
            using (var rng = RandomNumberGenerator.Create())
            {
                for (int i = chars.Length - 1; i > 0; i--)
                {
                    byte[] randomBytes = new byte[4];
                    rng.GetBytes(randomBytes);
                    int j = Math.Abs(BitConverter.ToInt32(randomBytes, 0)) % (i + 1);
                    (chars[i], chars[j]) = (chars[j], chars[i]);
                }
            }
            return new string(chars);
        }

        private async Task<DateTime> GetLastPasswordChangeAsync(int userId)
        {
            try
            {
                var lastChange = await _context.Set<PasswordHistory>()
                    .Where(ph => ph.UserId == userId)
                    .OrderByDescending(ph => ph.CreatedAt)
                    .Select(ph => ph.CreatedAt)
                    .FirstOrDefaultAsync();

                return lastChange == default ? DateTime.UtcNow.AddDays(-90) : lastChange;
            }
            catch
            {
                return DateTime.UtcNow.AddDays(-90); // Default to 90 days ago
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
                return emailRegex.IsMatch(email) && email.Length <= 254;
            }
            catch
            {
                return false;
            }
        }

        private bool IsValidPhone(string phone)
        {
            var phoneRegex = new Regex(@"^[\+]?[1-9][\d]{0,15}$");
            return phoneRegex.IsMatch(phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", ""));
        }

        private bool IsValidName(string name)
        {
            var nameRegex = new Regex(@"^[a-zA-Z\s\-\.\']+$");
            return nameRegex.IsMatch(name) && name.Length <= 100;
        }

        public async Task<bool> ValidateInputSecurityAsync(string input, string inputType = "general")
        {
            return ValidateInputSecurity(input, inputType);
        }

        public async Task<PasswordStatusResult> CheckPasswordStatusAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return new PasswordStatusResult
                {
                    RequiresChange = true,
                    Message = "User not found"
                };
            }

            var policy = await GetPasswordPolicyAsync();
            var lastChanged = user.authpasswordchangedate ?? DateTime.UtcNow.AddDays(-policy.MaxPasswordAge);
            var daysSinceChange = (DateTime.UtcNow - lastChanged).Days;
            var daysUntilExpiration = policy.MaxPasswordAge - daysSinceChange;

            return new PasswordStatusResult
            {
                IsExpired = daysSinceChange >= policy.MaxPasswordAge,
                IsNearExpiration = daysUntilExpiration <= 7,
                DaysUntilExpiration = Math.Max(0, daysUntilExpiration),
                RequiresChange = user.authrequirepasswordchange ?? false,
                LastChanged = lastChanged
            };
        }
    }
}
