  <div class="modern-list-container">
    <!-- Modern List Header -->
    <div class="modern-list-header">
      <div class="list-title-section">
        <h2 class="list-title">
          <i data-feather="shopping-cart" class="title-icon"></i>
          Client Orders
        </h2>
        <p class="list-subtitle">Manage and view all client orders</p>
      </div>

      <div class="list-actions">
        <div class="filter-group">
          <label class="filter-label">
            <i data-feather="list" class="label-icon"></i>
            {{ 'Show' | translate }}
          </label>
          <select
            class="form-control modern-select"
            [(ngModel)]="pageSize"
            (change)="paginate($event)">
            <option value="5">5 items</option>
            <option value="10">10 items</option>
            <option value="20">20 items</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Modern Data Table -->
    <div class="modern-table-container">
      <div class="table-wrapper">
        <table class="modern-table">
          <thead class="table-header">
            <tr>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="hash" class="header-icon"></i>
                  ID
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="calendar" class="header-icon"></i>
                  {{ 'Date' | translate }}
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="user" class="header-icon"></i>
                  {{ 'client' | translate }}
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="dollar-sign" class="header-icon"></i>
                  {{ 'Total' | translate }}
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="info" class="header-icon"></i>
                  {{ 'etat' | translate }}
                </div>
              </th>
              <th class="table-header-cell">
                <div class="header-content">
                  <i data-feather="settings" class="header-icon"></i>
                  Actions
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="table-body">
            <tr
              class="table-row"
              *ngFor="let commande of commandeclients | paginate: { itemsPerPage: pageSize, currentPage: currentPage }">

              <td class="table-cell">
                <div class="cell-content">
                  <a [routerLink]="['/affichercommandeclient', commande.id]" class="id-link">
                    <span class="id-badge">{{ commande.id }}</span>
                  </a>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <div class="date-info">
                    <span class="date-value">{{ commande.datecommande | date: 'dd/MM/yyyy' }}</span>
                    <span class="time-value">{{ commande.datecommande | date: 'HH:mm:ss' }}</span>
                  </div>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <div class="client-info">
                    <span class="client-id">{{ commande.idclientweb }}</span>
                    <span class="client-name">{{ getclientwebName(commande.idclientweb) }}</span>
                  </div>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <span class="price-value">{{ commande.total | currency }}</span>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <span class="status-badge" [ngClass]="'status-' + commande.etat?.toLowerCase()">
                    <i data-feather="circle" class="status-icon"></i>
                    {{ commande.etat }}
                  </span>
                </div>
              </td>

              <td class="table-cell">
                <div class="cell-content">
                  <div class="action-buttons">
                    <a [routerLink]="['/affichercommandeclient', commande.id]" class="btn-action btn-view" title="View Order">
                      <i data-feather="eye" class="action-icon"></i>
                    </a>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="!commandeclients || commandeclients.length === 0">
          <div class="empty-icon">
            <i data-feather="shopping-cart" class="empty-icon-svg"></i>
          </div>
          <h3 class="empty-title">No Orders Found</h3>
          <p class="empty-description">There are no client orders to display.</p>
        </div>
      </div>
    </div>

    <!-- Modern Pagination -->
    <div class="modern-pagination-container">
      <pagination-controls
        (pageChange)="currentPage = $event"
        previousLabel="Previous"
        nextLabel="Next"
        class="modern-pagination">
      </pagination-controls>
    </div>
  </div>