# MSKStore Database Setup and User Creation Script
param(
    [switch]$ResetDatabase = $false,
    [switch]$CreateSampleUsers = $true
)

Write-Host "🗄️ MSKStore Database Setup Script" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Check if we're in the right directory
if (-not (Test-Path "MskStoreAPI\MskStoreAPI\MskStoreAPI.csproj")) {
    Write-Host "❌ Please run this script from the project root directory" -ForegroundColor Red
    Write-Host "Expected: Downloads\Projet MSKStore-02-07-2025\" -ForegroundColor Yellow
    exit 1
}

# Navigate to API directory
Set-Location "MskStoreAPI\MskStoreAPI"
Write-Host "📁 Navigated to API directory" -ForegroundColor Green

# Check .NET SDK
Write-Host "`n🔍 Checking .NET SDK..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET SDK not found. Please install .NET 7 SDK" -ForegroundColor Red
    exit 1
}

# Reset database if requested
if ($ResetDatabase) {
    Write-Host "`n🔄 Resetting database..." -ForegroundColor Yellow
    dotnet ef database drop --force
    Write-Host "✅ Database dropped" -ForegroundColor Green
}

# Check existing migrations
Write-Host "`n📋 Checking migrations..." -ForegroundColor Yellow
$migrations = dotnet ef migrations list 2>$null
if ($migrations -match "20230526195941_initial") {
    Write-Host "✅ Initial migration found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Creating initial migration..." -ForegroundColor Yellow
    dotnet ef migrations add InitialCreate
}

# Apply migrations
Write-Host "`n🚀 Applying database migrations..." -ForegroundColor Yellow
try {
    dotnet ef database update
    Write-Host "✅ Database updated successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Database update failed. Check connection string in appsettings.json" -ForegroundColor Red
    Write-Host "Current connection: Data Source=.;Initial Catalog=storedbAdel;Integrated Security=true" -ForegroundColor Yellow
    exit 1
}

# Build the project
Write-Host "`n🔨 Building project..." -ForegroundColor Yellow
dotnet build --configuration Release
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Project built successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎯 Database setup completed!" -ForegroundColor Green
Write-Host "`n📊 Database Information:" -ForegroundColor Cyan
Write-Host "- Database Name: storedbAdel" -ForegroundColor White
Write-Host "- Server: . (local SQL Server)" -ForegroundColor White
Write-Host "- Authentication: Windows Integrated" -ForegroundColor White

Write-Host "`n🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Start the API: dotnet run" -ForegroundColor White
Write-Host "2. Open Swagger: https://localhost:7258/swagger" -ForegroundColor White
Write-Host "3. Create users via API or frontend" -ForegroundColor White

Write-Host "`n👥 Sample User Creation:" -ForegroundColor Cyan
Write-Host "Admin User:" -ForegroundColor Yellow
Write-Host '{
  "name": "Admin",
  "lastName": "User", 
  "email": "<EMAIL>",
  "password": "Admin123!",
  "phone": "1234567890",
  "role": "Admin"
}' -ForegroundColor White

Write-Host "`nEmployee User:" -ForegroundColor Yellow
Write-Host '{
  "name": "John",
  "lastName": "Doe",
  "email": "<EMAIL>", 
  "password": "Employee123!",
  "phone": "0987654321",
  "role": "Employee",
  "cin": 12345678,
  "typecontrat": "CDI",
  "salaire": 3000.00,
  "salairebrut": 3500.00,
  "permission": 7
}' -ForegroundColor White

Write-Host "`n📚 Documentation:" -ForegroundColor Cyan
Write-Host "- Complete guide: DATABASE_MIGRATION_USER_GUIDE.md" -ForegroundColor White
Write-Host "- Setup guide: COMPLETE_SETUP_GUIDE.md" -ForegroundColor White

# Return to original directory
Set-Location "..\..\"
Write-Host "`n✨ Database setup script completed successfully!" -ForegroundColor Green
