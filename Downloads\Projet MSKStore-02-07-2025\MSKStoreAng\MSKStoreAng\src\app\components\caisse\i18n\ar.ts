
export const locale = {
	lang: 'de',
	data: {
		Barcode: "الرمز الشريطي",
		Quantity: "الكمية",
		Name: "الاسم",
		UnitPrice: "سعر الوحدة",
		Reduction: "التخفيض",
		Total: "المجموع",
		Checkout: "الدفع",
		Date:"التاريخ",
		Cashier:"البائع",
		FactureNumber:"رقم الفاتورة",
		TotalPurchase:"مجموع الشراء",
		PaymentMethod:"طريقة الدفع",
		Cash:"نقد",
		Credit:"دين",
		Check:"شيك",
		RestaurantTicket:"بطاقة مطعم",
		AmountPaid:"المبلغ المدفوع",
		Return:"الباقي",
		Client:"الزبون",
		AccountOwner:"صاحب الحساب",
		accountNumber:"رقم الحساب",
		typeTicket:"نوع التذكرة",
		TicketCode:"رمز التذكرة",
		TotalAmountPaidwithTicket:"إجمالي المبلغ المدفوع بالتذاكر",
		AmounttobePaidinCash:"المبلغ المتبقي للدفع نقداً",
		Add: "إضافة",
		Remove: "حذف",
		AddTicket: "إضافة بطاقة",
		Save: "حفظ",
		Delete: "مسح",

		Search:"بحث..",
		
		TableName: "اسم الجدول",
Savepurchasestable: "حفظ جدول المشتريات",
SavedTables: "الجداول المحفوظة",
Loadpurchasestable: "تحميل جدول المشتريات",
Barcodedoesnotexist: "الرمز الشريطي غير موجود",
NoDeviceSelected: "لم يتم تحديد أي جهاز",
Result: "النتيجة",
Couldntcheckbarcode: "تعذر التحقق من الرمز الشريطي",
RefPrice: "السعر المرجعي",
Taxe: "الضريبة",
SellPrice: "سعر البيع",
printticket: "طباعة التذكرة",
ticketNumber: "رقم التذكرة",
Enablescanner:"تشغيل الماسح",
RestaurantTicketcredit:"بطاقة مطعم و دين",
	}
	
}