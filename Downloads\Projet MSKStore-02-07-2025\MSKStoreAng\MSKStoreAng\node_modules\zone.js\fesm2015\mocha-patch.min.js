"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */function patchMocha(t){t.__load_patch("mocha",((t,n)=>{const e=t.Mocha;if(void 0===e)return;if(void 0===n)throw new Error("Missing Zone.js");const r=n.ProxyZoneSpec,o=n.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");if(e.__zone_patch__)throw new Error('"Mocha" has already been patched with "Zone".');e.__zone_patch__=!0;const i=n.current,c=i.fork(new o("Mocha.describe"));let u=null;const s=i.fork(new r),f={after:t.after,afterEach:t.afterEach,before:t.before,beforeEach:t.beforeEach,describe:t.describe,it:t.it};function a(t,n,e){for(let r=0;r<t.length;r++){let o=t[r];"function"==typeof o&&(t[r]=0===o.length?n(o):e(o),t[r].toString=function(){return o.toString()})}return t}function h(t){return a(t,(function(t){return function(){return c.run(t,this,arguments)}}))}function p(t){return a(t,(function(t){return function(){return u.run(t,this)}}),(function(t){return function(n){return u.run(t,this,[n])}}))}function l(t){return a(t,(function(t){return function(){return s.run(t,this)}}),(function(t){return function(n){return s.run(t,this,[n])}}))}var y,d;t.describe=t.suite=function(){return f.describe.apply(this,h(arguments))},t.xdescribe=t.suite.skip=t.describe.skip=function(){return f.describe.skip.apply(this,h(arguments))},t.describe.only=t.suite.only=function(){return f.describe.only.apply(this,h(arguments))},t.it=t.specify=t.test=function(){return f.it.apply(this,p(arguments))},t.xit=t.xspecify=t.it.skip=function(){return f.it.skip.apply(this,p(arguments))},t.it.only=t.test.only=function(){return f.it.only.apply(this,p(arguments))},t.after=t.suiteTeardown=function(){return f.after.apply(this,l(arguments))},t.afterEach=t.teardown=function(){return f.afterEach.apply(this,p(arguments))},t.before=t.suiteSetup=function(){return f.before.apply(this,l(arguments))},t.beforeEach=t.setup=function(){return f.beforeEach.apply(this,p(arguments))},y=e.Runner.prototype.runTest,d=e.Runner.prototype.run,e.Runner.prototype.runTest=function(t){n.current.scheduleMicroTask("mocha.forceTask",(()=>{y.call(this,t)}))},e.Runner.prototype.run=function(t){return this.on("test",(t=>{u=i.fork(new r)})),this.on("fail",((t,n)=>{const e=u&&u.get("ProxyZoneSpec");if(e&&n)try{n.message+=e.getAndClearPendingTasksInfo()}catch(t){}})),d.call(this,t)}}))}patchMocha(Zone);