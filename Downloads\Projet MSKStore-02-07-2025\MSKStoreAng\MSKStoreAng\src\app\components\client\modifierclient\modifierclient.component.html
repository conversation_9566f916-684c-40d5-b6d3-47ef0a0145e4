    <div class="modern-form-container" *ngIf="client">
      <div class="modern-form-header">
        <h3 class="modern-form-title">
          <i data-feather="edit" class="title-icon"></i>
          {{ 'Modify Client' | translate }}
        </h3>
        <p class="modern-form-subtitle">Update client information below</p>
      </div>

      <form class="modern-form" (submit)="onSubmit()">
        <!-- Personal Information Section -->
        <div class="modern-form-section">
          <h4 class="section-title">
            <i data-feather="user" class="section-icon"></i>
            Personal Information
          </h4>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="firstName">
                <i data-feather="user" class="label-icon"></i>
                {{ 'firstName' | translate }}
              </label>
              <input
                type="text"
                class="form-control modern-input"
                id="firstname"
                name="name"
                [(ngModel)]="client.name"
                placeholder="Enter first name">
            </div>
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="lastName">
                <i data-feather="user" class="label-icon"></i>
                {{ 'lastName' | translate }}
              </label>
              <input
                type="text"
                class="form-control modern-input"
                id="lastname"
                name="lastname"
                [(ngModel)]="client.lastname"
                placeholder="Enter last name">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="priority">
                <i data-feather="flag" class="label-icon"></i>
                {{ 'Priority' | translate }}
              </label>
              <select id="priority" name="priority" class="form-control modern-select" [(ngModel)]="client.priority">
                <option [value]="'low'">{{ 'Low' | translate }}</option>
                <option [value]="'medium'">{{ 'Medium' | translate }}</option>
                <option [value]="'high'">{{ 'High' | translate }}</option>
              </select>
            </div>
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="gender">
                <i data-feather="users" class="label-icon"></i>
                {{ 'Gender' | translate }}
              </label>
              <select id="gender" name="gender" class="form-control modern-select" [(ngModel)]="client.gender">
                <option [value]="'male'">{{ 'Male' | translate }}</option>
                <option [value]="'Female'">{{ 'Female' | translate }}</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Contact Information Section -->
        <div class="modern-form-section">
          <h4 class="section-title">
            <i data-feather="phone" class="section-icon"></i>
            Contact Information
          </h4>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="phone">
                <i data-feather="phone" class="label-icon"></i>
                {{ 'Phone' | translate }}
              </label>
              <input
                type="tel"
                class="form-control modern-input"
                id="phone"
                name="phone"
                [(ngModel)]="client.phone"
                placeholder="Enter phone number">
            </div>
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="email">
                <i data-feather="mail" class="label-icon"></i>
                {{ 'Email' | translate }}
              </label>
              <input
                type="email"
                class="form-control modern-input"
                id="email"
                name="email"
                [(ngModel)]="client.email"
                placeholder="Enter email address">
            </div>
          </div>

          <div class="form-group">
            <label class="modern-form-label" for="address">
              <i data-feather="map-pin" class="label-icon"></i>
              {{ 'Address' | translate }}
            </label>
            <input
              type="text"
              class="form-control modern-input"
              id="addresse"
              name="addresse"
              [(ngModel)]="client.adresse"
              placeholder="Enter full address">
          </div>
        </div>

        <!-- Additional Information Section -->
        <div class="modern-form-section">
          <h4 class="section-title">
            <i data-feather="settings" class="section-icon"></i>
            Additional Information
          </h4>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="photo">
                <i data-feather="image" class="label-icon"></i>
                {{ 'photo' | translate }}
              </label>
              <input
                type="url"
                class="form-control modern-input"
                id="photo"
                name="photo"
                [(ngModel)]="client.photo"
                placeholder="Enter photo URL">
            </div>
            <div class="form-group col-md-6">
              <label class="modern-form-label" for="credit">
                <i data-feather="credit-card" class="label-icon"></i>
                {{ 'Credit' | translate }}
              </label>
              <input
                type="number"
                class="form-control modern-input"
                id="credit"
                name="credit"
                [(ngModel)]="client.credit"
                placeholder="Enter credit amount"
                min="0"
                step="0.01">
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="modern-form-actions">
          <button type="submit" class="btn-modern btn-primary">
            <i data-feather="save" class="btn-icon"></i>
            {{ 'update' | translate }}
          </button>
          <button type="button" class="btn-modern btn-secondary" onclick="history.back()">
            <i data-feather="x" class="btn-icon"></i>
            Cancel
          </button>
        </div>
      </form>
    </div>
    
