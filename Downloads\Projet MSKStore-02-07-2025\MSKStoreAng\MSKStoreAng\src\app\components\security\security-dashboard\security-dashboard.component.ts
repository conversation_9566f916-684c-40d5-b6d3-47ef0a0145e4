import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SecurityService, SecurityEvent } from 'app/core/services/security.service';

@Component({
  selector: 'app-security-dashboard',
  templateUrl: './security-dashboard.component.html',
  styleUrls: ['./security-dashboard.component.scss']
})
export class SecurityDashboardComponent implements OnInit, OnDestroy {
  private _unsubscribeAll: Subject<any> = new Subject<any>();

  // Security data
  securityEvents: SecurityEvent[] = [];
  sessionTimeout: number = 0;
  isSessionActive: boolean = false;
  
  // Dashboard stats
  totalEvents: number = 0;
  criticalEvents: number = 0;
  highSeverityEvents: number = 0;
  mediumSeverityEvents: number = 0;
  lowSeverityEvents: number = 0;
  
  // Event type counts
  eventTypeCounts: { [key: string]: number } = {};
  
  // Filters
  selectedSeverity: string = 'ALL';
  selectedEventType: string = 'ALL';
  filteredEvents: SecurityEvent[] = [];
  
  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 0;

  constructor(private securityService: SecurityService) {}

  ngOnInit(): void {
    this.subscribeToSecurityEvents();
    this.subscribeToSessionStatus();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  /**
   * Subscribe to security events
   */
  private subscribeToSecurityEvents(): void {
    this.securityService.getSecurityEvents()
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((events: SecurityEvent[]) => {
        this.securityEvents = events;
        this.updateDashboardStats();
        this.applyFilters();
      });
  }

  /**
   * Subscribe to session status
   */
  private subscribeToSessionStatus(): void {
    this.securityService.getSessionTimeout()
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((timeout: number) => {
        this.sessionTimeout = timeout;
      });

    this.securityService.getSessionStatus()
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((isActive: boolean) => {
        this.isSessionActive = isActive;
      });
  }

  /**
   * Update dashboard statistics
   */
  private updateDashboardStats(): void {
    this.totalEvents = this.securityEvents.length;
    
    // Count events by severity
    this.criticalEvents = this.securityEvents.filter(e => e.severity === 'CRITICAL').length;
    this.highSeverityEvents = this.securityEvents.filter(e => e.severity === 'HIGH').length;
    this.mediumSeverityEvents = this.securityEvents.filter(e => e.severity === 'MEDIUM').length;
    this.lowSeverityEvents = this.securityEvents.filter(e => e.severity === 'LOW').length;
    
    // Count events by type
    this.eventTypeCounts = {};
    this.securityEvents.forEach(event => {
      this.eventTypeCounts[event.type] = (this.eventTypeCounts[event.type] || 0) + 1;
    });
  }

  /**
   * Apply filters to events
   */
  applyFilters(): void {
    let filtered = [...this.securityEvents];
    
    // Filter by severity
    if (this.selectedSeverity !== 'ALL') {
      filtered = filtered.filter(event => event.severity === this.selectedSeverity);
    }
    
    // Filter by event type
    if (this.selectedEventType !== 'ALL') {
      filtered = filtered.filter(event => event.type === this.selectedEventType);
    }
    
    this.filteredEvents = filtered;
    this.totalPages = Math.ceil(this.filteredEvents.length / this.itemsPerPage);
    this.currentPage = 1; // Reset to first page when filters change
  }

  /**
   * Get paginated events
   */
  getPaginatedEvents(): SecurityEvent[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredEvents.slice(startIndex, endIndex);
  }

  /**
   * Change page
   */
  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  /**
   * Get severity badge class
   */
  getSeverityBadgeClass(severity: string): string {
    switch (severity) {
      case 'CRITICAL':
        return 'badge-danger';
      case 'HIGH':
        return 'badge-warning';
      case 'MEDIUM':
        return 'badge-info';
      case 'LOW':
        return 'badge-success';
      default:
        return 'badge-secondary';
    }
  }

  /**
   * Get event type icon
   */
  getEventTypeIcon(eventType: string): string {
    switch (eventType) {
      case 'LOGIN_SUCCESS':
      case 'LOGIN_ATTEMPT':
        return 'feather icon-log-in';
      case 'LOGIN_FAILED':
        return 'feather icon-x-circle';
      case 'LOGOUT':
        return 'feather icon-log-out';
      case 'TOKEN_STORED':
      case 'TOKEN_CLEARED':
        return 'feather icon-key';
      case 'SESSION_EXPIRED':
      case 'SESSION_WARNING':
        return 'feather icon-clock';
      case 'DEVTOOLS_DETECTED':
        return 'feather icon-tool';
      case 'CSP_VIOLATION':
        return 'feather icon-shield-off';
      case 'HTTP_ERROR':
        return 'feather icon-alert-triangle';
      case 'MALICIOUS_CONTENT':
        return 'feather icon-alert-octagon';
      default:
        return 'feather icon-info';
    }
  }

  /**
   * Format timestamp
   */
  formatTimestamp(timestamp: Date): string {
    return new Date(timestamp).toLocaleString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * Format session timeout
   */
  formatSessionTimeout(): string {
    if (this.sessionTimeout <= 0) {
      return 'Session expirée';
    }
    
    const hours = Math.floor(this.sessionTimeout / 3600);
    const minutes = Math.floor((this.sessionTimeout % 3600) / 60);
    const seconds = this.sessionTimeout % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get unique event types for filter dropdown
   */
  getUniqueEventTypes(): string[] {
    const types = [...new Set(this.securityEvents.map(event => event.type))];
    return types.sort();
  }

  /**
   * Clear all security events
   */
  clearAllEvents(): void {
    if (confirm('Êtes-vous sûr de vouloir effacer tous les événements de sécurité ?')) {
      localStorage.removeItem('msk_security_events');
      this.securityService.logSecurityEvent({
        type: 'EVENTS_CLEARED',
        description: 'All security events cleared by user',
        timestamp: new Date(),
        severity: 'LOW'
      });
    }
  }

  /**
   * Export security events
   */
  exportEvents(): void {
    const dataStr = JSON.stringify(this.securityEvents, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `security-events-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    this.securityService.logSecurityEvent({
      type: 'EVENTS_EXPORTED',
      description: 'Security events exported by user',
      timestamp: new Date(),
      severity: 'LOW'
    });
  }

  /**
   * Refresh dashboard data
   */
  refreshDashboard(): void {
    this.updateDashboardStats();
    this.applyFilters();
    
    this.securityService.logSecurityEvent({
      type: 'DASHBOARD_REFRESHED',
      description: 'Security dashboard refreshed by user',
      timestamp: new Date(),
      severity: 'LOW'
    });
  }
}
