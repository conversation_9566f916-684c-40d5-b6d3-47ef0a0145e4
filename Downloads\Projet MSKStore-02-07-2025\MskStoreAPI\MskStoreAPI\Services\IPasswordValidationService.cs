using MskStoreAPI.Models.Security;

namespace MskStoreAPI.Services
{
    public interface IPasswordValidationService
    {
        /// <summary>
        /// Validates a password against the current password policy
        /// </summary>
        Task<PasswordValidationResult> ValidatePasswordAsync(string password, int? userId = null, string? email = null, string? name = null);
        
        /// <summary>
        /// Validates a password change request
        /// </summary>
        Task<PasswordValidationResult> ValidatePasswordChangeAsync(int userId, PasswordChangeRequest request);
        
        /// <summary>
        /// Checks if a password has been used before by the user
        /// </summary>
        Task<bool> IsPasswordReusedAsync(int userId, string password);
        
        /// <summary>
        /// Adds a password to the user's password history
        /// </summary>
        Task AddPasswordToHistoryAsync(int userId, string hashedPassword, string ipAddress);
        
        /// <summary>
        /// Gets the current password policy
        /// </summary>
        Task<PasswordPolicy> GetPasswordPolicyAsync();
        
        /// <summary>
        /// Updates the password policy (admin only)
        /// </summary>
        Task<bool> UpdatePasswordPolicyAsync(PasswordPolicy policy);
        
        /// <summary>
        /// Calculates password strength score (0-100)
        /// </summary>
        int CalculatePasswordStrength(string password);
        
        /// <summary>
        /// Generates a secure random password that meets policy requirements
        /// </summary>
        string GenerateSecurePassword(int length = 12);
        
        /// <summary>
        /// Checks if password is about to expire
        /// </summary>
        Task<bool> IsPasswordNearExpirationAsync(int userId);
        
        /// <summary>
        /// Gets days until password expires
        /// </summary>
        Task<int> GetDaysUntilPasswordExpiresAsync(int userId);
        
        /// <summary>
        /// Forces password change for a user
        /// </summary>
        Task<bool> ForcePasswordChangeAsync(int userId);
        
        /// <summary>
        /// Validates input against common security patterns
        /// </summary>
        bool ValidateInputSecurity(string input, string inputType = "general");
        
        /// <summary>
        /// Sanitizes input to prevent XSS and injection attacks
        /// </summary>
        string SanitizeInput(string input);
    }
}
