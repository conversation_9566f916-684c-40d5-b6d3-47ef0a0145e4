/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { EventTask as _EventTask, HasTaskState as _HasTaskState, MacroTask as _MacroTask, MicroTask as _MicroTask, PatchFn, Task as _Task, TaskData as _TaskData, TaskState as _TaskState, TaskType as _TaskType, UncaughtPromiseError as _UncaughtPromiseError, Zone as _Zone, ZoneDelegate as _ZoneDelegate, ZoneFrame, ZonePrivate, ZoneSpec as _ZoneSpec, ZoneType as _ZoneType } from './zone-impl';
declare global {
    const Zone: ZoneType;
    type Zone = _Zone;
    type ZoneType = _ZoneType;
    type _PatchFn = PatchFn;
    type _ZonePrivate = ZonePrivate;
    type _ZoneFrame = ZoneFrame;
    type UncaughtPromiseError = _UncaughtPromiseError;
    type ZoneSpec = _ZoneSpec;
    type ZoneDelegate = _ZoneDelegate;
    type HasTaskState = _HasTaskState;
    type TaskType = _TaskType;
    type TaskState = _TaskState;
    type TaskData = _TaskData;
    type Task = _Task;
    type MicroTask = _MicroTask;
    type MacroTask = _MacroTask;
    type EventTask = _EventTask;
    /**
     * Extend the Error with additional fields for rewritten stack frames
     */
    interface Error {
        /**
         * Stack trace where extra frames have been removed and zone names added.
         */
        zoneAwareStack?: string;
        /**
         * Original stack trace with no modifications
         */
        originalStack?: string;
    }
}
export declare function loadZone(): ZoneType;
