using System;
using System.Security.Cryptography;

namespace KeyGenerator
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== MSKStore Security Key Generator ===");
            Console.WriteLine();
            
            // Generate AES-256 key (32 bytes)
            using (var rng = RandomNumberGenerator.Create())
            {
                var keyBytes = new byte[32]; // 256 bits
                rng.GetBytes(keyBytes);
                var key = Convert.ToBase64String(keyBytes);
                
                var ivBytes = new byte[16]; // 128 bits
                rng.GetBytes(ivBytes);
                var iv = Convert.ToBase64String(ivBytes);
                
                Console.WriteLine("SECURE ENCRYPTION KEYS GENERATED:");
                Console.WriteLine("IMPORTANT: Store these keys securely and never commit them to source control!");
                Console.WriteLine();
                Console.WriteLine($"EncryptionKey: {key}");
                Console.WriteLine($"EncryptionIV: {iv}");
                Console.WriteLine();
                Console.WriteLine("Add these to your appsettings.json under Security section:");
                Console.WriteLine($"\"EncryptionKey\": \"{key}\",");
                Console.WriteLine($"\"EncryptionIV\": \"{iv}\",");
                Console.WriteLine();
                
                // Generate JWT secret
                var jwtKeyBytes = new byte[64]; // 512 bits for JWT
                rng.GetBytes(jwtKeyBytes);
                var jwtKey = Convert.ToBase64String(jwtKeyBytes);
                
                Console.WriteLine($"JWT Secret Key: {jwtKey}");
                Console.WriteLine();
                Console.WriteLine("=== KEYS GENERATED SUCCESSFULLY ===");
            }
        }
    }
}
