<!-- Modern Page Size Selector -->
<div class="modern-page-size-container">
  <label class="page-size-label">{{ 'Show' | translate }}</label>
  <select class="page-size-select" [(ngModel)]="pageSize" (change)="paginate($event)">
    <option value="5">5</option>
    <option value="10">10</option>
    <option value="20">20</option>
  </select>
</div>

<!-- Modern Table Container -->
<div class="modern-table-container">
  <div class="modern-table-header">
    <h3 class="table-title">
      <i data-feather="truck" class="title-icon"></i>
      {{ 'Supplier List' | translate }}
    </h3>
    <div class="table-actions">
      <button class="btn-modern btn-primary btn-sm" routerLink="/fournisseur/ajouter">
        <i data-feather="plus" class="btn-icon"></i>
        {{ 'Add Supplier' | translate }}
      </button>
    </div>
  </div>

  <div class="table-responsive">
    <table class="table modern-table">
      <thead>
        <tr>
          <th>
            <i data-feather="hash" class="th-icon"></i>
            #
          </th>
          <th>
            <i data-feather="user" class="th-icon"></i>
            {{ 'FirstName' | translate }}
          </th>
          <th>
            <i data-feather="user" class="th-icon"></i>
            {{ 'LastName' | translate }}
          </th>
          <th>
            <i data-feather="phone" class="th-icon"></i>
            {{ 'Phone' | translate }}
          </th>
          <th>
            <i data-feather="tag" class="th-icon"></i>
            {{ 'type' | translate }}
          </th>
          <th>
            <i data-feather="settings" class="th-icon"></i>
            Actions
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let fournisseur of fournisseurs | paginate: { itemsPerPage: pageSize, currentPage: currentPage }"
            class="modern-table-row">
          <td>
            <a [routerLink]="['/afficherfournisseur', fournisseur.id]" class="supplier-id-link">
              <span class="id-badge">{{fournisseur.id}}</span>
            </a>
          </td>
          <td>
            <span class="supplier-name">{{ fournisseur.nom }}</span>
          </td>
          <td>
            <span class="supplier-name">{{ fournisseur.prenom }}</span>
          </td>
          <td>
            <span class="contact-info">{{ fournisseur.telephone }}</span>
          </td>
          <td>
            <span class="modern-badge badge-info">
              <i data-feather="tag" class="badge-icon"></i>
              {{ fournisseur.type }}
            </span>
          </td>
          <td>
            <div class="action-buttons">
              <a [routerLink]="['/afficherfournisseur', fournisseur.id]" class="btn-action btn-view" title="View">
                <i data-feather="eye" class="action-icon"></i>
              </a>
              <a [routerLink]="['/modifierfournisseur', fournisseur.id]" class="btn-action btn-edit" title="Edit">
                <i data-feather="edit" class="action-icon"></i>
              </a>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Modern Pagination -->
<div class="modern-pagination-container">
  <div class="pagination-info">
    <span class="pagination-text">
      Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, fournisseurs.length) }}
      of {{ fournisseurs.length }} suppliers
    </span>
  </div>
  <div class="modern-pagination">
    <pagination-controls
      (pageChange)="currentPage = $event"
      previousLabel="Previous"
      nextLabel="Next"
      class="modern-pagination-controls">
    </pagination-controls>
  </div>
</div>
  
  
