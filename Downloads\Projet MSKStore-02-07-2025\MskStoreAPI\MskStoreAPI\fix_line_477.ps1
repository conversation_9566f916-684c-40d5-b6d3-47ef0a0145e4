# Fix the specific line 477 issue in PasswordValidationService.cs

Write-Host "Fixing line 477 in PasswordValidationService.cs..." -ForegroundColor Green

$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath

# Find and fix the specific line with "null, null,"
for ($i = 0; $i -lt $content.Length; $i++) {
    if ($content[$i] -match '^\s+null, null,\s*$') {
        Write-Host "Found problematic line at $($i+1): $($content[$i])" -ForegroundColor Yellow
        # Replace with two separate lines
        $content[$i] = $content[$i] -replace 'null, null,', 'null,'
        # Insert a new line after it
        $newLine = $content[$i] -replace 'null,', 'null,'
        $newLine = $newLine -replace 'null,', '                    null,'
        $content = $content[0..$i] + $newLine + $content[($i+1)..($content.Length-1)]
        break
    }
}

Set-Content $passwordServicePath $content

Write-Host "Fixed line 477 in PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "Running build..." -ForegroundColor Green
dotnet build
