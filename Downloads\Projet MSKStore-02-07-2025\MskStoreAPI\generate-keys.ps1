# MSKStore Security Key Generator
Write-Host "=== MSKStore Security Key Generator ===" -ForegroundColor Green
Write-Host ""

# Generate AES-256 key (32 bytes)
$keyBytes = New-Object byte[] 32
$ivBytes = New-Object byte[] 16
$jwtKeyBytes = New-Object byte[] 64

# Use cryptographically secure random number generator
$rng = [System.Security.Cryptography.RandomNumberGenerator]::Create()
$rng.GetBytes($keyBytes)
$rng.GetBytes($ivBytes)
$rng.GetBytes($jwtKeyBytes)

$key = [Convert]::ToBase64String($keyBytes)
$iv = [Convert]::ToBase64String($ivBytes)
$jwtKey = [Convert]::ToBase64String($jwtKeyBytes)

Write-Host "SECURE ENCRYPTION KEYS GENERATED:" -ForegroundColor Yellow
Write-Host "IMPORTANT: Store these keys securely and never commit them to source control!" -ForegroundColor Red
Write-Host ""
Write-Host "EncryptionKey: $key" -ForegroundColor Cyan
Write-Host "EncryptionIV: $iv" -ForegroundColor Cyan
Write-Host ""
Write-Host "JWT Secret Key: $jwtKey" -ForegroundColor Cyan
Write-Host ""
Write-Host "Add these to your appsettings.json under Security section:" -ForegroundColor Green
Write-Host "`"EncryptionKey`": `"$key`"," -ForegroundColor White
Write-Host "`"EncryptionIV`": `"$iv`"," -ForegroundColor White
Write-Host ""
Write-Host "=== KEYS GENERATED SUCCESSFULLY ===" -ForegroundColor Green

$rng.Dispose()
