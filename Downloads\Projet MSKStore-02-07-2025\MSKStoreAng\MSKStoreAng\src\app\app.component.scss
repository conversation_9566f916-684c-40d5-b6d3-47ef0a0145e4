// Global theme transition styles
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

// Ensure smooth transitions for all theme changes
body {
  transition: background-color 0.3s ease, color 0.3s ease !important;
}

// Card transitions
.card {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

// Button transitions
.btn {
  transition: all 0.3s ease !important;
}

// Input transitions
.form-control {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

// Navbar transitions
.navbar {
  transition: background-color 0.3s ease, border-color 0.3s ease !important;
}

// Sidebar transitions
.main-menu {
  transition: background-color 0.3s ease !important;
}

// Content area transitions
.content-wrapper {
  transition: background-color 0.3s ease !important;
}