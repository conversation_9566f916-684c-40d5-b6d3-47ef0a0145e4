using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MskStoreAPI.Data;

namespace MskStoreAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DatabaseController : ControllerBase
    {
        private readonly DataContext _context;

        public DatabaseController(DataContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Create security tables in the database
        /// </summary>
        [HttpPost("create-security-tables")]
        public async Task<IActionResult> CreateSecurityTables()
        {
            try
            {
                // Execute raw SQL to create security tables
                var sql = @"
                    -- Create SecurityAuditLogs table
                    CREATE TABLE IF NOT EXISTS ""SecurityAuditLogs"" (
                        ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_SecurityAuditLogs"" PRIMARY KEY AUTOINCREMENT,
                        ""Timestamp"" TEXT NOT NULL,
                        ""EventType"" TEXT NOT NULL,
                        ""Description"" TEXT NOT NULL,
                        ""UserId"" INTEGER NULL,
                        ""IpAddress"" TEXT NULL,
                        ""UserAgent"" TEXT NULL,
                        ""Severity"" TEXT NOT NULL,
                        ""Success"" INTEGER NOT NULL
                    );

                    -- Create LoginAttempts table
                    CREATE TABLE IF NOT EXISTS ""LoginAttempts"" (
                        ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_LoginAttempts"" PRIMARY KEY AUTOINCREMENT,
                        ""Email"" TEXT NOT NULL,
                        ""IpAddress"" TEXT NOT NULL,
                        ""AttemptTime"" TEXT NOT NULL,
                        ""Success"" INTEGER NOT NULL,
                        ""FailureReason"" TEXT NULL
                    );

                    -- Create AccountLockouts table
                    CREATE TABLE IF NOT EXISTS ""AccountLockouts"" (
                        ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_AccountLockouts"" PRIMARY KEY AUTOINCREMENT,
                        ""Email"" TEXT NOT NULL,
                        ""LockoutTime"" TEXT NOT NULL,
                        ""UnlockTime"" TEXT NULL,
                        ""FailedAttempts"" INTEGER NOT NULL,
                        ""IsActive"" INTEGER NOT NULL
                    );

                    -- Create indexes for better performance
                    CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_Timestamp"" ON ""SecurityAuditLogs"" (""Timestamp"");
                    CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_EventType"" ON ""SecurityAuditLogs"" (""EventType"");
                    CREATE INDEX IF NOT EXISTS ""IX_SecurityAuditLogs_UserId"" ON ""SecurityAuditLogs"" (""UserId"");
                    CREATE INDEX IF NOT EXISTS ""IX_LoginAttempts_Email"" ON ""LoginAttempts"" (""Email"");
                    CREATE INDEX IF NOT EXISTS ""IX_LoginAttempts_AttemptTime"" ON ""LoginAttempts"" (""AttemptTime"");
                    CREATE INDEX IF NOT EXISTS ""IX_AccountLockouts_Email"" ON ""AccountLockouts"" (""Email"");
                    CREATE INDEX IF NOT EXISTS ""IX_AccountLockouts_IsActive"" ON ""AccountLockouts"" (""IsActive"");
                ";

                await _context.Database.ExecuteSqlRawAsync(sql);

                return Ok(new { message = "Security tables created successfully!" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Check if security tables exist
        /// </summary>
        [HttpGet("check-security-tables")]
        public async Task<IActionResult> CheckSecurityTables()
        {
            try
            {
                var sql = @"
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND (
                        name = 'SecurityAuditLogs' OR 
                        name = 'LoginAttempts' OR 
                        name = 'AccountLockouts'
                    )
                ";

                var connection = _context.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = sql;
                
                var tables = new List<string>();
                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    tables.Add(reader.GetString(0));
                }

                return Ok(new { 
                    tablesExist = tables.Count == 3,
                    existingTables = tables,
                    message = tables.Count == 3 ? "All security tables exist" : "Some security tables are missing"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
