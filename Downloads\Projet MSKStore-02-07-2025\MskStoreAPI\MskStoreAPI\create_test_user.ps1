try {
    $body = @{
        name = "Test"
        lastName = "User"
        email = "<EMAIL>"
        password = "SecurePass123!"
        phone = "1234567890"
        role = "Admin"
    } | ConvertTo-Json

    Write-Host "Creating test user..."
    Write-Host "Request body: $body"
    
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $body
    
    Write-Host "SUCCESS: User created!"
    Write-Host "Response: $($response | ConvertTo-Json)"
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "Status Code: $statusCode"
        
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody"
        } catch {
            Write-Host "Could not read response body"
        }
    }
}
