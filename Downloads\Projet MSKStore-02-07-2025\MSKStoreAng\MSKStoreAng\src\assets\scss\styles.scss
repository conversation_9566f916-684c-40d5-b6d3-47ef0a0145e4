/*================================================================================
	Item Name: MskStore - Vuejs, HTML & Laravel Admin Dashboard Template
	Author: Msk
	Author URL: http://www.themeforest.net/user/Msk
================================================================================

NOTE:
------
PLACE HERE YOUR OWN SCSS CODES AND IF NEEDED, OVERRIDE THE STYLES FROM THE OTHER STYLESHEETS.
WE WILL RELEASE FUTURE UPDATES SO IN ORDER TO NOT OVERWRITE YOUR STYLES IT'S BETTER LIKE THIS.  */

// ===================================================================
// Modern Design System for MSKStore
// ===================================================================
@import 'modern-design-system';
@import 'modern-components';
@import 'modern-utilities';

// ===================================================================
// Global Modern Overrides
// ===================================================================

// Import Inter font for modern typography
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

// Apply modern font family globally
body {
  font-family: var(--font-family-sans);
  background-color: var(--bg-body);
  color: var(--text-primary);
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Modern scrollbar
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-full);

  &:hover {
    background: var(--color-gray-400);
  }
}

// Focus styles
*:focus {
  outline: none;
}

// Selection styles
::selection {
  background: var(--color-primary-100);
  color: var(--color-primary-800);
}

// Modern link styles
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);

  &:hover {
    color: var(--color-primary-600);
    text-decoration: none;
  }
}

// Override Bootstrap components with modern styles
.card {
  @extend .modern-card;
}

.btn {
  @extend .btn-modern;
}

.form-control {
  @include modern-input();
}

.table {
  @extend .modern-table;
}

.alert {
  @extend .modern-alert;
}

// Modern page layout
.content-wrapper {
  background: var(--bg-body);
  min-height: 100vh;
}

.content-header {
  background: var(--bg-surface);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);

  .content-header-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
  }

  .breadcrumb {
    background: none;
    padding: 0;
    margin: var(--spacing-2) 0 0;

    .breadcrumb-item {
      font-size: var(--text-sm);
      color: var(--text-muted);

      &.active {
        color: var(--text-secondary);
      }

      a {
        color: var(--color-primary);

        &:hover {
          color: var(--color-primary-600);
        }
      }
    }
  }
}
