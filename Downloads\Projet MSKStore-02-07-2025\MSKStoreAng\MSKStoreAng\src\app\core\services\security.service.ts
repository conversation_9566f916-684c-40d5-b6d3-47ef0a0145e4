import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, interval } from 'rxjs';
import { Router } from '@angular/router';

export interface SecurityEvent {
  type: string;
  description: string;
  timestamp: Date;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  userAgent?: string;
  ipAddress?: string;
}

export interface TokenInfo {
  token: string;
  expiresAt: Date;
  issuedAt: Date;
  sessionId: string;
}

@Injectable({
  providedIn: 'root'
})
export class SecurityService {
  private readonly TOKEN_KEY = 'msk_auth_token';
  private readonly SESSION_KEY = 'msk_session_info';
  private readonly SECURITY_EVENTS_KEY = 'msk_security_events';
  
  private securityEvents$ = new BehaviorSubject<SecurityEvent[]>([]);
  private sessionTimeout$ = new BehaviorSubject<number>(0);
  private isSessionActive$ = new BehaviorSubject<boolean>(false);

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    this.initializeSecurityMonitoring();
    this.startSessionMonitoring();
  }

  /**
   * Initialize security monitoring
   */
  private initializeSecurityMonitoring(): void {
    // Monitor for suspicious activities
    this.detectDevToolsUsage();
    this.detectConsoleUsage();
    this.detectRightClickDisabling();
    this.monitorNetworkRequests();
    
    // Load existing security events
    this.loadSecurityEvents();
  }

  /**
   * Secure token storage with encryption
   */
  setSecureToken(token: string, expiresIn: number): void {
    try {
      const tokenInfo: TokenInfo = {
        token: this.encryptData(token),
        expiresAt: new Date(Date.now() + expiresIn * 1000),
        issuedAt: new Date(),
        sessionId: this.generateSessionId()
      };

      // Store in sessionStorage (more secure than localStorage for tokens)
      sessionStorage.setItem(this.TOKEN_KEY, this.encryptData(JSON.stringify(tokenInfo)));
      
      // Set session as active
      this.isSessionActive$.next(true);
      
      // Start session timeout countdown
      this.startSessionTimeout(expiresIn);
      
      this.logSecurityEvent({
        type: 'TOKEN_STORED',
        description: 'Authentication token stored securely',
        timestamp: new Date(),
        severity: 'LOW'
      });
    } catch (error) {
      this.logSecurityEvent({
        type: 'TOKEN_STORAGE_ERROR',
        description: `Error storing token: ${error}`,
        timestamp: new Date(),
        severity: 'HIGH'
      });
    }
  }

  /**
   * Get secure token with validation
   */
  getSecureToken(): string | null {
    try {
      const encryptedTokenInfo = sessionStorage.getItem(this.TOKEN_KEY);
      if (!encryptedTokenInfo) {
        return null;
      }

      const tokenInfoStr = this.decryptData(encryptedTokenInfo);
      const tokenInfo: TokenInfo = JSON.parse(tokenInfoStr);

      // Check if token is expired
      if (new Date() > tokenInfo.expiresAt) {
        this.clearSecureToken();
        this.logSecurityEvent({
          type: 'TOKEN_EXPIRED',
          description: 'Authentication token expired',
          timestamp: new Date(),
          severity: 'MEDIUM'
        });
        return null;
      }

      return this.decryptData(tokenInfo.token);
    } catch (error) {
      this.logSecurityEvent({
        type: 'TOKEN_RETRIEVAL_ERROR',
        description: `Error retrieving token: ${error}`,
        timestamp: new Date(),
        severity: 'HIGH'
      });
      this.clearSecureToken();
      return null;
    }
  }

  /**
   * Clear secure token
   */
  clearSecureToken(): void {
    sessionStorage.removeItem(this.TOKEN_KEY);
    sessionStorage.removeItem(this.SESSION_KEY);
    this.isSessionActive$.next(false);
    
    this.logSecurityEvent({
      type: 'TOKEN_CLEARED',
      description: 'Authentication token cleared',
      timestamp: new Date(),
      severity: 'LOW'
    });
  }

  /**
   * Simple encryption for client-side data (not cryptographically secure)
   */
  private encryptData(data: string): string {
    // Simple XOR encryption for basic obfuscation
    const key = 'MSKStore2024SecureKey';
    let encrypted = '';
    for (let i = 0; i < data.length; i++) {
      encrypted += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(encrypted);
  }

  /**
   * Simple decryption for client-side data
   */
  private decryptData(encryptedData: string): string {
    const key = 'MSKStore2024SecureKey';
    const data = atob(encryptedData);
    let decrypted = '';
    for (let i = 0; i < data.length; i++) {
      decrypted += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return decrypted;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return 'sess_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  /**
   * Start session timeout monitoring
   */
  private startSessionTimeout(expiresIn: number): void {
    let timeRemaining = expiresIn;
    
    const countdown = interval(1000).subscribe(() => {
      timeRemaining--;
      this.sessionTimeout$.next(timeRemaining);
      
      // Warning at 5 minutes remaining
      if (timeRemaining === 300) {
        this.logSecurityEvent({
          type: 'SESSION_WARNING',
          description: 'Session expires in 5 minutes',
          timestamp: new Date(),
          severity: 'MEDIUM'
        });
      }
      
      // Auto-logout when expired
      if (timeRemaining <= 0) {
        countdown.unsubscribe();
        this.handleSessionExpiry();
      }
    });
  }

  /**
   * Handle session expiry
   */
  private handleSessionExpiry(): void {
    this.clearSecureToken();
    this.logSecurityEvent({
      type: 'SESSION_EXPIRED',
      description: 'User session expired automatically',
      timestamp: new Date(),
      severity: 'MEDIUM'
    });
    
    this.router.navigate(['/auth/login'], {
      queryParams: { reason: 'session_expired' }
    });
  }

  /**
   * Start session monitoring
   */
  private startSessionMonitoring(): void {
    // Monitor for multiple tabs/windows
    window.addEventListener('storage', (event) => {
      if (event.key === this.TOKEN_KEY && event.newValue === null) {
        // Token was cleared in another tab
        this.handleSessionExpiry();
      }
    });

    // Monitor for page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.logSecurityEvent({
          type: 'PAGE_HIDDEN',
          description: 'User switched away from application',
          timestamp: new Date(),
          severity: 'LOW'
        });
      }
    });
  }

  /**
   * Detect developer tools usage
   */
  private detectDevToolsUsage(): void {
    let devtools = false;
    
    setInterval(() => {
      const before = performance.now();
      debugger;
      const after = performance.now();
      
      if (after - before > 100 && !devtools) {
        devtools = true;
        this.logSecurityEvent({
          type: 'DEVTOOLS_DETECTED',
          description: 'Developer tools usage detected',
          timestamp: new Date(),
          severity: 'HIGH'
        });
      }
    }, 1000);
  }

  /**
   * Detect console usage
   */
  private detectConsoleUsage(): void {
    const originalLog = console.log;
    console.log = (...args) => {
      this.logSecurityEvent({
        type: 'CONSOLE_USAGE',
        description: 'Console usage detected',
        timestamp: new Date(),
        severity: 'MEDIUM'
      });
      originalLog.apply(console, args);
    };
  }

  /**
   * Disable right-click context menu
   */
  private detectRightClickDisabling(): void {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.logSecurityEvent({
        type: 'RIGHT_CLICK_BLOCKED',
        description: 'Right-click context menu blocked',
        timestamp: new Date(),
        severity: 'LOW'
      });
    });
  }

  /**
   * Monitor network requests
   */
  private monitorNetworkRequests(): void {
    // This would be implemented with HTTP interceptors
    // Placeholder for network monitoring logic
  }

  /**
   * Log security event
   */
  logSecurityEvent(event: SecurityEvent): void {
    const events = this.securityEvents$.value;
    events.unshift(event);
    
    // Keep only last 100 events
    if (events.length > 100) {
      events.splice(100);
    }
    
    this.securityEvents$.next(events);
    this.saveSecurityEvents(events);
    
    // Send critical events to server
    if (event.severity === 'CRITICAL' || event.severity === 'HIGH') {
      this.sendSecurityEventToServer(event);
    }
  }

  /**
   * Save security events to local storage
   */
  private saveSecurityEvents(events: SecurityEvent[]): void {
    try {
      localStorage.setItem(this.SECURITY_EVENTS_KEY, JSON.stringify(events));
    } catch (error) {
      console.error('Error saving security events:', error);
    }
  }

  /**
   * Load security events from local storage
   */
  private loadSecurityEvents(): void {
    try {
      const eventsStr = localStorage.getItem(this.SECURITY_EVENTS_KEY);
      if (eventsStr) {
        const events = JSON.parse(eventsStr);
        this.securityEvents$.next(events);
      }
    } catch (error) {
      console.error('Error loading security events:', error);
    }
  }

  /**
   * Send security event to server
   */
  private sendSecurityEventToServer(event: SecurityEvent): void {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    this.http.post('/api/security/events', event, { headers }).subscribe({
      error: (error) => {
        console.error('Error sending security event to server:', error);
      }
    });
  }

  // Observables for components to subscribe to
  getSecurityEvents(): Observable<SecurityEvent[]> {
    return this.securityEvents$.asObservable();
  }

  getSessionTimeout(): Observable<number> {
    return this.sessionTimeout$.asObservable();
  }

  getSessionStatus(): Observable<boolean> {
    return this.isSessionActive$.asObservable();
  }
}
