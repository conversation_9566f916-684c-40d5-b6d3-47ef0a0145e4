try {
    Write-Host "Testing POST /api/Users endpoint with fixed middleware..."
    
    $body = @{
        name = "Test"
        lastName = "User"
        email = "<EMAIL>"
        password = "Test123!"
        phone = "1234567890"
        role = "Admin"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $body
    
    Write-Host "SUCCESS! User created successfully:"
    $response | ConvertTo-Json -Depth 3
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
