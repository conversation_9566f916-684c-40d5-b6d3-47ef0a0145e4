{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\Projet MSKStore-02-07-2025\\MskStoreAPI\\MskStoreAPI\\MskStoreAPI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\Projet MSKStore-02-07-2025\\MskStoreAPI\\MskStoreAPI\\MskStoreAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\Projet MSKStore-02-07-2025\\MskStoreAPI\\MskStoreAPI\\MskStoreAPI.csproj", "projectName": "MskStoreAPI", "projectPath": "C:\\Users\\<USER>\\Downloads\\Projet MSKStore-02-07-2025\\MskStoreAPI\\MskStoreAPI\\MskStoreAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\Projet MSKStore-02-07-2025\\MskStoreAPI\\MskStoreAPI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files\\dotnet\\sdk\\7.0.317\\Sdks\\Microsoft.NET.Sdk.Web\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"BCrypt.Net-Core": {"target": "Package", "version": "[1.6.0, )"}, "Microsoft.AspNet.WebApi.Core": {"target": "Package", "version": "[5.2.9, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[7.0.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.3, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[7.0.3, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.3, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[7.0.4, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[7.0.6, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[6.27.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.317\\RuntimeIdentifierGraph.json"}}}}}