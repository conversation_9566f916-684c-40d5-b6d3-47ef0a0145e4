using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Logging;

namespace MskStoreAPI.Middleware
{
    /// <summary>
    /// Middleware to enforce HTTPS and redirect HTTP requests
    /// </summary>
    public class HttpsRedirectionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<HttpsRedirectionMiddleware> _logger;
        private readonly bool _isDevelopment;

        public HttpsRedirectionMiddleware(RequestDelegate next, ILogger<HttpsRedirectionMiddleware> logger, IWebHostEnvironment environment)
        {
            _next = next;
            _logger = logger;
            _isDevelopment = environment.IsDevelopment();
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Skip HTTPS enforcement in development
            if (_isDevelopment)
            {
                await _next(context);
                return;
            }

            // Check if request is already HTTPS
            if (context.Request.IsHttps)
            {
                await _next(context);
                return;
            }

            // Check for forwarded protocol headers (for load balancers/proxies)
            var forwardedProto = context.Request.Headers["X-Forwarded-Proto"].FirstOrDefault();
            if (string.Equals(forwardedProto, "https", StringComparison.OrdinalIgnoreCase))
            {
                await _next(context);
                return;
            }

            // Skip HTTPS redirect for health checks and specific endpoints
            if (ShouldSkipHttpsRedirect(context.Request.Path))
            {
                await _next(context);
                return;
            }

            // Redirect to HTTPS
            var httpsUrl = $"https://{context.Request.Host}{context.Request.PathBase}{context.Request.Path}{context.Request.QueryString}";
            
            _logger.LogInformation("Redirecting HTTP request to HTTPS: {HttpUrl} -> {HttpsUrl}", 
                context.Request.GetDisplayUrl(), httpsUrl);

            context.Response.StatusCode = 301; // Permanent redirect
            context.Response.Headers.Location = httpsUrl;
            
            // Add security headers for the redirect
            context.Response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
            
            await context.Response.WriteAsync($"Redirecting to HTTPS: {httpsUrl}");
        }

        private bool ShouldSkipHttpsRedirect(PathString path)
        {
            var skipPaths = new[] { "/health", "/metrics" };
            return skipPaths.Any(skipPath => path.StartsWithSegments(skipPath));
        }
    }
}
