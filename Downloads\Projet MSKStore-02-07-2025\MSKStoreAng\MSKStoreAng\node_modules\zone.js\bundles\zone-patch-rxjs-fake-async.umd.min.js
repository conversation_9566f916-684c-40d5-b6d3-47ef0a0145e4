"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e=function(){function e(e){void 0===e&&(e=null),this.name="ProxyZone",this._delegateSpec=null,this.properties={ProxyZoneSpec:this},this.propertyKeys=null,this.lastTaskState=null,this.isNeedToTriggerHasTask=!1,this.tasks=[],this.defaultSpecDelegate=e,this.setDelegate(e)}return e.get=function(){return Zone.current.get("ProxyZoneSpec")},e.isLoaded=function(){return e.get()instanceof e},e.assertPresent=function(){var t=e.get();if(void 0===t)throw new Error("Expected to be running in 'ProxyZone', but it was not found.");return t},e.prototype.setDelegate=function(e){var t=this,s=this._delegateSpec!==e;this._delegateSpec=e,this.propertyKeys&&this.propertyKeys.forEach((function(e){return delete t.properties[e]})),this.propertyKeys=null,e&&e.properties&&(this.propertyKeys=Object.keys(e.properties),this.propertyKeys.forEach((function(s){return t.properties[s]=e.properties[s]}))),s&&this.lastTaskState&&(this.lastTaskState.macroTask||this.lastTaskState.microTask)&&(this.isNeedToTriggerHasTask=!0)},e.prototype.getDelegate=function(){return this._delegateSpec},e.prototype.resetDelegate=function(){this.getDelegate(),this.setDelegate(this.defaultSpecDelegate)},e.prototype.tryTriggerHasTask=function(e,t,s){this.isNeedToTriggerHasTask&&this.lastTaskState&&(this.isNeedToTriggerHasTask=!1,this.onHasTask(e,t,s,this.lastTaskState))},e.prototype.removeFromTasks=function(e){if(this.tasks)for(var t=0;t<this.tasks.length;t++)if(this.tasks[t]===e)return void this.tasks.splice(t,1)},e.prototype.getAndClearPendingTasksInfo=function(){if(0===this.tasks.length)return"";var e="--Pending async tasks are: ["+this.tasks.map((function(e){var t=e.data&&Object.keys(e.data).map((function(t){return t+":"+e.data[t]})).join(",");return"type: ".concat(e.type,", source: ").concat(e.source,", args: {").concat(t,"}")}))+"]";return this.tasks=[],e},e.prototype.onFork=function(e,t,s,n){return this._delegateSpec&&this._delegateSpec.onFork?this._delegateSpec.onFork(e,t,s,n):e.fork(s,n)},e.prototype.onIntercept=function(e,t,s,n,a){return this._delegateSpec&&this._delegateSpec.onIntercept?this._delegateSpec.onIntercept(e,t,s,n,a):e.intercept(s,n,a)},e.prototype.onInvoke=function(e,t,s,n,a,r,o){return this.tryTriggerHasTask(e,t,s),this._delegateSpec&&this._delegateSpec.onInvoke?this._delegateSpec.onInvoke(e,t,s,n,a,r,o):e.invoke(s,n,a,r,o)},e.prototype.onHandleError=function(e,t,s,n){return this._delegateSpec&&this._delegateSpec.onHandleError?this._delegateSpec.onHandleError(e,t,s,n):e.handleError(s,n)},e.prototype.onScheduleTask=function(e,t,s,n){return"eventTask"!==n.type&&this.tasks.push(n),this._delegateSpec&&this._delegateSpec.onScheduleTask?this._delegateSpec.onScheduleTask(e,t,s,n):e.scheduleTask(s,n)},e.prototype.onInvokeTask=function(e,t,s,n,a,r){return"eventTask"!==n.type&&this.removeFromTasks(n),this.tryTriggerHasTask(e,t,s),this._delegateSpec&&this._delegateSpec.onInvokeTask?this._delegateSpec.onInvokeTask(e,t,s,n,a,r):e.invokeTask(s,n,a,r)},e.prototype.onCancelTask=function(e,t,s,n){return"eventTask"!==n.type&&this.removeFromTasks(n),this.tryTriggerHasTask(e,t,s),this._delegateSpec&&this._delegateSpec.onCancelTask?this._delegateSpec.onCancelTask(e,t,s,n):e.cancelTask(s,n)},e.prototype.onHasTask=function(e,t,s,n){this.lastTaskState=n,this._delegateSpec&&this._delegateSpec.onHasTask?this._delegateSpec.onHasTask(e,t,s,n):e.hasTask(s,n)},e}();!function t(s){s.ProxyZoneSpec=e}(Zone)}));