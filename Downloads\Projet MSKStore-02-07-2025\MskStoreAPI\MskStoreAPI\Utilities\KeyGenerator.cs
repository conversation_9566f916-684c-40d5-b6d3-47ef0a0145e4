using MskStoreAPI.Services;
using System;

namespace MskStoreAPI.Utilities
{
    /// <summary>
    /// Utility class for generating secure encryption keys
    /// </summary>
    public static class KeyGenerator
    {
        /// <summary>
        /// Generates and displays secure encryption keys for production use
        /// </summary>
        public static void GenerateAndDisplayKeys()
        {
            var (key, iv) = EncryptionService.GenerateProductionKeys();
            
            Console.WriteLine("=== SECURE ENCRYPTION KEYS ===");
            Console.WriteLine("IMPORTANT: Store these keys securely and never commit them to source control!");
            Console.WriteLine();
            Console.WriteLine($"EncryptionKey: {key}");
            Console.WriteLine($"EncryptionIV: {iv}");
            Console.WriteLine();
            Console.WriteLine("Add these to your appsettings.json under Security section:");
            Console.WriteLine($"\"EncryptionKey\": \"{key}\",");
            Console.WriteLine($"\"EncryptionIV\": \"{iv}\",");
            Console.WriteLine();
            Console.WriteLine("=== END KEYS ===");
        }
    }
}
