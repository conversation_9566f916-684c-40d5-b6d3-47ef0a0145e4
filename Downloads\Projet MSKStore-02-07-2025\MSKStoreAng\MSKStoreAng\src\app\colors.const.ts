export const colors = {
  solid: {
    primary: '#6366F1', // Modern indigo
    secondary: '#64748B', // Slate gray
    success: '#10B981', // Emerald green
    info: '#06B6D4', // Cyan
    warning: '#F59E0B', // Amber
    danger: '#EF4444', // Red
    dark: '#1E293B', // Dark slate
    black: '#000',
    white: '#fff',
    body: '#F8FAFC', // Light gray background
    // New modern colors
    accent: '#8B5CF6', // Purple accent
    neutral: '#F1F5F9', // Light neutral
    'neutral-dark': '#475569', // Dark neutral
    'surface': '#FFFFFF', // Card/surface background
    'surface-dark': '#F8FAFC', // Darker surface
    'border': '#E2E8F0', // Border color
    'border-light': '#F1F5F9', // Light border
    'text-primary': '#0F172A', // Primary text
    'text-secondary': '#64748B', // Secondary text
    'text-muted': '#94A3B8' // Muted text
  },
  light: {
    primary: '#6366F11a',
    secondary: '#64748B1a',
    success: '#10B9811a',
    info: '#06B6D41a',
    warning: '#F59E0B1a',
    danger: '#EF44441a',
    dark: '#1E293B1a',
    accent: '#8B5CF61a',
    neutral: '#F1F5F91a'
  },
  gradients: {
    primary: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
    success: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
    warning: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
    danger: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',
    info: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)',
    dark: 'linear-gradient(135deg, #1E293B 0%, #0F172A 100%)'
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: '0 0 #0000'
  }
};
