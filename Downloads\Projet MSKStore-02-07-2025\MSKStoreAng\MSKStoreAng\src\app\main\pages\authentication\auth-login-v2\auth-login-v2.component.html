<div class="auth-wrapper auth-v2">
  <div class="auth-inner row m-0">
    <!-- Brand logo--><a class="brand-logo" href="javascript:void(0);">
      <img src="{{ coreConfig.app.appLogoImage }}" alt="brand-logo" height="28" />
      <h2 class="brand-text text-primary ml-1">MskStore</h2></a
    >
    <!-- /Brand logo-->
    <!-- Left Text-->
    <div class="d-none d-lg-flex col-lg-8 align-items-center p-5">
      <div class="w-100 d-lg-flex align-items-center justify-content-center px-5">
        <img
          class="img-fluid"
          [src]="
            coreConfig.layout.skin === 'dark'
              ? 'assets/images/pages/login-v2-dark.svg'
              : 'assets/images/pages/login-v2.svg'
          "
          alt="Login V2"
        />
      </div>
    </div>
    <!-- /Left Text-->
    <!-- Login-->
    <div class="d-flex col-lg-4 align-items-center auth-bg px-2 p-lg-5">
      <div class="col-12 col-sm-8 col-md-6 col-lg-12 px-xl-2 mx-auto">
        <h2 class="card-title font-weight-bold mb-1">Welcome to MskStore! 👋</h2>

        <!-- Error Alert -->
        <ngb-alert [type]="'danger'" [dismissible]="false" *ngIf="error">
          <div class="alert-body">
            <i class="feather icon-alert-triangle mr-1"></i>
            <span>{{ error }}</span>
          </div>
        </ngb-alert>

        <!-- Account Lockout Warning -->
        <ngb-alert [type]="'warning'" [dismissible]="false" *ngIf="loginAttempts > 2 && !isAccountLocked">
          <div class="alert-body">
            <i class="feather icon-alert-circle mr-1"></i>
            <span>Attention: {{ maxLoginAttempts - loginAttempts }} tentative(s) restante(s) avant verrouillage du compte.</span>
          </div>
        </ngb-alert>

        <form class="auth-login-form mt-2" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <div class="form-group">
            <label class="form-label" for="login-email">
              Email <span class="text-danger">*</span>
            </label>
            <input
              type="email"
              formControlName="email"
              class="form-control"
              [ngClass]="{
                'is-invalid': hasFieldErrors('email'),
                'is-valid': f.email.valid && f.email.touched && emailValidation.isValid
              }"
              placeholder="<EMAIL>"
              aria-describedby="login-email"
              autofocus=""
              tabindex="1"
              (blur)="onEmailChange()"
              (input)="onEmailChange()"
            />

            <!-- Email Validation Feedback -->
            <div *ngIf="hasFieldErrors('email')" class="invalid-feedback d-block">
              <div *ngFor="let error of getEmailErrors()" class="d-flex align-items-center mb-1">
                <i class="feather icon-x-circle mr-1" style="font-size: 14px;"></i>
                <small>{{ error }}</small>
              </div>
            </div>

            <!-- Email Success Feedback -->
            <div *ngIf="f.email.valid && f.email.touched && emailValidation.isValid" class="valid-feedback d-block">
              <div class="d-flex align-items-center">
                <i class="feather icon-check-circle mr-1 text-success" style="font-size: 14px;"></i>
                <small class="text-success">Email valide</small>
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="d-flex justify-content-between align-items-center">
              <label for="login-password">
                Mot de passe <span class="text-danger">*</span>
              </label>
              <small class="text-muted">
                <i class="feather icon-shield mr-1"></i>
                Sécurisé
              </small>
            </div>

            <div class="input-group input-group-merge form-password-toggle">
              <input
                [type]="passwordTextType ? 'text' : 'password'"
                formControlName="password"
                class="form-control form-control-merge"
                [ngClass]="{
                  'is-invalid': hasFieldErrors('password'),
                  'is-valid': f.password.valid && f.password.touched && passwordValidation.isValid
                }"
                placeholder="Entrez votre mot de passe"
                aria-describedby="login-password"
                tabindex="2"
                (blur)="onPasswordChange()"
                (input)="onPasswordChange()"
              />
              <div class="input-group-append">
                <span class="input-group-text cursor-pointer" (click)="togglePasswordTextType()">
                  <i
                    class="feather font-small-4"
                    [ngClass]="{
                      'icon-eye-off': passwordTextType,
                      'icon-eye': !passwordTextType
                    }"
                    [title]="passwordTextType ? 'Masquer le mot de passe' : 'Afficher le mot de passe'"
                  ></i>
                </span>
              </div>
            </div>

            <!-- Password Validation Feedback -->
            <div *ngIf="hasFieldErrors('password')" class="invalid-feedback d-block">
              <div *ngFor="let error of getPasswordErrors()" class="d-flex align-items-center mb-1">
                <i class="feather icon-x-circle mr-1" style="font-size: 14px;"></i>
                <small>{{ error }}</small>
              </div>
            </div>

            <!-- Password Success Feedback -->
            <div *ngIf="f.password.valid && f.password.touched && passwordValidation.isValid" class="valid-feedback d-block">
              <div class="d-flex align-items-center">
                <i class="feather icon-check-circle mr-1 text-success" style="font-size: 14px;"></i>
                <small class="text-success">Mot de passe valide</small>
              </div>
            </div>

            <!-- Password Warnings -->
            <div *ngIf="passwordValidation.warnings.length > 0 && showValidationFeedback" class="mt-2">
              <div *ngFor="let warning of passwordValidation.warnings" class="d-flex align-items-center mb-1">
                <i class="feather icon-alert-triangle mr-1 text-warning" style="font-size: 14px;"></i>
                <small class="text-warning">{{ warning }}</small>
              </div>
            </div>
          </div>

          <!-- Security Notice -->
          <div class="mt-3 mb-3" *ngIf="showValidationFeedback">
            <div class="alert alert-info py-2">
              <div class="d-flex align-items-center">
                <i class="feather icon-info mr-2"></i>
                <small>
                  Vos données sont protégées par un chiffrement AES-256 et des mesures de sécurité avancées.
                </small>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <button
            [disabled]="loading || isAccountLocked || (showValidationFeedback && (!emailValidation.isValid || !passwordValidation.isValid))"
            class="btn btn-primary btn-block"
            tabindex="4"
            rippleEffect
            type="submit">
            <span *ngIf="loading" class="spinner-border spinner-border-sm mr-1"></span>
            <i *ngIf="!loading" class="feather icon-log-in mr-1"></i>
            {{ loading ? 'Connexion en cours...' : 'Se connecter' }}
          </button>

          <!-- Login Attempts Counter -->
          <div class="text-center mt-2" *ngIf="loginAttempts > 0 && !isAccountLocked">
            <small class="text-muted">
              Tentatives: {{ loginAttempts }}/{{ maxLoginAttempts }}
            </small>
          </div>
        </form>
        
       
       
      </div>
    </div>
    <!-- /Login--> 
  </div>
</div>
