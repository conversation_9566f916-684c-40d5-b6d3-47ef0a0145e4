// ===================================================================
// Modern Component Styles for MSKStore
// ===================================================================

// ===================================================================
// Modern Cards
// ===================================================================
.modern-card {
  @include modern-card();
  
  &.card-elevated {
    box-shadow: var(--shadow-lg);
    
    &:hover {
      box-shadow: var(--shadow-xl);
      transform: translateY(-2px);
    }
  }
  
  &.card-gradient {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    border: none;
    
    .card-title, .card-text {
      color: var(--text-inverse);
    }
  }
  
  &.card-glass {
    @extend .modern-glass-effect;
  }
}

.modern-card-header {
  padding: var(--spacing-6) var(--spacing-6) var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-4);
  
  .card-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
  }
  
  .card-subtitle {
    font-size: var(--text-sm);
    color: var(--text-muted);
    margin: var(--spacing-1) 0 0;
  }
}

.modern-card-body {
  padding: var(--spacing-6);
}

.modern-card-footer {
  padding: var(--spacing-4) var(--spacing-6) var(--spacing-6);
  border-top: 1px solid var(--border-color);
  margin-top: var(--spacing-4);
}

// ===================================================================
// Modern Buttons
// ===================================================================
.btn-modern {
  @include modern-button();
  
  &.btn-primary {
    @include modern-button('primary');
  }
  
  &.btn-secondary {
    @include modern-button('secondary');
  }
  
  &.btn-outline {
    @include modern-button('outline');
  }
  
  &.btn-success {
    background: var(--color-success);
    color: var(--text-inverse);
    border-color: var(--color-success);
    
    &:hover:not(:disabled) {
      background: var(--color-success-600);
      border-color: var(--color-success-600);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
  
  &.btn-warning {
    background: var(--color-warning);
    color: var(--text-inverse);
    border-color: var(--color-warning);
    
    &:hover:not(:disabled) {
      background: var(--color-warning-600);
      border-color: var(--color-warning-600);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
  
  &.btn-danger {
    background: var(--color-danger);
    color: var(--text-inverse);
    border-color: var(--color-danger);
    
    &:hover:not(:disabled) {
      background: var(--color-danger-600);
      border-color: var(--color-danger-600);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
  
  &.btn-sm {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--text-xs);
  }
  
  &.btn-lg {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--text-base);
  }
  
  &.btn-icon {
    padding: var(--spacing-3);
    width: auto;
    height: auto;
    
    i, svg {
      width: 1rem;
      height: 1rem;
    }
  }
  
  &.btn-floating {
    @extend .modern-floating-action;
    width: 56px;
    height: 56px;
    padding: 0;
    
    i, svg {
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

// ===================================================================
// Modern Forms
// ===================================================================
.modern-form-group {
  margin-bottom: var(--spacing-6);
  
  label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-2);
    
    .required {
      color: var(--color-danger);
      margin-left: var(--spacing-1);
    }
  }
  
  .form-control {
    @include modern-input();
    
    &.is-invalid {
      border-color: var(--color-danger);
      
      &:focus {
        border-color: var(--color-danger);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
      }
    }
    
    &.is-valid {
      border-color: var(--color-success);
      
      &:focus {
        border-color: var(--color-success);
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
      }
    }
  }
  
  .form-select {
    @include modern-input();
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-3) center;
    background-repeat: no-repeat;
    background-size: 1rem;
    padding-right: var(--spacing-10);
  }
  
  .invalid-feedback {
    display: block;
    font-size: var(--text-xs);
    color: var(--color-danger);
    margin-top: var(--spacing-1);
  }
  
  .valid-feedback {
    display: block;
    font-size: var(--text-xs);
    color: var(--color-success);
    margin-top: var(--spacing-1);
  }
  
  .form-text {
    font-size: var(--text-xs);
    color: var(--text-muted);
    margin-top: var(--spacing-1);
  }
}

.modern-input-group {
  display: flex;
  align-items: stretch;
  
  .form-control {
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: var(--radius-md);
      border-bottom-left-radius: var(--radius-md);
    }
    
    &:last-child {
      border-top-right-radius: var(--radius-md);
      border-bottom-right-radius: var(--radius-md);
    }
    
    &:not(:first-child) {
      border-left: none;
    }
  }
  
  .input-group-text {
    display: flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    background: var(--color-gray-50);
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: var(--text-sm);
    
    &:first-child {
      border-top-left-radius: var(--radius-md);
      border-bottom-left-radius: var(--radius-md);
      border-right: none;
    }
    
    &:last-child {
      border-top-right-radius: var(--radius-md);
      border-bottom-right-radius: var(--radius-md);
      border-left: none;
    }
  }
}

// ===================================================================
// Modern Tables
// ===================================================================
.modern-table {
  @include modern-table();
  
  &.table-striped {
    tbody tr:nth-child(even) {
      background: var(--color-gray-50);
    }
  }
  
  &.table-hover {
    tbody tr:hover {
      background: var(--color-primary-50);
    }
  }
  
  &.table-sm {
    th, td {
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--text-xs);
    }
  }
  
  .table-actions {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    
    .btn {
      padding: var(--spacing-1) var(--spacing-2);
      font-size: var(--text-xs);
    }
  }
}

.modern-table-container {
  background: var(--bg-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  
  .table-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    
    .table-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }
    
    .table-subtitle {
      font-size: var(--text-sm);
      color: var(--text-muted);
      margin: var(--spacing-1) 0 0;
    }
  }
  
  .table-filters {
    padding: var(--spacing-4) var(--spacing-6);
    background: var(--color-gray-50);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-4);
    align-items: center;
    flex-wrap: wrap;
    
    .filter-group {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      
      label {
        font-size: var(--text-xs);
        font-weight: var(--font-medium);
        color: var(--text-secondary);
        margin: 0;
        white-space: nowrap;
      }
      
      .form-control, .form-select {
        min-width: 120px;
        font-size: var(--text-xs);
        padding: var(--spacing-2) var(--spacing-3);
      }
    }
  }
  
  .table-wrapper {
    overflow-x: auto;
  }
  
  .table-footer {
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: between;
    align-items: center;
    
    .pagination-info {
      font-size: var(--text-sm);
      color: var(--text-muted);
    }
  }
}

// ===================================================================
// Modern Alerts
// ===================================================================
.modern-alert {
  padding: var(--spacing-4) var(--spacing-6);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  
  .alert-icon {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
  }
  
  .alert-content {
    flex: 1;
    
    .alert-title {
      font-weight: var(--font-semibold);
      margin: 0 0 var(--spacing-1);
    }
    
    .alert-message {
      margin: 0;
      font-size: var(--text-sm);
    }
  }
  
  .alert-close {
    flex-shrink: 0;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity var(--transition-fast);
    
    &:hover {
      opacity: 1;
    }
  }
  
  &.alert-success {
    background: var(--color-success-50);
    border-color: var(--color-success-200);
    color: var(--color-success-800);
    
    .alert-icon {
      color: var(--color-success);
    }
  }
  
  &.alert-warning {
    background: var(--color-warning-50);
    border-color: var(--color-warning-200);
    color: var(--color-warning-800);
    
    .alert-icon {
      color: var(--color-warning);
    }
  }
  
  &.alert-danger {
    background: var(--color-danger-50);
    border-color: var(--color-danger-200);
    color: var(--color-danger-800);
    
    .alert-icon {
      color: var(--color-danger);
    }
  }
  
  &.alert-info {
    background: var(--color-info-50);
    border-color: var(--color-info-200);
    color: var(--color-info-800);
    
    .alert-icon {
      color: var(--color-info);
    }
  }
}

// ===================================================================
// Enhanced Global Form Styles
// ===================================================================

// Apply modern styling to all form controls globally
.form-control {
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--spacing-3) var(--spacing-4) !important;
  font-size: var(--text-sm) !important;
  line-height: 1.5 !important;
  color: var(--text-primary) !important;
  background-color: var(--bg-surface) !important;
  background-clip: padding-box !important;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast), background-color var(--transition-fast) !important;

  &:focus {
    border-color: var(--color-primary) !important;
    outline: 0 !important;
    box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1) !important;
    background-color: var(--bg-surface) !important;
  }

  &:hover:not(:focus):not(:disabled) {
    border-color: var(--color-gray-400) !important;
  }

  &::placeholder {
    color: var(--text-muted) !important;
    opacity: 1 !important;
  }

  &:disabled,
  &[readonly] {
    background-color: var(--color-gray-50) !important;
    border-color: var(--color-gray-200) !important;
    color: var(--text-muted) !important;
    opacity: 1 !important;
  }

  // Validation states
  &.is-invalid {
    border-color: var(--color-danger) !important;

    &:focus {
      border-color: var(--color-danger) !important;
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    }
  }

  &.error {
    border-color: var(--color-danger) !important;

    &:focus {
      border-color: var(--color-danger) !important;
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    }
  }

  &.is-valid {
    border-color: var(--color-success) !important;

    &:focus {
      border-color: var(--color-success) !important;
      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
    }
  }
}

// Modern select styling
select.form-control {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
  background-position: right var(--spacing-3) center !important;
  background-repeat: no-repeat !important;
  background-size: 1rem !important;
  padding-right: calc(var(--spacing-4) + 1.5rem) !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;

  &:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236366f1' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
  }
}

// Modern textarea styling
textarea.form-control {
  resize: vertical !important;
  min-height: calc(var(--spacing-3) * 2 + var(--spacing-4) * 2 + 1.5rem * 3) !important;
}

// Modern form labels
.form-label,
label {
  display: inline-block !important;
  font-size: var(--text-sm) !important;
  font-weight: var(--font-medium) !important;
  color: var(--text-primary) !important;
  margin-bottom: var(--spacing-2) !important;
}

// Modern form groups
.form-group {
  margin-bottom: var(--spacing-5) !important;

  &:last-child {
    margin-bottom: 0 !important;
  }
}

// Modern form rows
.form-row {
  display: flex !important;
  flex-wrap: wrap !important;
  margin-right: calc(var(--spacing-3) * -1) !important;
  margin-left: calc(var(--spacing-3) * -1) !important;

  > .col,
  > [class*="col-"] {
    padding-right: var(--spacing-3) !important;
    padding-left: var(--spacing-3) !important;
  }
}

// Modern validation feedback
.invalid-feedback {
  display: block !important;
  width: 100% !important;
  margin-top: var(--spacing-1) !important;
  font-size: var(--text-sm) !important;
  color: var(--color-danger) !important;
  font-weight: var(--font-medium) !important;
  animation: modern-fade-in 0.3s ease-out !important;
}

.valid-feedback {
  display: block !important;
  width: 100% !important;
  margin-top: var(--spacing-1) !important;
  font-size: var(--text-sm) !important;
  color: var(--color-success) !important;
  font-weight: var(--font-medium) !important;
  animation: modern-fade-in 0.3s ease-out !important;
}

// ===================================================================
// Modern Input Groups & Password Toggle
// ===================================================================

// Modern input groups
.input-group {
  position: relative !important;
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: stretch !important;
  width: 100% !important;

  .input-group-prepend,
  .input-group-append {
    display: flex !important;

    .input-group-text {
      display: flex !important;
      align-items: center !important;
      padding: var(--spacing-3) var(--spacing-4) !important;
      font-size: var(--text-sm) !important;
      font-weight: var(--font-normal) !important;
      line-height: 1.5 !important;
      color: var(--text-secondary) !important;
      text-align: center !important;
      white-space: nowrap !important;
      background-color: var(--color-gray-50) !important;
      border: 1px solid var(--border-color) !important;
      border-radius: var(--radius-md) !important;
      transition: all var(--transition-fast) !important;
      cursor: pointer !important;

      &:hover {
        background-color: var(--color-gray-100) !important;
        color: var(--color-primary) !important;
      }

      i, svg {
        width: 1rem !important;
        height: 1rem !important;
      }
    }
  }

  .input-group-prepend .input-group-text {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-right: 0 !important;
  }

  .input-group-append .input-group-text {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-left: 0 !important;
  }

  .form-control {
    position: relative !important;
    flex: 1 1 auto !important;
    width: 1% !important;
    min-width: 0 !important;

    &:not(:first-child) {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
    }

    &:not(:last-child) {
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
      border-right: 0 !important;
    }
  }
}

// Password toggle specific styling
.input-group-merge {
  .form-control {
    border-right: 1px solid var(--border-color) !important;
  }

  .input-group-append {
    .input-group-text {
      background-color: transparent !important;
      border-left: none !important;

      &:hover {
        background-color: var(--color-primary-50) !important;
      }
    }
  }
}

// Modern checkbox and radio styling
.form-check {
  position: relative !important;
  display: block !important;
  padding-left: 1.75rem !important;
  margin-bottom: var(--spacing-3) !important;

  .form-check-input {
    position: absolute !important;
    margin-top: 0.25rem !important;
    margin-left: -1.75rem !important;
    width: 1rem !important;
    height: 1rem !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--radius-sm) !important;
    background-color: var(--bg-surface) !important;
    transition: all var(--transition-fast) !important;

    &:focus {
      border-color: var(--color-primary) !important;
      outline: 0 !important;
      box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1) !important;
    }

    &:checked {
      background-color: var(--color-primary) !important;
      border-color: var(--color-primary) !important;
    }

    &[type="radio"] {
      border-radius: var(--radius-full) !important;
    }
  }

  .form-check-label {
    font-size: var(--text-sm) !important;
    font-weight: var(--font-normal) !important;
    color: var(--text-primary) !important;
    cursor: pointer !important;
  }
}

// File input styling
input[type="file"].form-control {
  padding: var(--spacing-2) var(--spacing-3) !important;

  &::-webkit-file-upload-button {
    padding: var(--spacing-2) var(--spacing-3) !important;
    margin: calc(var(--spacing-2) * -1) var(--spacing-3) calc(var(--spacing-2) * -1) calc(var(--spacing-3) * -1) !important;
    background-color: var(--color-gray-100) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--radius-sm) !important;
    color: var(--text-secondary) !important;
    font-size: var(--text-sm) !important;
    cursor: pointer !important;
    transition: all var(--transition-fast) !important;

    &:hover {
      background-color: var(--color-primary-50) !important;
      color: var(--color-primary) !important;
    }
  }
}

// ===================================================================
// Modern Form Container & Layout
// ===================================================================

.modern-form-container {
  background: var(--bg-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-8);
  margin: var(--spacing-6) 0;
  border: 1px solid var(--border-color);

  @media (max-width: 768px) {
    padding: var(--spacing-6);
    margin: var(--spacing-4) 0;
    border-radius: var(--radius-lg);
  }
}

.modern-form-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-6);
  border-bottom: 2px solid var(--border-color);

  .modern-form-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-3);

    .title-icon {
      width: 2rem;
      height: 2rem;
      color: var(--color-primary);
    }
  }

  .modern-form-subtitle {
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin: 0;
    font-weight: var(--font-normal);
  }
}

.modern-form-section {
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6);
  background: var(--color-gray-25);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-100);

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--color-gray-200);

    .section-icon {
      width: 1.5rem;
      height: 1.5rem;
      color: var(--color-primary);
    }
  }
}

.modern-form-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);

  .label-icon {
    width: 1rem;
    height: 1rem;
    color: var(--color-primary);
    flex-shrink: 0;
  }

  .required {
    color: var(--color-danger);
    font-weight: var(--font-bold);
    margin-left: var(--spacing-1);
  }
}

.modern-input,
.modern-select {
  border: 2px solid var(--border-color) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-4) var(--spacing-4) !important;
  font-size: var(--text-sm) !important;
  line-height: 1.5 !important;
  color: var(--text-primary) !important;
  background-color: var(--bg-surface) !important;
  transition: all var(--transition-fast) !important;

  &:focus {
    border-color: var(--color-primary) !important;
    outline: 0 !important;
    box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb), 0.1) !important;
    background-color: var(--bg-surface) !important;
  }

  &:hover:not(:focus) {
    border-color: var(--color-gray-400) !important;
  }

  &::placeholder {
    color: var(--text-muted) !important;
    font-style: italic !important;
  }
}

.modern-form-actions {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  margin-top: var(--spacing-8);
  padding-top: var(--spacing-6);
  border-top: 2px solid var(--border-color);

  @media (max-width: 576px) {
    flex-direction: column;

    .btn-modern {
      width: 100%;
    }
  }
}

.btn-modern {
  @include modern-button();
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  border-radius: var(--radius-lg);
  border: 2px solid transparent;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;

  .btn-icon {
    width: 1.2rem;
    height: 1.2rem;
    flex-shrink: 0;
  }

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover:not(:disabled):before {
    left: 100%;
  }

  &.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }

  &.btn-secondary {
    background: var(--bg-surface);
    color: var(--text-secondary);
    border-color: var(--border-color);

    &:hover:not(:disabled) {
      background: var(--color-gray-50);
      color: var(--text-primary);
      border-color: var(--color-gray-300);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

// ===================================================================
// Modern Modal Styling
// ===================================================================

.modern-modal-form {
  .modern-modal-header {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    border-bottom: none;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    padding: var(--spacing-6);

    .modern-modal-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      margin: 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-3);

      .title-icon {
        width: 1.5rem;
        height: 1.5rem;
        color: var(--text-inverse);
      }
    }

    .modern-close-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: var(--radius-md);
      padding: var(--spacing-2);
      color: var(--text-inverse);
      cursor: pointer;
      transition: all var(--transition-fast);
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }

      i {
        width: 1.2rem;
        height: 1.2rem;
      }
    }
  }

  .modern-modal-body {
    padding: var(--spacing-6);
    background: var(--bg-surface);

    .modern-quick-form {
      .form-group {
        margin-bottom: var(--spacing-5);

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .modern-modal-footer {
    background: var(--color-gray-25);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    padding: var(--spacing-4) var(--spacing-6);
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;

    .btn-modern {
      padding: var(--spacing-3) var(--spacing-5);
      font-size: var(--text-sm);

      .btn-icon {
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// Modal backdrop and container enhancements
.modal {
  .modal-dialog {
    .modal-content {
      border: none;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-2xl);
      overflow: hidden;

      .modal-header {
        &.modern-modal-header {
          border-bottom: none;
        }
      }

      .modal-body {
        &.modern-modal-body {
          padding: var(--spacing-6);
        }
      }

      .modal-footer {
        &.modern-modal-footer {
          border-top: 1px solid var(--border-color);
        }
      }
    }
  }
}

// ===================================================================
// Modern Table Styling
// ===================================================================

.table {
  background: var(--bg-surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);

  thead {
    th {
      background: var(--gradient-primary);
      color: var(--text-inverse);
      font-weight: var(--font-semibold);
      font-size: var(--text-sm);
      padding: var(--spacing-4) var(--spacing-4);
      border: none;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &:first-child {
        border-radius: var(--radius-lg) 0 0 0;
      }

      &:last-child {
        border-radius: 0 var(--radius-lg) 0 0;
      }
    }
  }

  tbody {
    tr {
      transition: all var(--transition-fast);
      border-bottom: 1px solid var(--border-color);

      &:hover {
        background: var(--color-primary-25);
        transform: scale(1.01);
      }

      &:last-child {
        border-bottom: none;

        td {
          &:first-child {
            border-radius: 0 0 0 var(--radius-lg);
          }

          &:last-child {
            border-radius: 0 0 var(--radius-lg) 0;
          }
        }
      }

      td {
        padding: var(--spacing-4);
        font-size: var(--text-sm);
        color: var(--text-primary);
        border: none;
        vertical-align: middle;

        a {
          color: var(--color-primary);
          text-decoration: none;
          font-weight: var(--font-medium);
          transition: color var(--transition-fast);

          &:hover {
            color: var(--color-primary-600);
            text-decoration: underline;
          }
        }
      }
    }
  }
}

// Enhanced table container and header
.modern-table-container {
  background: var(--bg-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-6);
  margin: var(--spacing-6) 0;
  border: 1px solid var(--border-color);

  .modern-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 2px solid var(--border-color);

    .table-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-3);

      .title-icon {
        width: 1.5rem;
        height: 1.5rem;
        color: var(--color-primary);
      }
    }

    .table-actions {
      .btn-modern.btn-sm {
        padding: var(--spacing-2) var(--spacing-4);
        font-size: var(--text-sm);

        .btn-icon {
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }

  .table-responsive {
    border-radius: var(--radius-lg);
    overflow: hidden;
  }
}

// Enhanced table styling
.modern-table {
  margin-bottom: 0;

  thead th {
    .th-icon {
      width: 1rem;
      height: 1rem;
      margin-right: var(--spacing-2);
      opacity: 0.9;
    }
  }

  tbody {
    .modern-table-row {
      .client-id-link {
        .id-badge {
          background: var(--color-primary-100);
          color: var(--color-primary-800);
          padding: var(--spacing-1) var(--spacing-2);
          border-radius: var(--radius-md);
          font-size: var(--text-xs);
          font-weight: var(--font-bold);
        }
      }

      .client-name {
        font-weight: var(--font-medium);
        color: var(--text-primary);
      }

      .priority-badge {
        padding: var(--spacing-1) var(--spacing-3);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: var(--font-semibold);
        text-transform: uppercase;

        &.priority-high {
          background: var(--color-danger-100);
          color: var(--color-danger-800);
        }

        &.priority-medium {
          background: var(--color-warning-100);
          color: var(--color-warning-800);
        }

        &.priority-low {
          background: var(--color-success-100);
          color: var(--color-success-800);
        }
      }

      .gender-badge {
        display: flex;
        align-items: center;
        gap: var(--spacing-1);

        .gender-icon {
          width: 0.875rem;
          height: 0.875rem;
          color: var(--color-primary);
        }
      }

      .contact-info {
        color: var(--text-secondary);
        font-size: var(--text-sm);
      }

      .address-text {
        color: var(--text-secondary);
        font-size: var(--text-sm);
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .credit-amount {
        font-weight: var(--font-semibold);
        color: var(--color-success-600);
        background: var(--color-success-50);
        padding: var(--spacing-1) var(--spacing-2);
        border-radius: var(--radius-md);
        font-size: var(--text-sm);
      }
    }
  }
}

// ===================================================================
// Modern Pagination Styling
// ===================================================================

.modern-pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .pagination-info {
    .pagination-text {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      font-weight: var(--font-medium);
    }
  }

  .modern-pagination {
    .modern-pagination-controls {
      ::ng-deep {
        .ngx-pagination {
          display: flex;
          gap: var(--spacing-2);
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            &.current {
              a, span {
                background: var(--gradient-primary) !important;
                color: var(--text-inverse) !important;
                border-color: var(--color-primary) !important;
                font-weight: var(--font-semibold) !important;
                transform: scale(1.1);
                box-shadow: var(--shadow-md);
              }
            }

            &.disabled {
              a, span {
                background: var(--color-gray-100) !important;
                color: var(--text-muted) !important;
                border-color: var(--color-gray-200) !important;
                cursor: not-allowed !important;
                opacity: 0.6;
              }
            }

            a, span {
              display: flex !important;
              align-items: center !important;
              justify-content: center !important;
              padding: var(--spacing-2) var(--spacing-3) !important;
              border: 2px solid var(--border-color) !important;
              border-radius: var(--radius-md) !important;
              background: var(--bg-surface) !important;
              color: var(--text-primary) !important;
              text-decoration: none !important;
              font-size: var(--text-sm) !important;
              font-weight: var(--font-medium) !important;
              transition: all var(--transition-fast) !important;
              min-width: 2.5rem !important;
              height: 2.5rem !important;

              &:hover:not(.disabled) {
                background: var(--color-primary-50) !important;
                border-color: var(--color-primary) !important;
                color: var(--color-primary) !important;
                transform: translateY(-1px);
              }
            }
          }
        }
      }
    }
  }
}

// ===================================================================
// Modern Client Profile Styling
// ===================================================================

.modern-client-profile {
  background: var(--bg-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin: var(--spacing-6) 0;
  border: 1px solid var(--border-color);

  .profile-header {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    padding: var(--spacing-8);
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    position: relative;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: var(--spacing-4);
      padding: var(--spacing-6);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
      pointer-events: none;
    }

    .profile-avatar {
      position: relative;
      z-index: 1;

      .avatar-placeholder {
        width: 5rem;
        height: 5rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid rgba(255, 255, 255, 0.3);

        .avatar-icon {
          width: 2.5rem;
          height: 2.5rem;
          color: var(--text-inverse);
        }
      }

      .avatar-image {
        width: 5rem;
        height: 5rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 3px solid rgba(255, 255, 255, 0.3);
      }
    }

    .profile-info {
      flex: 1;
      position: relative;
      z-index: 1;

      .profile-name {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        margin: 0 0 var(--spacing-3) 0;
        color: var(--text-inverse);

        @media (max-width: 768px) {
          font-size: var(--text-2xl);
        }
      }

      .profile-badges {
        display: flex;
        gap: var(--spacing-3);
        flex-wrap: wrap;

        @media (max-width: 768px) {
          justify-content: center;
        }

        .priority-badge,
        .gender-badge {
          display: flex;
          align-items: center;
          gap: var(--spacing-1);
          padding: var(--spacing-2) var(--spacing-3);
          background: rgba(255, 255, 255, 0.2);
          border-radius: var(--radius-full);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          color: var(--text-inverse);
          border: 1px solid rgba(255, 255, 255, 0.3);

          .badge-icon {
            width: 1rem;
            height: 1rem;
          }
        }
      }
    }

    .profile-actions {
      display: flex;
      gap: var(--spacing-3);
      position: relative;
      z-index: 1;

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
      }

      .btn-modern {
        background: rgba(255, 255, 255, 0.2);
        color: var(--text-inverse);
        border-color: rgba(255, 255, 255, 0.3);

        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }

        &.btn-danger {
          background: rgba(220, 38, 38, 0.8);
          border-color: rgba(220, 38, 38, 0.9);

          &:hover:not(:disabled) {
            background: rgba(220, 38, 38, 0.9);
          }
        }
      }
    }
  }

  .profile-content {
    padding: var(--spacing-8);

    @media (max-width: 768px) {
      padding: var(--spacing-6);
    }

    .info-sections {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-8);

      .info-section {
        background: var(--color-gray-25);
        border-radius: var(--radius-lg);
        padding: var(--spacing-6);
        border: 1px solid var(--color-gray-100);

        .section-title {
          font-size: var(--text-lg);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
          margin-bottom: var(--spacing-6);
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          padding-bottom: var(--spacing-3);
          border-bottom: 2px solid var(--color-gray-200);

          .section-icon {
            width: 1.5rem;
            height: 1.5rem;
            color: var(--color-primary);
          }
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: var(--spacing-4);

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .info-item {
            background: var(--bg-surface);
            padding: var(--spacing-4);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            transition: all var(--transition-fast);

            &:hover {
              box-shadow: var(--shadow-md);
              transform: translateY(-1px);
            }

            &.full-width {
              grid-column: 1 / -1;
            }

            .info-label {
              display: flex;
              align-items: center;
              gap: var(--spacing-2);
              font-size: var(--text-sm);
              font-weight: var(--font-semibold);
              color: var(--text-secondary);
              margin-bottom: var(--spacing-2);

              .info-icon {
                width: 1rem;
                height: 1rem;
                color: var(--color-primary);
              }
            }

            .info-value {
              font-size: var(--text-base);
              color: var(--text-primary);
              font-weight: var(--font-medium);

              &.credit-amount {
                font-weight: var(--font-bold);
                color: var(--color-success-600);
                background: var(--color-success-50);
                padding: var(--spacing-2) var(--spacing-3);
                border-radius: var(--radius-md);
                display: inline-block;
              }

              .id-badge {
                background: var(--color-primary-100);
                color: var(--color-primary-800);
                padding: var(--spacing-1) var(--spacing-3);
                border-radius: var(--radius-full);
                font-size: var(--text-sm);
                font-weight: var(--font-bold);
              }
            }
          }
        }
      }
    }
  }
}

// ===================================================================
// Modern Product Form Styling
// ===================================================================

.scanner-container {
  background: var(--color-gray-25);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  border: 1px solid var(--color-gray-200);

  .scanner-controls {
    margin-bottom: var(--spacing-4);
  }

  .scanner-viewport {
    display: flex;
    justify-content: center;
    margin: var(--spacing-4) 0;

    .modern-scanner {
      width: 100%;
      max-width: 400px;
      height: 250px;
      border-radius: var(--radius-md);
      overflow: hidden;
      border: 2px solid var(--color-primary-200);
    }
  }

  .scanner-result {
    margin-top: var(--spacing-4);

    .result-card {
      background: var(--color-success-50);
      border: 1px solid var(--color-success-200);
      border-radius: var(--radius-md);
      padding: var(--spacing-4);
      display: flex;
      align-items: center;
      gap: var(--spacing-3);

      .result-icon {
        width: 1.5rem;
        height: 1.5rem;
        color: var(--color-success-600);
      }

      .result-content {
        .result-label {
          display: block;
          font-size: var(--text-sm);
          color: var(--color-success-700);
          font-weight: var(--font-medium);
        }

        .result-value {
          font-size: var(--text-lg);
          color: var(--color-success-800);
          font-weight: var(--font-bold);
        }
      }
    }
  }
}

.scanner-error {
  .error-card {
    background: var(--color-red-50);
    border: 1px solid var(--color-red-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-6);
    text-align: center;

    .error-icon {
      width: 3rem;
      height: 3rem;
      color: var(--color-red-500);
      margin: 0 auto var(--spacing-3);
    }

    .error-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--color-red-800);
      margin-bottom: var(--spacing-2);
    }

    .error-message {
      font-size: var(--text-sm);
      color: var(--color-red-600);
      margin: 0;
    }
  }
}

.modern-checkbox-container {
  margin-top: var(--spacing-2);

  .modern-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    cursor: pointer;
    padding: var(--spacing-3);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);

    &:hover {
      background: var(--color-gray-50);
    }

    input[type="checkbox"] {
      display: none;
    }

    .checkmark {
      width: 1.25rem;
      height: 1.25rem;
      border: 2px solid var(--color-gray-300);
      border-radius: var(--radius-sm);
      position: relative;
      transition: all var(--transition-fast);

      &::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 6px;
        width: 6px;
        height: 10px;
        border: solid var(--text-inverse);
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
        transition: all var(--transition-fast);
      }
    }

    input[type="checkbox"]:checked + .checkmark {
      background: var(--color-primary);
      border-color: var(--color-primary);

      &::after {
        opacity: 1;
      }
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      font-weight: var(--font-medium);
      color: var(--text-primary);

      .checkbox-icon {
        width: 1rem;
        height: 1rem;
        color: var(--color-primary);
      }
    }
  }
}

.parent-info {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--color-blue-50);
  border: 1px solid var(--color-blue-200);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);

  .info-icon {
    width: 1rem;
    height: 1rem;
    color: var(--color-blue-600);
  }

  .info-text {
    font-size: var(--text-sm);
    color: var(--color-blue-800);
    font-weight: var(--font-medium);
  }
}

.file-upload-container {
  position: relative;

  .modern-file-input {
    padding: var(--spacing-4);
    border: 2px dashed var(--color-gray-300);
    border-radius: var(--radius-lg);
    background: var(--color-gray-25);
    transition: all var(--transition-fast);

    &:hover {
      border-color: var(--color-primary-300);
      background: var(--color-primary-25);
    }

    &:focus {
      border-color: var(--color-primary);
      background: var(--color-primary-50);
      box-shadow: 0 0 0 3px var(--color-primary-100);
    }
  }

  .file-upload-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    pointer-events: none;
    color: var(--text-secondary);

    .hint-icon {
      width: 2rem;
      height: 2rem;
      color: var(--color-gray-400);
    }

    span {
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
    }
  }
}

.modern-textarea {
  resize: vertical;
  min-height: 100px;

  &:focus {
    min-height: 120px;
  }
}

.required {
  color: var(--color-red-500);
  margin-left: var(--spacing-1);
}

// ===================================================================
// Modern Product Profile Styling
// ===================================================================

.modern-product-profile {
  background: var(--bg-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin: var(--spacing-6) 0;
  border: 1px solid var(--border-color);

  .product-header {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    padding: var(--spacing-8);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-4);
      padding: var(--spacing-6);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
      pointer-events: none;
    }

    .product-title-section {
      position: relative;
      z-index: 1;

      .product-name {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        margin: 0 0 var(--spacing-3) 0;
        color: var(--text-inverse);
        display: flex;
        align-items: center;
        gap: var(--spacing-3);

        @media (max-width: 768px) {
          font-size: var(--text-2xl);
          justify-content: center;
        }

        .product-icon {
          width: 2rem;
          height: 2rem;
        }
      }

      .product-badges {
        display: flex;
        gap: var(--spacing-3);
        flex-wrap: wrap;

        @media (max-width: 768px) {
          justify-content: center;
        }

        .product-badge {
          display: flex;
          align-items: center;
          gap: var(--spacing-1);
          padding: var(--spacing-2) var(--spacing-3);
          background: rgba(255, 255, 255, 0.2);
          border-radius: var(--radius-full);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          color: var(--text-inverse);
          border: 1px solid rgba(255, 255, 255, 0.3);

          .badge-icon {
            width: 1rem;
            height: 1rem;
          }

          &.barcode-badge {
            background: rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.5);
          }

          &.category-badge {
            background: rgba(16, 185, 129, 0.3);
            border-color: rgba(16, 185, 129, 0.5);
          }

          &.piece-badge {
            background: rgba(245, 158, 11, 0.3);
            border-color: rgba(245, 158, 11, 0.5);
          }
        }
      }
    }

    .product-actions {
      display: flex;
      gap: var(--spacing-3);
      position: relative;
      z-index: 1;

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
      }

      .btn-modern {
        background: rgba(255, 255, 255, 0.2);
        color: var(--text-inverse);
        border-color: rgba(255, 255, 255, 0.3);

        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }

        &.btn-danger {
          background: rgba(220, 38, 38, 0.8);
          border-color: rgba(220, 38, 38, 0.9);

          &:hover:not(:disabled) {
            background: rgba(220, 38, 38, 0.9);
          }
        }
      }
    }
  }

  .product-content {
    padding: var(--spacing-8);

    @media (max-width: 768px) {
      padding: var(--spacing-6);
    }

    .product-main-info {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-8);

      .product-images {
        .modern-carousel {
          border-radius: var(--radius-lg);
          overflow: hidden;
          box-shadow: var(--shadow-md);

          .carousel-image-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
            background: var(--color-gray-50);

            .product-image {
              max-height: 400px;
              max-width: 100%;
              object-fit: contain;
              border-radius: var(--radius-md);
            }
          }
        }
      }

      .no-images-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        background: var(--color-gray-50);
        border: 2px dashed var(--color-gray-300);
        border-radius: var(--radius-lg);
        color: var(--text-secondary);

        .placeholder-icon {
          width: 3rem;
          height: 3rem;
          margin-bottom: var(--spacing-2);
          color: var(--color-gray-400);
        }

        span {
          font-size: var(--text-lg);
          font-weight: var(--font-medium);
        }
      }

      .barcode-section {
        background: var(--color-gray-25);
        border-radius: var(--radius-lg);
        padding: var(--spacing-6);
        border: 1px solid var(--color-gray-200);

        .barcode-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-6);
          padding-bottom: var(--spacing-3);
          border-bottom: 2px solid var(--color-gray-200);

          @media (max-width: 768px) {
            flex-direction: column;
            gap: var(--spacing-3);
            align-items: flex-start;
          }

          .section-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-2);

            .section-icon {
              width: 1.5rem;
              height: 1.5rem;
              color: var(--color-primary);
            }
          }

          .barcode-actions {
            display: flex;
            gap: var(--spacing-2);

            .btn-sm {
              padding: var(--spacing-2) var(--spacing-3);
              font-size: var(--text-sm);
            }
          }
        }

        .barcode-display {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: var(--spacing-6);

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .qr-code-container,
          .barcode-container {
            background: var(--bg-surface);
            border-radius: var(--radius-md);
            padding: var(--spacing-4);
            border: 1px solid var(--border-color);
            text-align: center;

            .code-label {
              font-size: var(--text-sm);
              font-weight: var(--font-semibold);
              color: var(--text-secondary);
              margin-bottom: var(--spacing-3);
            }

            .qr-code,
            .barcode {
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100px;
            }
          }
        }
      }

      .product-info-sections {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-6);

        .info-section {
          background: var(--color-gray-25);
          border-radius: var(--radius-lg);
          padding: var(--spacing-6);
          border: 1px solid var(--color-gray-100);

          .section-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-6);
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            padding-bottom: var(--spacing-3);
            border-bottom: 2px solid var(--color-gray-200);

            .section-icon {
              width: 1.5rem;
              height: 1.5rem;
              color: var(--color-primary);
            }
          }

          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-4);

            @media (max-width: 768px) {
              grid-template-columns: 1fr;
            }

            .info-item {
              background: var(--bg-surface);
              padding: var(--spacing-4);
              border-radius: var(--radius-md);
              border: 1px solid var(--border-color);
              transition: all var(--transition-fast);

              &:hover {
                box-shadow: var(--shadow-md);
                transform: translateY(-1px);
              }

              &.full-width {
                grid-column: 1 / -1;
              }

              .info-label {
                display: flex;
                align-items: center;
                gap: var(--spacing-2);
                font-size: var(--text-sm);
                font-weight: var(--font-semibold);
                color: var(--text-secondary);
                margin-bottom: var(--spacing-2);

                .info-icon {
                  width: 1rem;
                  height: 1rem;
                  color: var(--color-primary);
                }
              }

              .info-value {
                font-size: var(--text-base);
                color: var(--text-primary);
                font-weight: var(--font-medium);

                &.price-value {
                  font-weight: var(--font-bold);
                  color: var(--color-blue-600);

                  &.selling-price {
                    color: var(--color-green-600);
                    background: var(--color-green-50);
                    padding: var(--spacing-2) var(--spacing-3);
                    border-radius: var(--radius-md);
                    display: inline-block;
                  }
                }
              }
            }
          }

          .rangements-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-3);

            .rangement-item {
              background: var(--bg-surface);
              padding: var(--spacing-4);
              border-radius: var(--radius-md);
              border: 1px solid var(--border-color);
              display: flex;
              justify-content: space-between;
              align-items: center;
              transition: all var(--transition-fast);

              &:hover {
                box-shadow: var(--shadow-md);
                transform: translateY(-1px);
              }

              @media (max-width: 768px) {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-2);
              }

              .rangement-info {
                display: flex;
                flex-direction: column;
                gap: var(--spacing-1);

                .rangement-location {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-2);
                  font-size: var(--text-sm);
                  color: var(--text-secondary);

                  .location-icon {
                    width: 1rem;
                    height: 1rem;
                    color: var(--color-primary);
                  }
                }

                .rangement-name {
                  .rangement-link {
                    color: var(--color-primary);
                    text-decoration: none;
                    font-weight: var(--font-semibold);

                    &:hover {
                      text-decoration: underline;
                    }
                  }
                }
              }

              .stock-info {
                display: flex;
                align-items: center;
                gap: var(--spacing-2);
                background: var(--color-blue-50);
                padding: var(--spacing-2) var(--spacing-3);
                border-radius: var(--radius-full);

                .stock-label {
                  font-size: var(--text-sm);
                  color: var(--color-blue-700);
                  font-weight: var(--font-medium);
                }

                .stock-value {
                  font-size: var(--text-base);
                  color: var(--color-blue-800);
                  font-weight: var(--font-bold);
                }
              }
            }
          }
        }
      }
    }
  }
}

// ===================================================================
// Modern Photo Management Styling
// ===================================================================

.photo-management-container {
  .modern-photo-carousel {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    background: var(--color-gray-50);

    .photo-slide {
      position: relative;

      .photo-container {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;
        background: var(--color-gray-50);

        .product-photo {
          max-height: 400px;
          max-width: 100%;
          object-fit: contain;
          border-radius: var(--radius-md);
        }

        .photo-delete-btn {
          position: absolute;
          top: var(--spacing-3);
          right: var(--spacing-3);
          background: rgba(220, 38, 38, 0.9);
          color: var(--text-inverse);
          border: none;
          border-radius: var(--radius-full);
          width: 3rem;
          height: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all var(--transition-fast);
          backdrop-filter: blur(4px);

          &:hover:not(:disabled) {
            background: rgba(220, 38, 38, 1);
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
          }

          .btn-icon {
            width: 1.25rem;
            height: 1.25rem;
          }
        }
      }
    }
  }
}

.current-parent {
  font-size: var(--text-sm);
  color: var(--color-blue-600);
  font-weight: var(--font-medium);
  margin-left: var(--spacing-2);
}

// ===================================================================
// Modern Product List Styling
// ===================================================================

.modern-list-container {
  .modern-list-header {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-4);
      padding: var(--spacing-6);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
      pointer-events: none;
    }

    .list-title-section {
      position: relative;
      z-index: 1;

      .list-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        margin: 0 0 var(--spacing-2) 0;
        color: var(--text-inverse);
        display: flex;
        align-items: center;
        gap: var(--spacing-3);

        @media (max-width: 768px) {
          font-size: var(--text-2xl);
          justify-content: center;
        }

        .title-icon {
          width: 2rem;
          height: 2rem;
        }
      }

      .list-subtitle {
        font-size: var(--text-lg);
        color: rgba(255, 255, 255, 0.9);
        margin: 0;

        @media (max-width: 768px) {
          text-align: center;
        }
      }
    }

    .list-actions {
      position: relative;
      z-index: 1;

      .btn-modern {
        background: rgba(255, 255, 255, 0.2);
        color: var(--text-inverse);
        border-color: rgba(255, 255, 255, 0.3);

        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }
      }
    }
  }

  .modern-filters-section {
    background: var(--bg-surface);
    padding: var(--spacing-6);
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);

    .filters-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-6);

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
      }

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);

        .filter-label {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          font-size: var(--text-sm);
          font-weight: var(--font-semibold);
          color: var(--text-secondary);

          .label-icon {
            width: 1rem;
            height: 1rem;
            color: var(--color-primary);
          }
        }

        .search-input-group {
          display: flex;
          gap: var(--spacing-2);

          .modern-search-input {
            flex: 1;
          }

          .search-btn {
            white-space: nowrap;
          }
        }
      }
    }
  }

  .modern-table-container {
    background: var(--bg-surface);
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);

    .table-wrapper {
      overflow-x: auto;

      .modern-table {
        width: 100%;
        border-collapse: collapse;

        .table-header {
          background: var(--color-gray-50);

          .table-header-cell {
            padding: var(--spacing-4) var(--spacing-6);
            text-align: left;
            border-bottom: 2px solid var(--border-color);

            .header-content {
              display: flex;
              align-items: center;
              gap: var(--spacing-2);
              font-size: var(--text-sm);
              font-weight: var(--font-semibold);
              color: var(--text-secondary);

              .header-icon {
                width: 1rem;
                height: 1rem;
                color: var(--color-primary);
              }
            }
          }
        }

        .table-body {
          .table-row {
            transition: all var(--transition-fast);
            border-bottom: 1px solid var(--border-color);

            &:hover {
              background: var(--color-gray-25);
            }

            .table-cell {
              padding: var(--spacing-4) var(--spacing-6);
              vertical-align: middle;

              .cell-content {
                display: flex;
                align-items: center;

                .id-link {
                  text-decoration: none;

                  .id-badge {
                    background: var(--color-primary-100);
                    color: var(--color-primary-800);
                    padding: var(--spacing-1) var(--spacing-2);
                    border-radius: var(--radius-md);
                    font-size: var(--text-sm);
                    font-weight: var(--font-semibold);
                    transition: all var(--transition-fast);
                  }

                  &:hover .id-badge {
                    background: var(--color-primary-200);
                    transform: scale(1.05);
                  }
                }

                .product-info {
                  display: flex;
                  flex-direction: column;
                  gap: var(--spacing-1);

                  .product-name {
                    font-weight: var(--font-semibold);
                    color: var(--text-primary);
                  }

                  .product-barcode {
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-1);
                    font-size: var(--text-sm);
                    color: var(--text-secondary);

                    .barcode-icon {
                      width: 0.875rem;
                      height: 0.875rem;
                    }
                  }
                }

                .category-badge {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-2);
                  background: var(--color-blue-50);
                  padding: var(--spacing-2) var(--spacing-3);
                  border-radius: var(--radius-full);
                  border: 1px solid var(--color-blue-200);

                  .category-icon {
                    width: 1rem;
                    height: 1rem;
                    color: var(--color-blue-600);
                  }

                  .category-id {
                    font-size: var(--text-sm);
                    font-weight: var(--font-bold);
                    color: var(--color-blue-800);
                  }

                  .category-name {
                    font-size: var(--text-sm);
                    color: var(--color-blue-700);
                  }
                }

                .price-value {
                  font-size: var(--text-lg);
                  font-weight: var(--font-bold);
                  color: var(--color-green-600);
                  background: var(--color-green-50);
                  padding: var(--spacing-2) var(--spacing-3);
                  border-radius: var(--radius-md);
                  border: 1px solid var(--color-green-200);
                }

                .action-buttons {
                  display: flex;
                  gap: var(--spacing-2);

                  .btn-action {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: var(--radius-md);
                    text-decoration: none;
                    transition: all var(--transition-fast);

                    .action-icon {
                      width: 1.125rem;
                      height: 1.125rem;
                    }

                    &.btn-view {
                      background: var(--color-blue-50);
                      color: var(--color-blue-600);
                      border: 1px solid var(--color-blue-200);

                      &:hover {
                        background: var(--color-blue-100);
                        transform: translateY(-1px);
                        box-shadow: var(--shadow-md);
                      }
                    }

                    &.btn-edit {
                      background: var(--color-amber-50);
                      color: var(--color-amber-600);
                      border: 1px solid var(--color-amber-200);

                      &:hover {
                        background: var(--color-amber-100);
                        transform: translateY(-1px);
                        box-shadow: var(--shadow-md);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .empty-state {
        text-align: center;
        padding: var(--spacing-12) var(--spacing-6);

        .empty-icon {
          margin-bottom: var(--spacing-6);

          .empty-icon-svg {
            width: 4rem;
            height: 4rem;
            color: var(--color-gray-400);
          }
        }

        .empty-title {
          font-size: var(--text-xl);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
          margin-bottom: var(--spacing-2);
        }

        .empty-description {
          font-size: var(--text-base);
          color: var(--text-secondary);
          margin-bottom: var(--spacing-6);
        }
      }
    }
  }

  .modern-pagination-container {
    background: var(--bg-surface);
    padding: var(--spacing-6);
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    display: flex;
    justify-content: center;
  }
}

// ===================================================================
// Additional Table Components Styling
// ===================================================================

.date-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);

  .date-value {
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    font-size: var(--text-sm);
  }

  .time-value {
    font-size: var(--text-xs);
    color: var(--text-secondary);
  }
}

.client-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);

  .client-id {
    font-size: var(--text-xs);
    color: var(--color-blue-600);
    font-weight: var(--font-bold);
    background: var(--color-blue-50);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    display: inline-block;
    width: fit-content;
  }

  .client-name {
    font-weight: var(--font-medium);
    color: var(--text-primary);
    font-size: var(--text-sm);
  }
}

.status-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  width: fit-content;

  .status-icon {
    width: 0.75rem;
    height: 0.75rem;
    fill: currentColor;
  }

  // Default status styling
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);

  // Status-specific styling
  &.status-pending {
    background: var(--color-yellow-100);
    color: var(--color-yellow-800);
    border-color: var(--color-yellow-300);
  }

  &.status-confirmed,
  &.status-complete,
  &.status-delivered {
    background: var(--color-green-100);
    color: var(--color-green-800);
    border-color: var(--color-green-300);
  }

  &.status-cancelled,
  &.status-rejected {
    background: var(--color-red-100);
    color: var(--color-red-800);
    border-color: var(--color-red-300);
  }

  &.status-processing,
  &.status-preparing {
    background: var(--color-blue-100);
    color: var(--color-blue-800);
    border-color: var(--color-blue-300);
  }
}

// ===================================================================
// Advanced UI Components
// ===================================================================

// Loading States
.loading-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--color-gray-200);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;

  &.spinner-sm {
    width: 1rem;
    height: 1rem;
    border-width: 1px;
  }

  &.spinner-lg {
    width: 2rem;
    height: 2rem;
    border-width: 3px;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);

  .loading-content {
    text-align: center;

    .loading-spinner {
      margin-bottom: var(--spacing-4);
    }

    .loading-text {
      font-size: var(--text-lg);
      color: var(--text-secondary);
      font-weight: var(--font-medium);
    }
  }
}

.btn-loading {
  position: relative;
  pointer-events: none;

  .btn-text {
    opacity: 0;
  }

  .loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

// Tooltips
.tooltip-container {
  position: relative;
  display: inline-block;

  .tooltip-content {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-gray-900);
    color: var(--text-inverse);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 5px solid transparent;
      border-top-color: var(--color-gray-900);
    }
  }

  &:hover .tooltip-content,
  &:focus .tooltip-content {
    opacity: 1;
    visibility: visible;
  }

  // Tooltip positions
  &.tooltip-top .tooltip-content {
    bottom: 125%;
    top: auto;

    &::after {
      top: 100%;
      border-top-color: var(--color-gray-900);
      border-bottom-color: transparent;
    }
  }

  &.tooltip-bottom .tooltip-content {
    top: 125%;
    bottom: auto;

    &::after {
      bottom: 100%;
      top: auto;
      border-bottom-color: var(--color-gray-900);
      border-top-color: transparent;
    }
  }

  &.tooltip-left .tooltip-content {
    right: 125%;
    left: auto;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);

    &::after {
      left: 100%;
      right: auto;
      top: 50%;
      transform: translateY(-50%);
      border-left-color: var(--color-gray-900);
      border-right-color: transparent;
      border-top-color: transparent;
      border-bottom-color: transparent;
    }
  }

  &.tooltip-right .tooltip-content {
    left: 125%;
    right: auto;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);

    &::after {
      right: 100%;
      left: auto;
      top: 50%;
      transform: translateY(-50%);
      border-right-color: var(--color-gray-900);
      border-left-color: transparent;
      border-top-color: transparent;
      border-bottom-color: transparent;
    }
  }
}

// Notifications
.notification-container {
  position: fixed;
  top: var(--spacing-6);
  right: var(--spacing-6);
  z-index: 10000;
  max-width: 400px;

  @media (max-width: 768px) {
    top: var(--spacing-4);
    right: var(--spacing-4);
    left: var(--spacing-4);
    max-width: none;
  }
}

.notification {
  background: var(--bg-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-3);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  animation: slideInRight 0.3s ease-out;
  position: relative;

  .notification-icon {
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;
    margin-top: var(--spacing-1);
  }

  .notification-content {
    flex: 1;

    .notification-title {
      font-size: var(--text-sm);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-1);
    }

    .notification-message {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      line-height: 1.4;
    }
  }

  .notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);

    &:hover {
      background: var(--color-gray-100);
      color: var(--text-primary);
    }

    .close-icon {
      width: 1rem;
      height: 1rem;
    }
  }

  // Notification types
  &.notification-success {
    border-left: 4px solid var(--color-green-500);

    .notification-icon {
      color: var(--color-green-600);
    }
  }

  &.notification-error {
    border-left: 4px solid var(--color-red-500);

    .notification-icon {
      color: var(--color-red-600);
    }
  }

  &.notification-warning {
    border-left: 4px solid var(--color-yellow-500);

    .notification-icon {
      color: var(--color-yellow-600);
    }
  }

  &.notification-info {
    border-left: 4px solid var(--color-blue-500);

    .notification-icon {
      color: var(--color-blue-600);
    }
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// Breadcrumbs
.breadcrumb-container {
  background: var(--bg-surface);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-4) var(--spacing-6);

  @media (max-width: 768px) {
    padding: var(--spacing-3) var(--spacing-4);
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  padding: 0;
  list-style: none;

  .breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);

    &:not(:last-child)::after {
      content: '';
      width: 1rem;
      height: 1rem;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/></svg>') no-repeat center;
      background-size: contain;
      opacity: 0.5;
    }

    a {
      color: var(--color-primary);
      text-decoration: none;
      transition: color var(--transition-fast);

      &:hover {
        color: var(--color-primary-dark);
        text-decoration: underline;
      }
    }

    &.active {
      color: var(--text-primary);
      font-weight: var(--font-medium);
    }

    .breadcrumb-icon {
      width: 1rem;
      height: 1rem;
      margin-right: var(--spacing-1);
    }
  }
}

// ===================================================================
// Accessibility & Focus Management
// ===================================================================

// Enhanced focus styles
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

// Skip links for screen readers
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--text-inverse);
  padding: var(--spacing-2) var(--spacing-4);
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 10000;
  font-weight: var(--font-semibold);

  &:focus {
    top: 6px;
  }
}

// Screen reader only content
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Focus trap for modals
.focus-trap {
  position: relative;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .btn-modern {
    border-width: 2px;
  }

  .modern-input,
  .modern-select,
  .modern-textarea {
    border-width: 2px;
  }

  .modern-table {
    border-width: 2px;

    th, td {
      border-width: 1px;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .loading-spinner {
    animation: none;
    border-top-color: var(--color-primary);
    border-right-color: var(--color-primary);
  }
}

// Keyboard navigation indicators
.keyboard-navigation {
  .btn-modern:focus,
  .modern-input:focus,
  .modern-select:focus,
  .modern-textarea:focus {
    box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.3);
  }

  .table-row:focus-within {
    background: var(--color-primary-50);
    outline: 2px solid var(--color-primary);
    outline-offset: -2px;
  }
}

// Error states for accessibility
.error-state {
  .modern-input,
  .modern-select,
  .modern-textarea {
    border-color: var(--color-red-500);
    background: var(--color-red-50);

    &:focus {
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.3);
    }
  }

  .error-message {
    color: var(--color-red-600);
    font-size: var(--text-sm);
    margin-top: var(--spacing-1);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);

    .error-icon {
      width: 1rem;
      height: 1rem;
      flex-shrink: 0;
    }
  }
}

// Success states
.success-state {
  .modern-input,
  .modern-select,
  .modern-textarea {
    border-color: var(--color-green-500);
    background: var(--color-green-50);

    &:focus {
      box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.3);
    }
  }

  .success-message {
    color: var(--color-green-600);
    font-size: var(--text-sm);
    margin-top: var(--spacing-1);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);

    .success-icon {
      width: 1rem;
      height: 1rem;
      flex-shrink: 0;
    }
  }
}

// ===================================================================
// Keyboard Navigation & Performance Optimizations
// ===================================================================

// Keyboard navigation support
body.keyboard-navigation {
  // Enhanced focus indicators for keyboard users
  .btn-modern:focus,
  .modern-input:focus,
  .modern-select:focus,
  .modern-textarea:focus,
  .table-row:focus-within,
  .nav-link:focus,
  .dropdown-item:focus {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 5px rgba(var(--color-primary-rgb), 0.2);
  }

  // Skip focus on mouse interactions
  .btn-modern:focus:not(:focus-visible),
  .modern-input:focus:not(:focus-visible),
  .modern-select:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
  }
}

// Performance optimizations
.modern-table {
  // Use transform for better performance
  will-change: transform;

  // Optimize repaints
  .table-row {
    will-change: background-color;
  }
}

.loading-spinner {
  // Use transform for animations
  will-change: transform;
}

// Optimize animations for performance
@media (prefers-reduced-motion: no-preference) {
  .btn-modern,
  .modern-input,
  .modern-select,
  .modern-textarea,
  .table-row {
    transition: all var(--transition-fast);
  }

  .notification {
    transition: transform var(--transition-normal), opacity var(--transition-normal);
  }
}

// Print styles for accessibility
@media print {
  .skip-link,
  .notification-container,
  .loading-overlay,
  .customizer,
  .btn-action {
    display: none !important;
  }

  .modern-table {
    border-collapse: collapse;

    th, td {
      border: 1px solid #000;
      padding: var(--spacing-2);
    }
  }

  .breadcrumb-container {
    border-bottom: 1px solid #000;
    margin-bottom: var(--spacing-4);
  }
}
