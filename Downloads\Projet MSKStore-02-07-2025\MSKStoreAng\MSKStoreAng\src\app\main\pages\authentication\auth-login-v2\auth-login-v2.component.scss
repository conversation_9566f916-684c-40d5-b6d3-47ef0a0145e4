@import '@core/scss/base/bootstrap-extended/include'; // Bootstrap includes
@import '@core/scss/base/pages/page-auth.scss';

// ===================================================================
// Modern Login Form Enhancements
// ===================================================================

.auth-wrapper {
  .auth-inner {
    .card {
      border: none;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.95);
      border-radius: 1rem;

      .card-body {
        padding: var(--spacing-8);

        @media (max-width: 576px) {
          padding: var(--spacing-6);
        }
      }
    }

    .brand-logo {
      margin-bottom: var(--spacing-8);
      text-align: center;

      .brand-text {
        font-size: var(--text-2xl);
        font-weight: var(--font-bold);
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .auth-login-form {
      .form-group {
        margin-bottom: var(--spacing-6);

        .form-label {
          font-size: var(--text-sm);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
          margin-bottom: var(--spacing-2);
          display: flex;
          align-items: center;

          .text-danger {
            margin-left: var(--spacing-1);
          }
        }

        .form-control {
          padding: var(--spacing-4);
          border-radius: var(--radius-lg);
          border: 2px solid var(--border-color);
          font-size: var(--text-sm);
          transition: all var(--transition-fast);
          background-color: var(--bg-surface);

          &:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb), 0.1);
            background-color: var(--bg-surface);
          }

          &:hover:not(:focus) {
            border-color: var(--color-gray-400);
          }

          &.is-invalid {
            border-color: var(--color-danger);

            &:focus {
              border-color: var(--color-danger);
              box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
            }
          }

          &.is-valid {
            border-color: var(--color-success);

            &:focus {
              border-color: var(--color-success);
              box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
            }
          }
        }

        // Password toggle styling
        .input-group-merge {
          .input-group-append {
            .input-group-text {
              border: 2px solid var(--border-color);
              border-left: none;
              border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
              background-color: var(--bg-surface);
              cursor: pointer;
              transition: all var(--transition-fast);

              &:hover {
                background-color: var(--color-primary-50);
                color: var(--color-primary);
              }

              .feather {
                width: 1.2rem;
                height: 1.2rem;
              }
            }
          }

          .form-control {
            border-right: none;
            border-radius: var(--radius-lg) 0 0 var(--radius-lg);

            &:focus + .input-group-append .input-group-text {
              border-color: var(--color-primary);
            }

            &.is-invalid + .input-group-append .input-group-text {
              border-color: var(--color-danger);
            }

            &.is-valid + .input-group-append .input-group-text {
              border-color: var(--color-success);
            }
          }
        }

        // Validation feedback styling
        .invalid-feedback,
        .valid-feedback {
          margin-top: var(--spacing-2);
          font-size: var(--text-xs);

          .d-flex {
            align-items: flex-start;

            .feather {
              width: 1rem;
              height: 1rem;
              margin-top: 0.125rem;
              flex-shrink: 0;
            }

            small {
              line-height: 1.4;
            }
          }
        }

        .invalid-feedback {
          color: var(--color-danger);

          .feather {
            color: var(--color-danger);
          }
        }

        .valid-feedback {
          color: var(--color-success);

          .feather {
            color: var(--color-success);
          }
        }
      }

      // Submit button styling
      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 0.75rem;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.2s ease;

        &:before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover:not(:disabled):before {
          left: 100%;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .spinner-border {
          width: 1rem;
          height: 1rem;
          border-width: 0.125rem;
        }

        .feather {
          width: 1.2rem;
          height: 1.2rem;
        }
      }
    }

    // Alert styling
    .alert {
      border-radius: var(--radius-lg);
      border: none;
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);

      &.alert-danger {
        background: var(--color-danger-50);
        color: var(--color-danger-800);

        .feather {
          color: var(--color-danger);
        }
      }

      &.alert-warning {
        background: var(--color-warning-50);
        color: var(--color-warning-800);

        .feather {
          color: var(--color-warning);
        }
      }

      &.alert-info {
        background: var(--color-info-50);
        color: var(--color-info-800);
        border-left: 4px solid var(--color-info);

        .feather {
          color: var(--color-info);
        }
      }

      .alert-body {
        display: flex;
        align-items: center;
        font-size: var(--text-sm);

        .feather {
          width: 1.2rem;
          height: 1.2rem;
          margin-right: var(--spacing-2);
          flex-shrink: 0;
        }
      }
    }

    // Login attempts counter
    .text-center {
      .text-muted {
        font-size: var(--text-xs);
        color: var(--text-muted);
      }
    }
  }
}

.auth-help-icon {
  position: absolute;
  top: 10px;
  right: 10px;
}
