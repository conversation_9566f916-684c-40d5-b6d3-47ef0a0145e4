using System;
using System.ComponentModel.DataAnnotations;

namespace MskStoreAPI.Models.Security
{
    /// <summary>
    /// Tracks login attempts for rate limiting and security monitoring
    /// </summary>
    public class LoginAttempt
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MaxLength(45)]
        public string IpAddress { get; set; } = string.Empty;

        [Required]
        public DateTime AttemptTime { get; set; } = DateTime.UtcNow;

        public bool IsSuccessful { get; set; }

        [MaxLength(500)]
        public string? UserAgent { get; set; }

        [MaxLength(200)]
        public string? FailureReason { get; set; }

        public int? UserId { get; set; }
    }

    /// <summary>
    /// Account lockout information
    /// </summary>
    public class AccountLockout
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        [Required]
        public DateTime LockoutTime { get; set; } = DateTime.UtcNow;

        public DateTime? UnlockTime { get; set; }

        public int FailedAttempts { get; set; }

        [MaxLength(45)]
        public string? IpAddress { get; set; }

        public bool IsActive { get; set; } = true;

        [MaxLength(200)]
        public string? Reason { get; set; }
    }
}
