// ===================================================================
// Modern Utilities & Animations for MSKStore
// ===================================================================

// ===================================================================
// Responsive Utilities
// ===================================================================
.container-modern {
  width: 100%;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
  margin-left: auto;
  margin-right: auto;
  
  @include respond-to('sm') {
    max-width: 540px;
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
  
  @include respond-to('md') {
    max-width: 720px;
  }
  
  @include respond-to('lg') {
    max-width: 960px;
  }
  
  @include respond-to('xl') {
    max-width: 1140px;
  }
  
  @include respond-to('xxl') {
    max-width: 1320px;
  }
}

// Grid System
.modern-grid {
  display: grid;
  gap: var(--spacing-6);
  
  &.grid-1 { grid-template-columns: 1fr; }
  &.grid-2 { grid-template-columns: repeat(2, 1fr); }
  &.grid-3 { grid-template-columns: repeat(3, 1fr); }
  &.grid-4 { grid-template-columns: repeat(4, 1fr); }
  &.grid-5 { grid-template-columns: repeat(5, 1fr); }
  &.grid-6 { grid-template-columns: repeat(6, 1fr); }
  
  // Responsive grid
  &.grid-responsive {
    grid-template-columns: 1fr;
    
    @include respond-to('sm') {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include respond-to('md') {
      grid-template-columns: repeat(3, 1fr);
    }
    
    @include respond-to('lg') {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  &.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  &.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

// Flexbox Utilities
.flex {
  display: flex;
  
  &.flex-col { flex-direction: column; }
  &.flex-row { flex-direction: row; }
  &.flex-wrap { flex-wrap: wrap; }
  &.flex-nowrap { flex-wrap: nowrap; }
  
  &.items-start { align-items: flex-start; }
  &.items-center { align-items: center; }
  &.items-end { align-items: flex-end; }
  &.items-stretch { align-items: stretch; }
  
  &.justify-start { justify-content: flex-start; }
  &.justify-center { justify-content: center; }
  &.justify-end { justify-content: flex-end; }
  &.justify-between { justify-content: space-between; }
  &.justify-around { justify-content: space-around; }
  &.justify-evenly { justify-content: space-evenly; }
  
  &.gap-1 { gap: var(--spacing-1); }
  &.gap-2 { gap: var(--spacing-2); }
  &.gap-3 { gap: var(--spacing-3); }
  &.gap-4 { gap: var(--spacing-4); }
  &.gap-5 { gap: var(--spacing-5); }
  &.gap-6 { gap: var(--spacing-6); }
  &.gap-8 { gap: var(--spacing-8); }
}

// Spacing Utilities
@each $size in (0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24) {
  .m-#{$size} { margin: var(--spacing-#{$size}); }
  .mt-#{$size} { margin-top: var(--spacing-#{$size}); }
  .mr-#{$size} { margin-right: var(--spacing-#{$size}); }
  .mb-#{$size} { margin-bottom: var(--spacing-#{$size}); }
  .ml-#{$size} { margin-left: var(--spacing-#{$size}); }
  .mx-#{$size} { margin-left: var(--spacing-#{$size}); margin-right: var(--spacing-#{$size}); }
  .my-#{$size} { margin-top: var(--spacing-#{$size}); margin-bottom: var(--spacing-#{$size}); }
  
  .p-#{$size} { padding: var(--spacing-#{$size}); }
  .pt-#{$size} { padding-top: var(--spacing-#{$size}); }
  .pr-#{$size} { padding-right: var(--spacing-#{$size}); }
  .pb-#{$size} { padding-bottom: var(--spacing-#{$size}); }
  .pl-#{$size} { padding-left: var(--spacing-#{$size}); }
  .px-#{$size} { padding-left: var(--spacing-#{$size}); padding-right: var(--spacing-#{$size}); }
  .py-#{$size} { padding-top: var(--spacing-#{$size}); padding-bottom: var(--spacing-#{$size}); }
}

// Text Utilities
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }

.font-thin { font-weight: var(--font-thin); }
.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-danger { color: var(--color-danger); }
.text-info { color: var(--color-info); }

// Background Utilities
.bg-white { background-color: var(--color-white); }
.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }
.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-danger { background-color: var(--color-danger); }
.bg-info { background-color: var(--color-info); }

.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-success { background: var(--gradient-success); }
.bg-gradient-warning { background: var(--gradient-warning); }
.bg-gradient-danger { background: var(--gradient-danger); }
.bg-gradient-info { background: var(--gradient-info); }

// Border Utilities
.border { border: 1px solid var(--border-color); }
.border-0 { border: none; }
.border-t { border-top: 1px solid var(--border-color); }
.border-r { border-right: 1px solid var(--border-color); }
.border-b { border-bottom: 1px solid var(--border-color); }
.border-l { border-left: 1px solid var(--border-color); }

.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

// Shadow Utilities
.shadow-none { box-shadow: var(--shadow-none); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-base); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

// ===================================================================
// Modern Animations
// ===================================================================

// Keyframes
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

// Animation Classes
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

// Hover Animations
.hover-lift {
  transition: transform var(--transition-base), box-shadow var(--transition-base);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}

.hover-scale {
  transition: transform var(--transition-base);
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-rotate {
  transition: transform var(--transition-base);
  
  &:hover {
    transform: rotate(5deg);
  }
}

.hover-glow {
  transition: box-shadow var(--transition-base);
  
  &:hover {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
}

// Loading States
.loading-skeleton {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-gray-200);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

// Stagger Animation for Lists
.stagger-animation {
  > * {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  @for $i from 1 through 20 {
    > *:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

// Page Transition
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.page-transition-leave {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-leave-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}
