// ===================================================================
// Modern Vertical Menu Styles
// ===================================================================

.modern-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6) var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-surface);
  position: relative;

  .modern-menu-brand {
    flex: 1;
    min-width: 0;

    .modern-brand-link {
      display: flex;
      align-items: center;
      text-decoration: none;
      transition: all var(--transition-fast);

      &:hover {
        text-decoration: none;
        transform: translateX(2px);
      }

      .modern-brand-logo {
        flex-shrink: 0;
        margin-right: var(--spacing-3);

        img {
          width: 32px;
          height: 32px;
          border-radius: var(--radius-md);
          box-shadow: var(--shadow-sm);
          transition: all var(--transition-fast);
        }

        &:hover img {
          box-shadow: var(--shadow-md);
          transform: scale(1.05);
        }
      }

      .modern-brand-text {
        font-size: var(--text-lg);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin: 0;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transition: all var(--transition-fast);
        white-space: nowrap;
        overflow: hidden;

        &.collapsed {
          width: 0;
          opacity: 0;
        }
      }
    }
  }

  .modern-menu-toggle {
    flex-shrink: 0;

    .modern-toggle-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      border: none;
      background: var(--color-gray-100);
      border-radius: var(--radius-md);
      color: var(--color-primary);
      cursor: pointer;
      transition: all var(--transition-fast);

      &:hover {
        background: var(--color-primary-100);
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      .modern-toggle-icon {
        width: 1rem;
        height: 1rem;
        transition: transform var(--transition-fast);
      }

      &.modern-close-btn {
        background: var(--color-danger-100);
        color: var(--color-danger);

        &:hover {
          background: var(--color-danger-200);
        }
      }
    }
  }
}

.modern-menu-shadow {
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
  opacity: 0;
  transition: opacity var(--transition-fast);

  &.visible {
    opacity: 1;
  }
}

.modern-menu-content {
  flex: 1;
  overflow: hidden;
  background: var(--bg-surface);

  .modern-navigation {
    height: 100%;

    .modern-navigation-list {
      padding: var(--spacing-4) 0;
      margin: 0;
      list-style: none;

      // Modern menu item styles
      .nav-item {
        margin: 0 var(--spacing-3) var(--spacing-1);

        .nav-link {
          display: flex;
          align-items: center;
          padding: var(--spacing-3) var(--spacing-4);
          border-radius: var(--radius-md);
          color: var(--text-secondary);
          text-decoration: none;
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          transition: all var(--transition-fast);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: var(--color-primary);
            transform: scaleY(0);
            transition: transform var(--transition-fast);
            border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
          }

          &:hover {
            background: var(--color-primary-50);
            color: var(--color-primary);
            text-decoration: none;
            transform: translateX(2px);

            &::before {
              transform: scaleY(1);
            }
          }

          &.active {
            background: var(--color-primary-100);
            color: var(--color-primary);

            &::before {
              transform: scaleY(1);
            }
          }
        }
      }
    }
  }
}