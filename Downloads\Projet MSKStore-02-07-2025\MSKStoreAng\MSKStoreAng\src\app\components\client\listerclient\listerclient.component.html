<div class="float-right mr-3 my-2">
    <label>{{ 'Show' | translate }}</label>
    <select class="form-control" [(ngModel)]="pageSize" (change)="paginate($event)">
      <option value="5">5</option>
      <option value="10">10</option>
      <option value="20">20</option>
    </select>
</div>


<div class="modern-table-container">
  <div class="modern-table-header">
    <h3 class="table-title">
      <i data-feather="users" class="title-icon"></i>
      {{ 'Client List' | translate }}
    </h3>
    <div class="table-actions">
      <button class="btn-modern btn-primary btn-sm" routerLink="/client/ajouter">
        <i data-feather="plus" class="btn-icon"></i>
        {{ 'Add Client' | translate }}
      </button>
    </div>
  </div>

  <div class="table-responsive">
    <table class="table modern-table">
      <thead>
        <tr>
          <th>
            <i data-feather="hash" class="th-icon"></i>
            #
          </th>
          <th>
            <i data-feather="user" class="th-icon"></i>
            {{ 'firstName' | translate }}
          </th>
          <th>
            <i data-feather="user" class="th-icon"></i>
            {{ 'lastName' | translate }}
          </th>
          <th>
            <i data-feather="flag" class="th-icon"></i>
            {{ 'Priority' | translate }}
          </th>
          <th>
            <i data-feather="users" class="th-icon"></i>
            {{ 'Gender' | translate }}
          </th>
          <th>
            <i data-feather="phone" class="th-icon"></i>
            {{ 'Phone' | translate }}
          </th>
          <th>
            <i data-feather="mail" class="th-icon"></i>
            {{ 'Email' | translate }}
          </th>
          <th>
            <i data-feather="map-pin" class="th-icon"></i>
            {{ 'Address' | translate }}
          </th>
          <th>
            <i data-feather="credit-card" class="th-icon"></i>
            {{ 'Credit' | translate }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let client of clients | paginate: { itemsPerPage: pageSize, currentPage: currentPage }"
            class="modern-table-row">
          <td>
            <a [routerLink]="['/afficherclient', client.id]" class="client-id-link">
              <span class="id-badge">{{client.id}}</span>
            </a>
          </td>
          <td>
            <span class="client-name">{{ client.name }}</span>
          </td>
          <td>
            <span class="client-name">{{ client.lastname }}</span>
          </td>
          <td>
            <span class="priority-badge" [ngClass]="'priority-' + client.priority">
              {{ client.priority }}
            </span>
          </td>
          <td>
            <span class="gender-badge" [ngClass]="'gender-' + client.gender">
              <i [data-feather]="client.gender === 'male' ? 'user' : 'user'" class="gender-icon"></i>
              {{ client.gender }}
            </span>
          </td>
          <td>
            <span class="contact-info">{{ client.phone }}</span>
          </td>
          <td>
            <span class="contact-info">{{ client.email }}</span>
          </td>
          <td>
            <span class="address-text">{{ client.adresse }}</span>
          </td>
          <td>
            <span class="credit-amount">{{ client.credit | currency }}</span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>


  <div class="modern-pagination-container">
    <div class="pagination-info">
      <span class="pagination-text">
        Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, clients.length) }}
        of {{ clients.length }} clients
      </span>
    </div>
    <div class="modern-pagination">
      <pagination-controls
        (pageChange)="currentPage = $event"
        previousLabel="Previous"
        nextLabel="Next"
        class="modern-pagination-controls">
      </pagination-controls>
    </div>
  </div>
  
  


