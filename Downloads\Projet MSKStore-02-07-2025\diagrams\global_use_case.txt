@startuml
left to right direction

actor admin
actor employ<PERSON>

rectangle "Système de Gestion de Magasin" {
  usecase "s'authentifier" as <PERSON>_Auth
  
  admin -- (<PERSON><PERSON><PERSON> les utilisateurs)
  admin -- (<PERSON><PERSON><PERSON>rer un rapport)
  admin -- (Modifier timbre fiscal)
  admin -- (<PERSON><PERSON><PERSON> son profil)
  
  employé -- (<PERSON><PERSON><PERSON> la caisse)
  employé -- (Gérer les factures)
  employé -- (<PERSON><PERSON>rer les crédits)
  employé -- (Gérer les clients)
  employé -- (Gérer la fermeture de caisse)
  employé -- (G<PERSON>rer les types ticket resto)
  employé -- (Gérer les produits)
  employé -- (Gérer les catégories)
  employé -- (Gérer les dépôts)
  employé -- (<PERSON><PERSON>rer les fournisseurs)
  employé -- (Gérer les achats fournisseur)
  
  (Gérer les utilisateurs) .> UC_Auth : <<include>>
  (Générer un rapport) .> UC_Auth : <<include>>
  (Modifier timbre fiscal) .> UC_Auth : <<include>>
  (<PERSON><PERSON><PERSON> son profil) .> UC_Auth : <<include>>
  (<PERSON><PERSON><PERSON> la caisse) .> UC_Auth : <<include>>
  (G<PERSON>rer les factures) .> UC_Auth : <<include>>
  (Gérer les crédits) .> UC_Auth : <<include>>
  (Gérer les clients) .> UC_Auth : <<include>>
  (Gérer la fermeture de caisse) .> UC_Auth : <<include>>
  (Gérer les types ticket resto) .> UC_Auth : <<include>>
  (Gérer les produits) .> UC_Auth : <<include>>
  (Gérer les catégories) .> UC_Auth : <<include>>
  (Gérer les dépôts) .> UC_Auth : <<include>>
  (Gérer les fournisseurs) .> UC_Auth : <<include>>
  (Gérer les achats fournisseur) .> UC_Auth : <<include>>
}
@enduml

