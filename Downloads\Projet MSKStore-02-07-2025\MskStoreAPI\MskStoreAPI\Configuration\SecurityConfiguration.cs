namespace MskStoreAPI.Configuration
{
    /// <summary>
    /// Centralized security configuration settings
    /// </summary>
    public class SecurityConfiguration
    {
        public const string SectionName = "SecuritySettings";

        /// <summary>
        /// JWT Token settings
        /// </summary>
        public JwtSettings Jwt { get; set; } = new();

        /// <summary>
        /// Password policy settings
        /// </summary>
        public PasswordPolicy PasswordPolicy { get; set; } = new();

        /// <summary>
        /// Account lockout settings
        /// </summary>
        public AccountLockoutSettings AccountLockout { get; set; } = new();

        /// <summary>
        /// Rate limiting settings
        /// </summary>
        public RateLimitingSettings RateLimiting { get; set; } = new();

        /// <summary>
        /// Session management settings
        /// </summary>
        public SessionSettings Session { get; set; } = new();

        /// <summary>
        /// Encryption settings
        /// </summary>
        public EncryptionSettings Encryption { get; set; } = new();

        /// <summary>
        /// Security monitoring settings
        /// </summary>
        public MonitoringSettings Monitoring { get; set; } = new();

        /// <summary>
        /// Content Security Policy settings
        /// </summary>
        public CspSettings ContentSecurityPolicy { get; set; } = new();

        /// <summary>
        /// HTTPS and TLS settings
        /// </summary>
        public HttpsSettings Https { get; set; } = new();
    }

    public class JwtSettings
    {
        /// <summary>
        /// JWT secret key (should be at least 32 characters for AES-256)
        /// </summary>
        public string SecretKey { get; set; } = "MSKStore_SecureKey_2024_AES256_CompliantKey_ForProduction_Use_Only_32Chars";

        /// <summary>
        /// JWT token issuer
        /// </summary>
        public string Issuer { get; set; } = "MSKStore_API";

        /// <summary>
        /// JWT token audience
        /// </summary>
        public string Audience { get; set; } = "MSKStore_Client";

        /// <summary>
        /// Token expiration time in minutes
        /// </summary>
        public int ExpirationMinutes { get; set; } = 480; // 8 hours

        /// <summary>
        /// Refresh token expiration time in days
        /// </summary>
        public int RefreshTokenExpirationDays { get; set; } = 7;

        /// <summary>
        /// Clock skew tolerance in minutes
        /// </summary>
        public int ClockSkewMinutes { get; set; } = 5;

        /// <summary>
        /// Require HTTPS for JWT tokens
        /// </summary>
        public bool RequireHttps { get; set; } = true;
    }

    public class PasswordPolicy
    {
        /// <summary>
        /// Minimum password length
        /// </summary>
        public int MinimumLength { get; set; } = 12;

        /// <summary>
        /// Maximum password length
        /// </summary>
        public int MaximumLength { get; set; } = 128;

        /// <summary>
        /// Require uppercase letters
        /// </summary>
        public bool RequireUppercase { get; set; } = true;

        /// <summary>
        /// Require lowercase letters
        /// </summary>
        public bool RequireLowercase { get; set; } = true;

        /// <summary>
        /// Require digits
        /// </summary>
        public bool RequireDigits { get; set; } = true;

        /// <summary>
        /// Require special characters
        /// </summary>
        public bool RequireSpecialCharacters { get; set; } = true;

        /// <summary>
        /// Number of previous passwords to remember
        /// </summary>
        public int PasswordHistoryCount { get; set; } = 5;

        /// <summary>
        /// Password expiration days (0 = never expires)
        /// </summary>
        public int ExpirationDays { get; set; } = 90;

        /// <summary>
        /// Minimum password age in hours
        /// </summary>
        public int MinimumAgeHours { get; set; } = 24;
    }

    public class AccountLockoutSettings
    {
        /// <summary>
        /// Maximum failed login attempts before lockout
        /// </summary>
        public int MaxFailedAttempts { get; set; } = 5;

        /// <summary>
        /// Lockout duration in minutes
        /// </summary>
        public int LockoutDurationMinutes { get; set; } = 30;

        /// <summary>
        /// Time window for failed attempts in minutes
        /// </summary>
        public int FailedAttemptWindowMinutes { get; set; } = 15;

        /// <summary>
        /// Enable progressive lockout (increasing lockout time)
        /// </summary>
        public bool EnableProgressiveLockout { get; set; } = true;

        /// <summary>
        /// Maximum lockout duration in hours
        /// </summary>
        public int MaxLockoutHours { get; set; } = 24;
    }

    public class RateLimitingSettings
    {
        /// <summary>
        /// Enable rate limiting
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Maximum requests per minute per IP
        /// </summary>
        public int RequestsPerMinute { get; set; } = 60;

        /// <summary>
        /// Maximum login attempts per minute per IP
        /// </summary>
        public int LoginAttemptsPerMinute { get; set; } = 5;

        /// <summary>
        /// Rate limit window in minutes
        /// </summary>
        public int WindowMinutes { get; set; } = 1;

        /// <summary>
        /// Whitelist of IP addresses exempt from rate limiting
        /// </summary>
        public List<string> WhitelistedIps { get; set; } = new();
    }

    public class SessionSettings
    {
        /// <summary>
        /// Session timeout in minutes
        /// </summary>
        public int TimeoutMinutes { get; set; } = 480; // 8 hours

        /// <summary>
        /// Idle timeout in minutes
        /// </summary>
        public int IdleTimeoutMinutes { get; set; } = 60;

        /// <summary>
        /// Enable concurrent session limit
        /// </summary>
        public bool EnableConcurrentSessionLimit { get; set; } = true;

        /// <summary>
        /// Maximum concurrent sessions per user
        /// </summary>
        public int MaxConcurrentSessions { get; set; } = 3;

        /// <summary>
        /// Require session validation on each request
        /// </summary>
        public bool ValidateOnEachRequest { get; set; } = true;
    }

    public class EncryptionSettings
    {
        /// <summary>
        /// AES encryption key size (128, 192, or 256)
        /// </summary>
        public int KeySize { get; set; } = 256;

        /// <summary>
        /// Encryption algorithm
        /// </summary>
        public string Algorithm { get; set; } = "AES-256-GCM";

        /// <summary>
        /// Key derivation iterations
        /// </summary>
        public int KeyDerivationIterations { get; set; } = 100000;

        /// <summary>
        /// Salt size in bytes
        /// </summary>
        public int SaltSize { get; set; } = 32;

        /// <summary>
        /// IV size in bytes
        /// </summary>
        public int IvSize { get; set; } = 16;
    }

    public class MonitoringSettings
    {
        /// <summary>
        /// Enable security monitoring
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Log all security events
        /// </summary>
        public bool LogAllEvents { get; set; } = true;

        /// <summary>
        /// Enable real-time alerting
        /// </summary>
        public bool EnableRealTimeAlerts { get; set; } = true;

        /// <summary>
        /// Alert thresholds
        /// </summary>
        public AlertThresholds Thresholds { get; set; } = new();

        /// <summary>
        /// Event retention days
        /// </summary>
        public int EventRetentionDays { get; set; } = 90;

        /// <summary>
        /// Enable automated threat response
        /// </summary>
        public bool EnableAutomatedResponse { get; set; } = true;
    }

    public class AlertThresholds
    {
        /// <summary>
        /// Failed login attempts threshold per hour
        /// </summary>
        public int FailedLoginsPerHour { get; set; } = 50;

        /// <summary>
        /// Critical events threshold per hour
        /// </summary>
        public int CriticalEventsPerHour { get; set; } = 10;

        /// <summary>
        /// Suspicious activity threshold per hour
        /// </summary>
        public int SuspiciousActivityPerHour { get; set; } = 25;

        /// <summary>
        /// High severity events threshold per day
        /// </summary>
        public int HighSeverityEventsPerDay { get; set; } = 100;
    }

    public class CspSettings
    {
        /// <summary>
        /// Enable Content Security Policy
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// CSP report-only mode
        /// </summary>
        public bool ReportOnly { get; set; } = false;

        /// <summary>
        /// Default source directive
        /// </summary>
        public string DefaultSrc { get; set; } = "'self'";

        /// <summary>
        /// Script source directive
        /// </summary>
        public string ScriptSrc { get; set; } = "'self' 'unsafe-inline' 'unsafe-eval'";

        /// <summary>
        /// Style source directive
        /// </summary>
        public string StyleSrc { get; set; } = "'self' 'unsafe-inline'";

        /// <summary>
        /// Image source directive
        /// </summary>
        public string ImgSrc { get; set; } = "'self' data: https:";

        /// <summary>
        /// Font source directive
        /// </summary>
        public string FontSrc { get; set; } = "'self' https: data:";

        /// <summary>
        /// Connect source directive
        /// </summary>
        public string ConnectSrc { get; set; } = "'self'";

        /// <summary>
        /// Frame ancestors directive
        /// </summary>
        public string FrameAncestors { get; set; } = "'none'";

        /// <summary>
        /// Base URI directive
        /// </summary>
        public string BaseUri { get; set; } = "'self'";

        /// <summary>
        /// Form action directive
        /// </summary>
        public string FormAction { get; set; } = "'self'";
    }

    public class HttpsSettings
    {
        /// <summary>
        /// Enforce HTTPS
        /// </summary>
        public bool EnforceHttps { get; set; } = true;

        /// <summary>
        /// HSTS max age in seconds
        /// </summary>
        public int HstsMaxAge { get; set; } = 31536000; // 1 year

        /// <summary>
        /// Include HSTS subdomains
        /// </summary>
        public bool HstsIncludeSubdomains { get; set; } = true;

        /// <summary>
        /// HSTS preload
        /// </summary>
        public bool HstsPreload { get; set; } = true;

        /// <summary>
        /// Minimum TLS version
        /// </summary>
        public string MinimumTlsVersion { get; set; } = "1.2";

        /// <summary>
        /// Allowed cipher suites
        /// </summary>
        public List<string> AllowedCipherSuites { get; set; } = new()
        {
            "TLS_AES_256_GCM_SHA384",
            "TLS_CHACHA20_POLY1305_SHA256",
            "TLS_AES_128_GCM_SHA256"
        };
    }
}
