<!-- Skip to main content link for screen readers -->
<a href="#main-content" class="skip-link">Skip to main content</a>

<!-- Notification component -->
<app-notification></app-notification>

<!-- Loading overlay -->
<app-loading></app-loading>

<!-- vertical-layout -->
<ng-container *ngIf="coreConfig.layout.type === 'vertical'">
  <vertical-layout></vertical-layout>
</ng-container>
<!-- / vertical-layout -->

<!-- horizontal-layout -->
<ng-container *ngIf="coreConfig.layout.type === 'horizontal'">
  <horizontal-layout></horizontal-layout>
</ng-container>
<!-- / horizontal-layout -->

<!-- theme customizer -->
<core-sidebar
  name="themeCustomizer"
  class="customizer d-none d-md-block"
  *ngIf="coreConfig.layout.customizer"
  role="complementary"
  aria-label="Theme customizer"
>
  <button
    type="button"
    class="customizer-toggle d-flex align-items-center justify-content-center"
    (click)="toggleSidebar('themeCustomizer')"
    aria-label="Open theme customizer"
    title="Customize theme settings"
  >
    <span [data-feather]="'settings'" [class]="'spinner'" aria-hidden="true"></span>
  </button>
  <core-theme-customizer></core-theme-customizer>
</core-sidebar>
<!-- / theme customizer -->
