using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MskStoreAPI.Data;
using MskStoreAPI.Models.Security;
using System.Text.Json;

namespace MskStoreAPI.Services
{
    /// <summary>
    /// Service for comprehensive security monitoring and alerting
    /// </summary>
    public interface ISecurityMonitoringService
    {
        Task<SecurityDashboardData> GetSecurityDashboardAsync();
        Task<List<SecurityAlert>> GetActiveAlertsAsync();
        Task ProcessSecurityEventAsync(SecurityAuditLog securityEvent);
        Task<SecurityReport> GenerateSecurityReportAsync(DateTime startDate, DateTime endDate);
        Task<bool> CheckForSuspiciousActivityAsync(string ipAddress, string userAgent);
        Task SendSecurityAlertAsync(SecurityAlert alert);
    }

    public class SecurityMonitoringService : ISecurityMonitoringService
    {
        private readonly DataContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SecurityMonitoringService> _logger;
        private readonly ISecurityService _securityService;

        public SecurityMonitoringService(
            DataContext context,
            IConfiguration configuration,
            ILogger<SecurityMonitoringService> logger,
            ISecurityService securityService)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
            _securityService = securityService;
        }

        public async Task<SecurityDashboardData> GetSecurityDashboardAsync()
        {
            try
            {
                var now = DateTime.UtcNow;
                var last24Hours = now.AddHours(-24);
                var last7Days = now.AddDays(-7);
                var last30Days = now.AddDays(-30);

                var dashboard = new SecurityDashboardData
                {
                    // Event counts by time period
                    EventsLast24Hours = await _context.SecurityAuditLogs
                        .Where(e => e.Timestamp >= last24Hours)
                        .CountAsync(),
                    
                    EventsLast7Days = await _context.SecurityAuditLogs
                        .Where(e => e.Timestamp >= last7Days)
                        .CountAsync(),
                    
                    EventsLast30Days = await _context.SecurityAuditLogs
                        .Where(e => e.Timestamp >= last30Days)
                        .CountAsync(),

                    // Critical events
                    CriticalEventsLast24Hours = await _context.SecurityAuditLogs
                        .Where(e => e.Timestamp >= last24Hours && e.Severity == "CRITICAL")
                        .CountAsync(),

                    // Failed login attempts
                    FailedLoginsLast24Hours = await _context.LoginAttempts
                        .Where(la => la.AttemptTime >= last24Hours && !la.IsSuccessful)
                        .CountAsync(),

                    // Active lockouts
                    ActiveAccountLockouts = await _context.AccountLockouts
                        .Where(al => al.IsActive && al.UnlockTime > now)
                        .CountAsync(),

                    // Top event types
                    TopEventTypes = await _context.SecurityAuditLogs
                        .Where(e => e.Timestamp >= last7Days)
                        .GroupBy(e => e.EventType)
                        .Select(g => new EventTypeCount
                        {
                            EventType = g.Key,
                            Count = g.Count()
                        })
                        .OrderByDescending(etc => etc.Count)
                        .Take(10)
                        .ToListAsync(),

                    // Top IP addresses
                    TopIpAddresses = await _context.SecurityAuditLogs
                        .Where(e => e.Timestamp >= last7Days && !string.IsNullOrEmpty(e.IpAddress))
                        .GroupBy(e => e.IpAddress)
                        .Select(g => new IpAddressCount
                        {
                            IpAddress = g.Key,
                            Count = g.Count()
                        })
                        .OrderByDescending(iac => iac.Count)
                        .Take(10)
                        .ToListAsync(),

                    // Recent critical events
                    RecentCriticalEvents = await _context.SecurityAuditLogs
                        .Where(e => e.Severity == "CRITICAL" || e.Severity == "HIGH")
                        .OrderByDescending(e => e.Timestamp)
                        .Take(20)
                        .ToListAsync(),

                    GeneratedAt = now
                };

                return dashboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating security dashboard");
                throw;
            }
        }

        public async Task<List<SecurityAlert>> GetActiveAlertsAsync()
        {
            try
            {
                var alerts = new List<SecurityAlert>();
                var now = DateTime.UtcNow;
                var last24Hours = now.AddHours(-24);

                // Check for multiple failed login attempts
                var suspiciousIps = await _context.LoginAttempts
                    .Where(la => la.AttemptTime >= last24Hours && !la.IsSuccessful)
                    .GroupBy(la => la.IpAddress)
                    .Where(g => g.Count() >= 10)
                    .Select(g => new { IpAddress = g.Key, Count = g.Count() })
                    .ToListAsync();

                foreach (var ip in suspiciousIps)
                {
                    alerts.Add(new SecurityAlert
                    {
                        Id = Guid.NewGuid(),
                        Type = "SUSPICIOUS_LOGIN_ATTEMPTS",
                        Severity = "HIGH",
                        Title = "Tentatives de connexion suspectes",
                        Description = $"L'adresse IP {ip.IpAddress} a effectué {ip.Count} tentatives de connexion échouées dans les dernières 24 heures",
                        IpAddress = ip.IpAddress,
                        CreatedAt = now,
                        IsActive = true
                    });
                }

                // Check for unusual activity patterns
                var unusualActivity = await DetectUnusualActivityAsync();
                alerts.AddRange(unusualActivity);

                // Check for potential security breaches
                var breachIndicators = await DetectBreachIndicatorsAsync();
                alerts.AddRange(breachIndicators);

                return alerts.Where(a => a.IsActive).OrderByDescending(a => a.CreatedAt).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active security alerts");
                return new List<SecurityAlert>();
            }
        }

        public async Task ProcessSecurityEventAsync(SecurityAuditLog securityEvent)
        {
            try
            {
                // Analyze the event for potential threats
                var alerts = await AnalyzeSecurityEventAsync(securityEvent);
                
                foreach (var alert in alerts)
                {
                    await SendSecurityAlertAsync(alert);
                }

                // Update threat intelligence
                await UpdateThreatIntelligenceAsync(securityEvent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing security event: {EventType}", securityEvent.EventType);
            }
        }

        public async Task<SecurityReport> GenerateSecurityReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var events = await _context.SecurityAuditLogs
                    .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
                    .ToListAsync();

                var loginAttempts = await _context.LoginAttempts
                    .Where(la => la.AttemptTime >= startDate && la.AttemptTime <= endDate)
                    .ToListAsync();

                var lockouts = await _context.AccountLockouts
                    .Where(al => al.LockoutTime >= startDate && al.LockoutTime <= endDate)
                    .ToListAsync();

                var report = new SecurityReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    GeneratedAt = DateTime.UtcNow,
                    
                    TotalEvents = events.Count,
                    CriticalEvents = events.Count(e => e.Severity == "CRITICAL"),
                    HighSeverityEvents = events.Count(e => e.Severity == "HIGH"),
                    MediumSeverityEvents = events.Count(e => e.Severity == "MEDIUM"),
                    LowSeverityEvents = events.Count(e => e.Severity == "LOW"),
                    
                    TotalLoginAttempts = loginAttempts.Count,
                    SuccessfulLogins = loginAttempts.Count(la => la.IsSuccessful),
                    FailedLogins = loginAttempts.Count(la => !la.IsSuccessful),
                    
                    AccountLockouts = lockouts.Count,
                    
                    TopEventTypes = events
                        .GroupBy(e => e.EventType)
                        .Select(g => new EventTypeCount { EventType = g.Key, Count = g.Count() })
                        .OrderByDescending(etc => etc.Count)
                        .Take(10)
                        .ToList(),
                    
                    TopIpAddresses = events
                        .Where(e => !string.IsNullOrEmpty(e.IpAddress))
                        .GroupBy(e => e.IpAddress)
                        .Select(g => new IpAddressCount { IpAddress = g.Key, Count = g.Count() })
                        .OrderByDescending(iac => iac.Count)
                        .Take(10)
                        .ToList(),
                    
                    SecurityTrends = await CalculateSecurityTrendsAsync(startDate, endDate),
                    Recommendations = await GenerateSecurityRecommendationsAsync(events, loginAttempts, lockouts)
                };

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating security report");
                throw;
            }
        }

        public async Task<bool> CheckForSuspiciousActivityAsync(string ipAddress, string userAgent)
        {
            try
            {
                var now = DateTime.UtcNow;
                var last1Hour = now.AddHours(-1);

                // Check for rapid requests from same IP
                var recentRequests = await _context.SecurityAuditLogs
                    .Where(e => e.IpAddress == ipAddress && e.Timestamp >= last1Hour)
                    .CountAsync();

                if (recentRequests > 100) // Configurable threshold
                {
                    await _securityService.LogSecurityEventAsync(
                        "SUSPICIOUS_ACTIVITY_DETECTED",
                        $"High request volume from IP {ipAddress}: {recentRequests} requests in last hour",
                        null,
                        ipAddress,
                        userAgent,
                        "HIGH",
                        false
                    );
                    return true;
                }

                // Check for known malicious patterns
                var maliciousPatterns = new[]
                {
                    "sqlmap", "nikto", "nmap", "burp", "owasp zap", "metasploit"
                };

                if (!string.IsNullOrEmpty(userAgent) && 
                    maliciousPatterns.Any(pattern => userAgent.ToLowerInvariant().Contains(pattern)))
                {
                    await _securityService.LogSecurityEventAsync(
                        "MALICIOUS_USER_AGENT_DETECTED",
                        $"Malicious user agent detected: {userAgent}",
                        null,
                        ipAddress,
                        userAgent,
                        "CRITICAL",
                        false
                    );
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for suspicious activity");
                return false;
            }
        }

        public async Task SendSecurityAlertAsync(SecurityAlert alert)
        {
            try
            {
                // Log the alert
                _logger.LogWarning("Security Alert: {Type} - {Title}", alert.Type, alert.Title);

                // In a real implementation, you would send notifications via:
                // - Email
                // - SMS
                // - Slack/Teams
                // - Security Information and Event Management (SIEM) systems
                
                // For now, we'll just log it
                await _securityService.LogSecurityEventAsync(
                    "SECURITY_ALERT_GENERATED",
                    $"Security alert generated: {alert.Title} - {alert.Description}",
                    null,
                    alert.IpAddress,
                    null,
                    alert.Severity,
                    false,
                    JsonSerializer.Serialize(alert)
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending security alert");
            }
        }

        private async Task<List<SecurityAlert>> AnalyzeSecurityEventAsync(SecurityAuditLog securityEvent)
        {
            var alerts = new List<SecurityAlert>();

            // Analyze based on event type and severity
            if (securityEvent.Severity == "CRITICAL")
            {
                alerts.Add(new SecurityAlert
                {
                    Id = Guid.NewGuid(),
                    Type = "CRITICAL_SECURITY_EVENT",
                    Severity = "CRITICAL",
                    Title = "Événement de sécurité critique détecté",
                    Description = $"Événement critique: {securityEvent.EventType} - {securityEvent.Description}",
                    IpAddress = securityEvent.IpAddress,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                });
            }

            return alerts;
        }

        private async Task UpdateThreatIntelligenceAsync(SecurityAuditLog securityEvent)
        {
            // Update threat intelligence based on the security event
            // This could include updating IP reputation, user behavior patterns, etc.
            await Task.CompletedTask;
        }

        private async Task<List<SecurityAlert>> DetectUnusualActivityAsync()
        {
            var alerts = new List<SecurityAlert>();
            // Implement unusual activity detection logic
            return alerts;
        }

        private async Task<List<SecurityAlert>> DetectBreachIndicatorsAsync()
        {
            var alerts = new List<SecurityAlert>();
            // Implement breach indicator detection logic
            return alerts;
        }

        private async Task<List<SecurityTrend>> CalculateSecurityTrendsAsync(DateTime startDate, DateTime endDate)
        {
            // Calculate security trends over time
            return new List<SecurityTrend>();
        }

        private async Task<List<string>> GenerateSecurityRecommendationsAsync(
            List<SecurityAuditLog> events, 
            List<LoginAttempt> loginAttempts, 
            List<AccountLockout> lockouts)
        {
            var recommendations = new List<string>();

            if (events.Count(e => e.Severity == "CRITICAL") > 10)
            {
                recommendations.Add("Nombre élevé d'événements critiques détectés. Examiner les logs de sécurité.");
            }

            if (loginAttempts.Count(la => !la.IsSuccessful) > loginAttempts.Count(la => la.IsSuccessful))
            {
                recommendations.Add("Plus d'échecs de connexion que de succès. Vérifier les tentatives d'intrusion.");
            }

            return recommendations;
        }
    }

    // Data models for security monitoring
    public class SecurityDashboardData
    {
        public int EventsLast24Hours { get; set; }
        public int EventsLast7Days { get; set; }
        public int EventsLast30Days { get; set; }
        public int CriticalEventsLast24Hours { get; set; }
        public int FailedLoginsLast24Hours { get; set; }
        public int ActiveAccountLockouts { get; set; }
        public List<EventTypeCount> TopEventTypes { get; set; } = new();
        public List<IpAddressCount> TopIpAddresses { get; set; } = new();
        public List<SecurityAuditLog> RecentCriticalEvents { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
    }

    public class EventTypeCount
    {
        public string EventType { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class IpAddressCount
    {
        public string IpAddress { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class SecurityAlert
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? IpAddress { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; }
    }

    public class SecurityReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public int TotalEvents { get; set; }
        public int CriticalEvents { get; set; }
        public int HighSeverityEvents { get; set; }
        public int MediumSeverityEvents { get; set; }
        public int LowSeverityEvents { get; set; }
        public int TotalLoginAttempts { get; set; }
        public int SuccessfulLogins { get; set; }
        public int FailedLogins { get; set; }
        public int AccountLockouts { get; set; }
        public List<EventTypeCount> TopEventTypes { get; set; } = new();
        public List<IpAddressCount> TopIpAddresses { get; set; } = new();
        public List<SecurityTrend> SecurityTrends { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    public class SecurityTrend
    {
        public DateTime Date { get; set; }
        public int EventCount { get; set; }
        public int CriticalEventCount { get; set; }
    }
}
