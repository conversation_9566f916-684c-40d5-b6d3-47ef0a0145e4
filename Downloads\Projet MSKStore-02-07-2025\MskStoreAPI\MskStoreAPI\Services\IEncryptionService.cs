using System;

namespace MskStoreAPI.Services
{
    /// <summary>
    /// Interface for encryption and decryption services
    /// </summary>
    public interface IEncryptionService
    {
        /// <summary>
        /// Encrypts a string using AES-256 encryption
        /// </summary>
        /// <param name="plainText">The text to encrypt</param>
        /// <returns>Base64 encoded encrypted string</returns>
        string Encrypt(string plainText);

        /// <summary>
        /// Decrypts a string using AES-256 encryption
        /// </summary>
        /// <param name="cipherText">Base64 encoded encrypted string</param>
        /// <returns>Decrypted plain text</returns>
        string Decrypt(string cipherText);

        /// <summary>
        /// Encrypts sensitive data like emails, phone numbers
        /// </summary>
        /// <param name="sensitiveData">The sensitive data to encrypt</param>
        /// <returns>Encrypted data</returns>
        string EncryptSensitiveData(string sensitiveData);

        /// <summary>
        /// Decrypts sensitive data like emails, phone numbers
        /// </summary>
        /// <param name="encryptedData">The encrypted data to decrypt</param>
        /// <returns>Decrypted sensitive data</returns>
        string DecryptSensitiveData(string encryptedData);

        /// <summary>
        /// Encrypts financial data like prices, amounts
        /// </summary>
        /// <param name="amount">The financial amount to encrypt</param>
        /// <returns>Encrypted amount</returns>
        string EncryptFinancialData(decimal amount);

        /// <summary>
        /// Decrypts financial data like prices, amounts
        /// </summary>
        /// <param name="encryptedAmount">The encrypted amount to decrypt</param>
        /// <returns>Decrypted amount</returns>
        decimal DecryptFinancialData(string encryptedAmount);
    }
}
