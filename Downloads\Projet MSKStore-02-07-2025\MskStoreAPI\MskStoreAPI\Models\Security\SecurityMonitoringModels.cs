using System.ComponentModel.DataAnnotations;

namespace MskStoreAPI.Models.Security
{
    /// <summary>
    /// Security dashboard data model
    /// </summary>
    public class SecurityDashboardData
    {
        public int TotalEvents { get; set; }
        public int CriticalEvents { get; set; }
        public int HighSeverityEvents { get; set; }
        public int MediumSeverityEvents { get; set; }
        public int LowSeverityEvents { get; set; }
        public int TotalLogins { get; set; }
        public int SuccessfulLogins { get; set; }
        public int FailedLogins { get; set; }
        public int ActiveLockouts { get; set; }
        public List<SecurityAuditLog> RecentEvents { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Security alert model
    /// </summary>
    public class SecurityAlert
    {
        public int Id { get; set; }
        public string EventType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? IpAddress { get; set; }
        public string? UserId { get; set; }
        public bool IsResolved { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public string? ResolvedBy { get; set; }
        public string? Resolution { get; set; }
    }

    /// <summary>
    /// Security report model
    /// </summary>
    public class SecurityReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalEvents { get; set; }
        public int CriticalEvents { get; set; }
        public int HighSeverityEvents { get; set; }
        public int MediumSeverityEvents { get; set; }
        public int LowSeverityEvents { get; set; }
        public int TotalLoginAttempts { get; set; }
        public int SuccessfulLogins { get; set; }
        public int FailedLogins { get; set; }
        public int AccountLockouts { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<EventTypeCount> EventsByType { get; set; } = new();
        public List<IpAddressActivity> TopIpAddresses { get; set; } = new();
        public List<DailyActivity> DailyActivity { get; set; } = new();
    }

    /// <summary>
    /// Event type count for reports
    /// </summary>
    public class EventTypeCount
    {
        public string EventType { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    /// <summary>
    /// IP address activity for reports
    /// </summary>
    public class IpAddressActivity
    {
        public string IpAddress { get; set; } = string.Empty;
        public int Count { get; set; }
        public int FailedAttempts { get; set; }
        public int SuccessfulAttempts { get; set; }
    }

    /// <summary>
    /// Daily activity for reports
    /// </summary>
    public class DailyActivity
    {
        public DateTime Date { get; set; }
        public int Count { get; set; }
        public int CriticalEvents { get; set; }
        public int HighSeverityEvents { get; set; }
    }

    /// <summary>
    /// Security status model
    /// </summary>
    public class SecurityStatus
    {
        public string OverallStatus { get; set; } = string.Empty;
        public DateTime LastSecurityScan { get; set; }
        public int ActiveThreats { get; set; }
        public TimeSpan SystemUptime { get; set; }
        public string SecurityLevel { get; set; } = string.Empty;
        public List<string> Recommendations { get; set; } = new();
        public List<SecurityMetric> Metrics { get; set; } = new();
    }

    /// <summary>
    /// Security metric for status monitoring
    /// </summary>
    public class SecurityMetric
    {
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime LastChecked { get; set; }
    }

    /// <summary>
    /// Security scan result model
    /// </summary>
    public class SecurityScanResult
    {
        public Guid ScanId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Status { get; set; } = string.Empty;
        public int ThreatsFound { get; set; }
        public int VulnerabilitiesFound { get; set; }
        public List<string> Recommendations { get; set; } = new();
        public List<SecurityFinding> Findings { get; set; } = new();
        public List<SecurityScanDetail> Details { get; set; } = new();
    }

    /// <summary>
    /// Security finding from scans
    /// </summary>
    public class SecurityFinding
    {
        public string Type { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? IpAddress { get; set; }
        public string? Email { get; set; }
        public int Count { get; set; }
        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }
    }

    /// <summary>
    /// Paginated result wrapper
    /// </summary>
    public class PaginatedResult<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// Security scan detail for comprehensive scan results
    /// </summary>
    public class SecurityScanDetail
    {
        public string Category { get; set; } = string.Empty;
        public string CheckName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // PASS, WARNING, CRITICAL
        public string Details { get; set; } = string.Empty;
    }
}
