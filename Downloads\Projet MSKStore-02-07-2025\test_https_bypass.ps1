# Bypass SSL certificate validation
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12

$body = @{
    name = "Test"
    lastName = "User"
    email = "<EMAIL>"
    password = "Test123!"
    phone = "1234567890"
    role = "Admin"
} | ConvertTo-Json

try {
    Write-Host "Testing HTTPS endpoint with certificate bypass..."
    $response = Invoke-RestMethod -Uri "https://localhost:7258/api/Users" -Method POST -ContentType "application/json" -Body $body
    Write-Host "SUCCESS! User created:"
    Write-Host ($response | ConvertTo-Json)
} catch {
    Write-Host "HTTPS ERROR: $($_.Exception.Message)"
    
    try {
        Write-Host "Testing HTTP endpoint..."
        $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $body
        Write-Host "SUCCESS! User created:"
        Write-Host ($response | ConvertTo-Json)
    } catch {
        Write-Host "HTTP ERROR: $($_.Exception.Message)"
        if ($_.Exception.Response) {
            Write-Host "Status Code: $($_.Exception.Response.StatusCode)"
            Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
        }
    }
}
