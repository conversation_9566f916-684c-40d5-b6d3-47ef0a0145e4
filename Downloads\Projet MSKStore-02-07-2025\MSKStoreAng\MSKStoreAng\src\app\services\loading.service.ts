import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private loadingTextSubject = new BehaviorSubject<string>('Loading...');

  constructor() { }

  get isLoading$(): Observable<boolean> {
    return this.loadingSubject.asObservable();
  }

  get loadingText$(): Observable<string> {
    return this.loadingTextSubject.asObservable();
  }

  show(text: string = 'Loading...'): void {
    this.loadingTextSubject.next(text);
    this.loadingSubject.next(true);
  }

  hide(): void {
    this.loadingSubject.next(false);
  }

  // Utility method for wrapping async operations
  async withLoading<T>(
    operation: () => Promise<T>, 
    loadingText: string = 'Loading...'
  ): Promise<T> {
    try {
      this.show(loadingText);
      return await operation();
    } finally {
      this.hide();
    }
  }
}
