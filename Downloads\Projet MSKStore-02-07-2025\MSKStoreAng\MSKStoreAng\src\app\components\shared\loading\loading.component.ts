import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../../services/loading.service';

@Component({
  selector: 'app-loading',
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.scss']
})
export class LoadingComponent implements OnInit, OnDestroy {
  isLoading = false;
  loadingText = 'Loading...';
  private subscriptions = new Subscription();

  constructor(private loadingService: LoadingService) { }

  ngOnInit(): void {
    this.subscriptions.add(
      this.loadingService.isLoading$.subscribe(
        isLoading => this.isLoading = isLoading
      )
    );

    this.subscriptions.add(
      this.loadingService.loadingText$.subscribe(
        text => this.loadingText = text
      )
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
