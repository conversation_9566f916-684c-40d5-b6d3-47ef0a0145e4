// ================================================================================================
//   File Name: _design-system.scss
//   Description: Global Design System for MSKStore Dashboard
//   Ensures consistent styling across all pages and components
// ================================================================================================

// Note: Removed circular import to prevent SCSS compilation errors

// ================================================================================================
//   MENU SCROLLING FIX
// ================================================================================================

// Fix for dashboard menu scrolling issue
.main-menu {
  height: 100vh !important;
  overflow: hidden !important;

  .main-menu-content,
  .modern-menu-content {
    height: calc(100vh - 80px) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;

    // Custom scrollbar styling
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }

  // Ensure navigation list can scroll
  .navigation,
  .modern-navigation-list {
    height: auto !important;
    min-height: 100% !important;
  }
}

// ================================================================================================
// DESIGN SYSTEM VARIABLES
// ================================================================================================

// Original Color Palette (Restored from Checkpoint 1)
$primary-text: #5e5873;
$secondary-text: #6e6b7b;
$muted-text: #82868b;
$card-bg: #fff;
$primary-accent: #7367f0;
$border-color: #ebe9f1;

// Spacing System
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-xxl: 3rem;     // 48px

// Border Radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

// Shadows
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
$shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);

// Typography
$font-size-xs: 0.75rem;   // 12px
$font-size-sm: 0.875rem;  // 14px
$font-size-md: 1rem;      // 16px
$font-size-lg: 1.125rem;  // 18px
$font-size-xl: 1.25rem;   // 20px
$font-size-xxl: 1.5rem;   // 24px

// ================================================================================================
// MODERN PAGE CONTAINER
// ================================================================================================

.modern-page-container {
  padding: $spacing-lg;
  background: #f8f8f8;
  min-height: calc(100vh - 120px);

  @media (max-width: 768px) {
    padding: $spacing-md;
  }
}

// ================================================================================================
// MODERN PAGE HEADER
// ================================================================================================

.modern-page-header {
  background: $card-bg;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: $spacing-md;

  .page-title-section {
    flex: 1;

    .page-title {
      font-size: $font-size-xxl;
      font-weight: 600;
      color: $primary-text;
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      .title-icon {
        width: 24px;
        height: 24px;
        color: $primary-accent;
      }
    }

    .page-subtitle {
      font-size: $font-size-sm;
      color: $muted-text;
      margin: $spacing-xs 0 0 0;
    }
  }

  .page-actions {
    display: flex;
    gap: $spacing-sm;
    align-items: center;
    flex-wrap: wrap;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    text-align: center;

    .page-actions {
      justify-content: center;
    }
  }
}

// ================================================================================================
// MODERN BUTTONS
// ================================================================================================

.btn-modern {
  display: inline-flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  font-size: $font-size-sm;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  cursor: pointer;
  white-space: nowrap;

  .btn-icon {
    width: 16px;
    height: 16px;
  }

  // Primary Button
  &.btn-primary {
    background: $primary-accent;
    color: white;
    border-color: $primary-accent;

    &:hover {
      background: darken($primary-accent, 10%);
      border-color: darken($primary-accent, 10%);
      transform: translateY(-1px);
      box-shadow: $shadow-md;
    }
  }

  // Secondary Button
  &.btn-secondary {
    background: transparent;
    color: $secondary-text;
    border-color: $border-color;

    &:hover {
      background: #f8f9fa;
      border-color: $secondary-text;
      color: $primary-text;
    }
  }

  // Success Button
  &.btn-success {
    background: #28a745;
    color: white;
    border-color: #28a745;

    &:hover {
      background: darken(#28a745, 10%);
      border-color: darken(#28a745, 10%);
      transform: translateY(-1px);
      box-shadow: $shadow-md;
    }
  }

  // Warning Button
  &.btn-warning {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;

    &:hover {
      background: darken(#ffc107, 10%);
      border-color: darken(#ffc107, 10%);
      transform: translateY(-1px);
      box-shadow: $shadow-md;
    }
  }

  // Danger Button
  &.btn-danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;

    &:hover {
      background: darken(#dc3545, 10%);
      border-color: darken(#dc3545, 10%);
      transform: translateY(-1px);
      box-shadow: $shadow-md;
    }
  }

  // Size Variants
  &.btn-sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;

    .btn-icon {
      width: 14px;
      height: 14px;
    }
  }

  &.btn-lg {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-md;

    .btn-icon {
      width: 18px;
      height: 18px;
    }
  }

  // Disabled State
  &:disabled,
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

// ================================================================================================
// MODERN CARDS
// ================================================================================================

.modern-card {
  background: $card-bg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    box-shadow: $shadow-md;
    transform: translateY(-2px);
  }

  .modern-card-header {
    padding: $spacing-lg;
    border-bottom: 1px solid $border-color;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    .card-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $primary-text;
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      .title-icon {
        width: 20px;
        height: 20px;
        color: $primary-accent;
      }
    }

    .card-subtitle {
      font-size: $font-size-sm;
      color: $muted-text;
      margin: $spacing-xs 0 0 0;
    }
  }

  .modern-card-body {
    padding: $spacing-lg;
  }

  .modern-card-footer {
    padding: $spacing-md $spacing-lg;
    border-top: 1px solid $border-color;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: $spacing-sm;
  }
}

// ================================================================================================
// MODERN TABLES
// ================================================================================================

.modern-table-container {
  background: $card-bg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  overflow: hidden;
  margin-bottom: $spacing-lg;

  .modern-table-header {
    padding: $spacing-lg;
    border-bottom: 1px solid $border-color;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: $spacing-md;

    .table-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $primary-text;
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      .title-icon {
        width: 20px;
        height: 20px;
        color: $primary-accent;
      }
    }

    .table-actions {
      display: flex;
      gap: $spacing-sm;
      align-items: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      text-align: center;

      .table-actions {
        justify-content: center;
      }
    }
  }

  .table-responsive {
    overflow-x: auto;
  }

  .modern-table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;

    thead {
      background: #f8f9fa;

      th {
        padding: $spacing-md $spacing-lg;
        font-size: $font-size-sm;
        font-weight: 600;
        color: $primary-text;
        border: none;
        border-bottom: 2px solid $border-color;
        text-align: left;
        white-space: nowrap;

        .th-icon {
          width: 14px;
          height: 14px;
          margin-right: $spacing-xs;
          color: $primary-accent;
        }
      }
    }

    tbody {
      tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid $border-color;

        &:hover {
          background: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        td {
          padding: $spacing-md $spacing-lg;
          font-size: $font-size-sm;
          color: $secondary-text;
          vertical-align: middle;

          // Action buttons in tables
          .action-buttons {
            display: flex;
            gap: $spacing-xs;
            align-items: center;

            .btn-action {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              border-radius: $border-radius-sm;
              border: 1px solid $border-color;
              background: white;
              color: $muted-text;
              text-decoration: none;
              transition: all 0.2s ease;

              .action-icon {
                width: 14px;
                height: 14px;
              }

              &.btn-view {
                &:hover {
                  background: #e3f2fd;
                  border-color: #2196f3;
                  color: #2196f3;
                }
              }

              &.btn-edit {
                &:hover {
                  background: #fff3e0;
                  border-color: #ff9800;
                  color: #ff9800;
                }
              }

              &.btn-delete {
                &:hover {
                  background: #ffebee;
                  border-color: #f44336;
                  color: #f44336;
                }
              }
            }
          }
        }
      }
    }
  }
}

// ================================================================================================
// MODERN FORMS
// ================================================================================================

.modern-form-container {
  background: $card-bg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  overflow: hidden;
  margin-bottom: $spacing-lg;

  .modern-form-header {
    padding: $spacing-xl;
    border-bottom: 1px solid $border-color;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    .modern-form-title {
      font-size: $font-size-xl;
      font-weight: 600;
      color: $primary-text;
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      .title-icon {
        width: 24px;
        height: 24px;
        color: $primary-accent;
      }
    }

    .modern-form-subtitle {
      font-size: $font-size-sm;
      color: $muted-text;
      margin: $spacing-xs 0 0 0;
    }
  }

  .modern-form {
    padding: $spacing-xl;

    .modern-form-section {
      margin-bottom: $spacing-xl;
      padding-bottom: $spacing-lg;
      border-bottom: 1px solid $border-color;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .section-title {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $primary-text;
        margin: 0 0 $spacing-lg 0;
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        .section-icon {
          width: 18px;
          height: 18px;
          color: $primary-accent;
        }
      }
    }

    .modern-form-label {
      font-size: $font-size-sm;
      font-weight: 500;
      color: $primary-text;
      margin-bottom: $spacing-xs;
      display: flex;
      align-items: center;
      gap: $spacing-xs;

      .label-icon {
        width: 14px;
        height: 14px;
        color: $muted-text;
      }

      .required {
        color: #dc3545;
        font-weight: 600;
      }
    }

    .modern-input,
    .modern-select {
      width: 100%;
      padding: $spacing-sm $spacing-md;
      border: 1px solid $border-color;
      border-radius: $border-radius-md;
      font-size: $font-size-sm;
      color: $primary-text;
      background: white;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: $primary-accent;
        box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
      }

      &::placeholder {
        color: $muted-text;
      }

      &:disabled {
        background: #f8f9fa;
        color: $muted-text;
        cursor: not-allowed;
      }
    }

    .modern-textarea {
      @extend .modern-input;
      min-height: 100px;
      resize: vertical;
    }

    .modern-form-actions {
      margin-top: $spacing-xl;
      padding-top: $spacing-lg;
      border-top: 1px solid $border-color;
      display: flex;
      gap: $spacing-sm;
      justify-content: flex-end;
      flex-wrap: wrap;

      @media (max-width: 768px) {
        justify-content: center;
      }
    }
  }
}

// ================================================================================================
// MODERN PAGINATION
// ================================================================================================

.modern-pagination-container {
  background: $card-bg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  padding: $spacing-lg;
  margin-top: $spacing-lg;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: $spacing-md;

  .pagination-info {
    .pagination-text {
      font-size: $font-size-sm;
      color: $muted-text;
    }
  }

  .modern-pagination {
    .modern-pagination-controls {
      ::ng-deep {
        .ngx-pagination {
          display: flex;
          gap: $spacing-xs;
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            a,
            span {
              display: flex;
              align-items: center;
              justify-content: center;
              min-width: 36px;
              height: 36px;
              padding: $spacing-xs $spacing-sm;
              border: 1px solid $border-color;
              border-radius: $border-radius-sm;
              color: $secondary-text;
              text-decoration: none;
              font-size: $font-size-sm;
              transition: all 0.2s ease;

              &:hover {
                background: #f8f9fa;
                border-color: $primary-accent;
                color: $primary-accent;
              }
            }

            &.current {
              a,
              span {
                background: $primary-accent;
                border-color: $primary-accent;
                color: white;

                &:hover {
                  background: darken($primary-accent, 10%);
                  border-color: darken($primary-accent, 10%);
                }
              }
            }

            &.disabled {
              a,
              span {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                  background: transparent;
                  border-color: $border-color;
                  color: $secondary-text;
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
}

// ================================================================================================
// MODERN PAGE SIZE SELECTOR
// ================================================================================================

.modern-page-size-container {
  background: $card-bg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  padding: $spacing-md $spacing-lg;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  .page-size-label {
    font-size: $font-size-sm;
    color: $secondary-text;
    font-weight: 500;
  }

  .page-size-select {
    min-width: 80px;
    padding: $spacing-xs $spacing-sm;
    border: 1px solid $border-color;
    border-radius: $border-radius-sm;
    font-size: $font-size-sm;
    color: $primary-text;
    background: white;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary-accent;
      box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
    }
  }
}

// ================================================================================================
// MODERN BADGES AND STATUS INDICATORS
// ================================================================================================

.modern-badge {
  display: inline-flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  .badge-icon {
    width: 12px;
    height: 12px;
  }

  // Status variants
  &.badge-success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
  }

  &.badge-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
  }

  &.badge-danger {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
  }

  &.badge-info {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
    border: 1px solid rgba(23, 162, 184, 0.2);
  }

  &.badge-primary {
    background: rgba(115, 103, 240, 0.1);
    color: $primary-accent;
    border: 1px solid rgba(115, 103, 240, 0.2);
  }

  &.badge-secondary {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
  }
}

// ================================================================================================
// UTILITY CLASSES
// ================================================================================================

// Spacing utilities
.m-xs { margin: $spacing-xs !important; }
.m-sm { margin: $spacing-sm !important; }
.m-md { margin: $spacing-md !important; }
.m-lg { margin: $spacing-lg !important; }
.m-xl { margin: $spacing-xl !important; }
.m-xxl { margin: $spacing-xxl !important; }

.p-xs { padding: $spacing-xs !important; }
.p-sm { padding: $spacing-sm !important; }
.p-md { padding: $spacing-md !important; }
.p-lg { padding: $spacing-lg !important; }
.p-xl { padding: $spacing-xl !important; }
.p-xxl { padding: $spacing-xxl !important; }

// Text utilities
.text-primary { color: $primary-text !important; }
.text-secondary { color: $secondary-text !important; }
.text-muted { color: $muted-text !important; }
.text-accent { color: $primary-accent !important; }

// Background utilities
.bg-card { background: $card-bg !important; }
.bg-accent { background: $primary-accent !important; }

// Border utilities
.border-modern { border: 1px solid $border-color !important; }
.border-radius-modern { border-radius: $border-radius-md !important; }

// Shadow utilities
.shadow-sm-modern { box-shadow: $shadow-sm !important; }
.shadow-md-modern { box-shadow: $shadow-md !important; }
.shadow-lg-modern { box-shadow: $shadow-lg !important; }

// ================================================================================================
// MODERN DETAIL PAGES
// ================================================================================================

.detail-group {
  margin-bottom: $spacing-lg;

  .detail-label {
    font-size: $font-size-sm;
    font-weight: 600;
    color: $primary-text;
    margin-bottom: $spacing-xs;
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .label-icon {
      width: 14px;
      height: 14px;
      color: $primary-accent;
    }
  }

  .detail-value {
    font-size: $font-size-md;
    color: $secondary-text;
    padding: $spacing-sm 0;

    .product-name,
    .supplier-name,
    .client-name,
    .category-title {
      font-weight: 500;
      color: $primary-text;
    }

    .barcode-value,
    .contact-info,
    .address-text {
      font-family: 'Courier New', monospace;
      background: #f8f9fa;
      padding: $spacing-xs $spacing-sm;
      border-radius: $border-radius-sm;
      border: 1px solid $border-color;
    }

    .credit-amount {
      font-weight: 600;
      color: #28a745;
    }
  }
}

// ================================================================================================
// MODERN LIST ITEM STYLING
// ================================================================================================

.client-id-link,
.supplier-id-link,
.category-id-link,
.product-id-link {
  text-decoration: none;

  .id-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 24px;
    padding: $spacing-xs $spacing-sm;
    background: $primary-accent;
    color: white;
    border-radius: $border-radius-sm;
    font-size: $font-size-xs;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  &:hover {
    .id-badge {
      background: darken($primary-accent, 10%);
      transform: scale(1.05);
    }
  }
}

.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  text-transform: uppercase;

  &.priority-high {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
  }

  &.priority-medium {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
  }

  &.priority-low {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
  }
}

.gender-badge {
  display: inline-flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  background: rgba(115, 103, 240, 0.1);
  color: $primary-accent;
  border: 1px solid rgba(115, 103, 240, 0.2);

  .gender-icon {
    width: 12px;
    height: 12px;
  }
}
