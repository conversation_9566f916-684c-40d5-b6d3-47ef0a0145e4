# PowerShell script to fix dependency injection issue in InputValidationMiddleware

$filePath = "Middleware\InputValidationMiddleware.cs"

# Read the file content
$content = Get-Content $filePath -Raw

# Remove the ISecurityService field
$content = $content -replace 'private readonly ISecurityService _securityService;', ''

# Fix the constructor
$content = $content -replace 'public InputValidationMiddleware\(RequestDelegate next, ILogger<InputValidationMiddleware> logger, ISecurityService securityService\)', 'public InputValidationMiddleware(RequestDelegate next, ILogger<InputValidationMiddleware> logger)'

# Remove the assignment in constructor
$content = $content -replace '_securityService = securityService;', ''

# Fix the LogSecurityEvent method to use service locator
$oldLogMethod = @'
            await _securityService.LogSecurityEventAsync(
                eventType,
                description,
                null,
                clientIp,
                userAgent,
                "WARNING",
                false,
                JsonSerializer.Serialize(new
                {
                    Path = context.Request.Path.ToString(),
                    Method = context.Request.Method,
                    QueryString = context.Request.QueryString.ToString()
                })
            );
'@

$newLogMethod = @'
            // Use service locator pattern to get scoped service
            var securityService = context.RequestServices.GetRequiredService<ISecurityService>();
            
            await securityService.LogSecurityEventAsync(
                eventType,
                description,
                null,
                clientIp,
                userAgent,
                "WARNING",
                false,
                JsonSerializer.Serialize(new
                {
                    Path = context.Request.Path.ToString(),
                    Method = context.Request.Method,
                    QueryString = context.Request.QueryString.ToString()
                })
            );
'@

$content = $content -replace [regex]::Escape($oldLogMethod), $newLogMethod

# Write the content back
Set-Content $filePath $content -Encoding UTF8

Write-Host "Fixed InputValidationMiddleware dependency injection issue"
