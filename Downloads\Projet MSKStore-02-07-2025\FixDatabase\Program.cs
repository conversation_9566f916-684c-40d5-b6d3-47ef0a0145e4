using Microsoft.Data.Sqlite;
using System;

class Program
{
    static void Main()
    {
        string connectionString = "Data Source=../MSKStoreAPI/MskStoreAPI/storedbAdel.db";
        
        try
        {
            using var connection = new SqliteConnection(connectionString);
            connection.Open();
            
            Console.WriteLine("Recreating SecurityAuditLogs table with correct schema...");

            // Drop the existing table and recreate it with the correct schema
            var dropCommand = new SqliteCommand("DROP TABLE IF EXISTS SecurityAuditLogs;", connection);
            dropCommand.ExecuteNonQuery();

            // Create the table with the exact schema from the model
            var createCommand = new SqliteCommand(@"
                CREATE TABLE SecurityAuditLogs (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Timestamp TEXT NOT NULL,
                    EventType TEXT NOT NULL,
                    Description TEXT NOT NULL,
                    UserId TEXT,
                    IpAddress TEXT,
                    UserAgent TEXT,
                    Severity TEXT NOT NULL DEFAULT 'Info',
                    AdditionalData TEXT,
                    IsSuccessful INTEGER NOT NULL DEFAULT 1
                );", connection);
            createCommand.ExecuteNonQuery();

            Console.WriteLine("✓ SecurityAuditLogs table recreated with correct schema!");
            
            // Verify the column was added
            var verifyCommand = new SqliteCommand("PRAGMA table_info(SecurityAuditLogs);", connection);
            Console.WriteLine("\nSecurityAuditLogs table structure:");
            using (var reader = verifyCommand.ExecuteReader())
            {
                while (reader.Read())
                {
                    string columnName = reader.GetString(1); // Column name is at index 1
                    string columnType = reader.GetString(2); // Column type is at index 2
                    Console.WriteLine($"  - {columnName}: {columnType}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
}
