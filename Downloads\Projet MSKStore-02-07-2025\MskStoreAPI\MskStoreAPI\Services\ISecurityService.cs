using MskStoreAPI.Models.Security;
using System.Threading.Tasks;

namespace MskStoreAPI.Services
{
    /// <summary>
    /// Interface for security-related operations
    /// </summary>
    public interface ISecurityService
    {
        /// <summary>
        /// Logs security events for audit purposes
        /// </summary>
        Task LogSecurityEventAsync(string eventType, string description, string? userId = null, 
            string? ipAddress = null, string? userAgent = null, string severity = "Info", 
            bool isSuccessful = true, string? additionalData = null);

        /// <summary>
        /// Records a login attempt
        /// </summary>
        Task RecordLoginAttemptAsync(string email, string ipAddress, bool isSuccessful, 
            string? userAgent = null, string? failureReason = null, int? userId = null);

        /// <summary>
        /// Checks if an account should be locked due to failed attempts
        /// </summary>
        Task<bool> ShouldLockAccountAsync(string email, string ipAddress);

        /// <summary>
        /// Locks an account due to failed login attempts
        /// </summary>
        Task LockAccountAsync(int userId, string email, string ipAddress, string reason);

        /// <summary>
        /// Checks if an account is currently locked
        /// </summary>
        Task<bool> IsAccountLockedAsync(string email);

        /// <summary>
        /// Unlocks an account
        /// </summary>
        Task UnlockAccountAsync(string email);

        /// <summary>
        /// Validates password against security policy
        /// </summary>
        Task<(bool IsValid, string[] Errors)> ValidatePasswordAsync(string password);

        /// <summary>
        /// Checks rate limiting for API calls
        /// </summary>
        Task<bool> IsRateLimitExceededAsync(string identifier, string operation);

        /// <summary>
        /// Gets client IP address from request
        /// </summary>
        string GetClientIpAddress(Microsoft.AspNetCore.Http.HttpContext context);

        /// <summary>
        /// Sanitizes input to prevent XSS attacks
        /// </summary>
        string SanitizeInput(string input);

        /// <summary>
        /// Validates that input doesn't contain malicious content
        /// </summary>
        bool IsInputSafe(string input);
    }
}
