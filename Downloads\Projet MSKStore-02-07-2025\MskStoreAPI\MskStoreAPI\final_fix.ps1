# Final precise fix for remaining compilation errors

Write-Host "Applying final fixes..." -ForegroundColor Green

# Fix UsersController.cs - Line 222-224 (parameter order)
$usersControllerPath = "Controllers\UsersController.cs"
$content = Get-Content $usersControllerPath -Raw

# Fix line 222-224: reorder parameters
$content = $content -replace '(\s+)null,(\s+)"ERROR",(\s+)false,(\s+)GetClientIpAddress\(\),(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\)', '$1null,$2GetClientIpAddress(),$4Request.Headers["User-Agent"].ToString(),$2"ERROR",$3false'

# Fix line 294-299: user.id -> user.id.ToString() and reorder parameters
$content = $content -replace '(\s+)user\.id,(\s+)"INFO",(\s+)true,(\s+)GetClientIpAddress\(\),(\s+)Request\.Headers\["User-Agent"\]\.ToString\(\),(\s+)new \{ sessionId, jti, expiresAt = tokenDescriptor\.Expires \}', '$1user.id.ToString(),$2GetClientIpAddress(),$4Request.Headers["User-Agent"].ToString(),$2"INFO",$3true,$6JsonSerializer.Serialize(new { sessionId, jti, expiresAt = tokenDescriptor.Expires })'

Set-Content $usersControllerPath $content

Write-Host "Fixed UsersController.cs" -ForegroundColor Yellow

# Fix PasswordValidationService.cs - Remaining lines
$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath -Raw

# Fix line 161-163: userId? -> userId?.ToString() and reorder
$content = $content -replace '(\s+)userId\?,(\s+)"WARNING",(\s+)false,(\s+)ipAddress,(\s+)userAgent', '$1userId?.ToString(),$2ipAddress,$4userAgent,$2"WARNING",$3false'

# Fix line 206-208: userId -> userId.ToString() and reorder
$content = $content -replace '(\s+)userId,(\s+)"WARNING",(\s+)false,(\s+)ipAddress,(\s+)userAgent', '$1userId.ToString(),$2ipAddress,$4userAgent,$2"WARNING",$3false'

# Fix line 350: reorder parameters
$content = $content -replace '(\s+)userId\?\.ToString\(\),(\s+)"INFO",(\s+)true,(\s+)ipAddress,(\s+)userAgent', '$1userId?.ToString(),$2ipAddress,$4userAgent,$2"INFO",$3true'

# Fix line 474-476: userId -> userId.ToString() and reorder
$content = $content -replace '(\s+)userId,(\s+)"INFO",(\s+)true,(\s+)ipAddress,(\s+)userAgent', '$1userId.ToString(),$2ipAddress,$4userAgent,$2"INFO",$3true'

Set-Content $passwordServicePath $content

Write-Host "Fixed PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "All fixes applied! Running build..." -ForegroundColor Green
dotnet build
