# Test user creation API endpoint
$uri = "http://localhost:5095/api/Users"
$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    name = "TestUser"
    lastName = "TestLastName"
    email = "<EMAIL>"
    password = "TestPassword123!"
    phone = "1234567890"
    role = "Employee"
} | ConvertTo-Json

Write-Host "Testing POST /api/Users endpoint..."
Write-Host "Request body: $body"
Write-Host ""

try {
    $response = Invoke-WebRequest -Uri $uri -Method POST -Headers $headers -Body $body
    Write-Host "SUCCESS! Status Code: $($response.StatusCode)"
    Write-Host "Response: $($response.Content)"
} catch {
    Write-Host "ERROR! Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Error Message: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
