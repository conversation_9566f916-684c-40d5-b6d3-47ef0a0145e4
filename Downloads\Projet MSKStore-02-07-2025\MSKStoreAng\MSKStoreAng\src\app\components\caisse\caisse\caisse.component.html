<div class="row">
  <div class="col-md-9">


    <div class="form-row">
      <div class="form-group col-md-8">
        <label for="tabledataname">{{ 'TableName' | translate }}:</label>
        <div class="input-group">
          <input type="text" class="form-control form-control-sm mr-1" id="tabledataname" name="tabledataname" [(ngModel)]="tabledataname">
          <div class="input-group-append">
            <button type="button" class="btn btn-primary mr-1 btn-sm" (click)="saveTableData()">{{ 'Savepurchasestable' | translate }}</button>
          </div>
        </div>
      </div>
    
      <div class="form-group col-md-4">
        <label for="saved-tables">{{ 'SavedTables' | translate }}:</label>
        <div class="input-group">
          <select class="form-control form-control-sm mr-1" id="saved-tables" name="saved-tables"  [(ngModel)]="selectedTableDataName">
            <option *ngFor="let tabledata of storedtabledatas" [ngValue]="tabledata.name">{{tabledata.name}}</option>
          </select>
          <div class="input-group-append">
            <button type="button" class="btn btn-primary btn-sm" (click)="getTableData()">{{ 'Loadpurchasestable' | translate }}</button>
          </div>
        </div>
      </div>
    </div>



    <div class="form-group">
      <label for="Search">{{ 'Search' | translate }}...</label>
      <input type="text" class="form-control form-control-sm" [(ngModel)]="searchText" (input)="search(searchText)">
      <div class="input-group">


      <ng-container *ngIf="searchText">
      <ng-container *ngFor="let produit of produitsrecherche">
        <button class="dropdown-item" (click)="selectProduit(produit)">{{ produit.nom }}</button>
      </ng-container>
    </ng-container>
    </div>
  </div>




<br><br>
    <div class="form-row">
      <div class="col-md-12 mb-3">
        <div class="input-group">
          <input #barcodeInput type="text" class="form-control mr-1 input-field" placeholder="Code à bares" />

          


          <div class="input-group-append">
            <button class="btn btn-success" (click)="onAddClick(barcodeInput)">{{ 'Add' | translate }}</button>
          </div>
        </div>
        <div class="form-check">
          <input class="form-check-input" type="checkbox" id="scannerCheckbox" name="scannerCheckbox" [(ngModel)]="scanner">
          <label class="form-check-label" for="scannerCheckbox">
            {{ 'Enablescanner' | translate }}
          </label>
        </div>
        <p id="barcodeMsg" style="display: none;">{{ 'Barcodedoesnotexist' | translate }}.</p>
        <div *ngIf="scanner ">

          <div class="scanner-shell" [hidden]="!hasDevices">
          
            <header>
              <div class="form-group">
                <select class="form-control" (change)="onDeviceSelectChange($event.target.value)">
                  <option value="" [selected]="!currentDevice">{{ 'NoDeviceSelected' | translate }}</option>
                  <option *ngFor="let device of availableDevices" [value]="device.deviceId"
                    [selected]="currentDevice && device.deviceId === currentDevice.deviceId">{{device.label}}</option>
                </select>
              </div>
            </header>
          <zxing-scanner style="width: 300px; height: 200px;"
           [torch]="torchEnabled"
           [(device)]="currentDevice" 
           (scanSuccess)="onCodeResult($event)"
          [formats]="formatsEnabled" 
          [tryHarder]="tryHarder" 
          (permissionResponse)="onHasPermission($event)"
          (camerasFound)="onCamerasFound($event)"
           (torchCompatible)="onTorchCompatible($event)">
          </zxing-scanner>
          
            <section class="results" *ngIf="qrResultString">
              <div>
                <small>{{ 'Result' | translate }}</small> 
                <strong>{{ qrResultString }}</strong>
              </div>
            </section>
          </div>
         
          <ng-container *ngIf="hasPermission === false || hasPermission === undefined || hasDevices === undefined || hasDevices === false">
          
            <h2>{{ 'Couldntcheckbarcode' | translate }} !! 😪</h2>
          </ng-container>
          
        </div>
  
      </div>





      




    </div>
    <!-- Table of products added to the cart -->
    <div class="table-responsive">

      <table class="table table-bordered">
        <thead>
          <tr>
            <th>{{ 'Barcode' | translate }}</th>
            <th>{{ 'Quantity' | translate }}</th>
            <th>{{ 'Name' | translate }}</th>
            <th>{{ 'RefPrice' | translate }}</th>
            <th>{{ 'Reduction' | translate }}</th>
            <th>{{ 'Taxe' | translate }}</th>
            <th>{{ 'SellPrice' | translate }}</th>

            <th>{{ 'Total' | translate }}</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of tableData">
            <td>{{ item.barcode }}</td>
            <td><input type="number" [(ngModel)]="item.quantity" (ngModelChange)="updateTotal(item)" style="width: 50px; height: 25px; font-size: 15px;"/></td>
            <td>{{ item.name }}</td>
            <td>{{ item.unitPrice }}</td>
            <td>{{ item.reduction }} %</td>
            <td>{{ item.taxe }} %</td>
            <td>{{ item.unitPrice-(item.unitPrice*item.reduction/100)+(item.unitPrice*item.taxe/100) }} </td>


            <td>{{ item.total }}</td>
            <td><button (click)="removeItem(item)">{{ 'Remove' | translate }}</button></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="col-md-3">
    <!-- Card for payment details -->
    <div class="card">
      <div class="card-header">{{ 'Checkout' | translate }}</div>
      <div class="card-body">
        <div class="form-group">
          <label for="date">{{ 'Date' | translate }} : {{ time | date: 'dd/MM/yyyy à HH:mm:ss' }}</label>
        </div>
        <div class="form-group">
          <label for="cashier-id">{{ 'Cashier' | translate }} : {{getCurrentUser()?.given_name}}</label>
        </div>
        <div class="form-group">
          <label for="facture-number">{{ 'ticketNumber' | translate }} : {{ticketNumber}}</label>
        </div>
        <div class="form-group">
          <label for="total-purchase"><b> {{ 'TotalPurchase' | translate }} : {{totalPurchase}} </b></label>
        </div>

        <div class="form-group">
          <label for="payment-method">{{ 'PaymentMethod' | translate }} :</label>
          <select class="form-control" id="payment-method" (change)="onPaymentMethodChange($event.target.value)">
            <option value="cash">{{ 'Cash' | translate }}</option>
            <option value="credit">{{ 'Credit' | translate }}</option>
            <option value="check">{{ 'Check' | translate }}</option>
            <option value="ticket">{{ 'RestaurantTicket' | translate }}</option>
            <option value="ticket&credit">{{ 'RestaurantTicketcredit' | translate }}</option>

          </select>
        </div>





        <div class="form-group" *ngIf="paymentMethod === 'cash'">
          <label for="amount-paid">{{ 'AmountPaid' | translate }} :</label>
          <input type="number" class="form-control" id="amount-paid" [(ngModel)]="amountPaid" />
        </div>
        <div class="form-group" *ngIf="paymentMethod === 'cash'  && amountPaid - totalPurchase > 0">
          <label for="return">{{ 'Return' | translate }}:{{amountPaid - totalPurchase}}</label>
        </div>
        

        <div class="form-group" *ngIf="paymentMethod === 'credit'">
          <label for="client">{{ 'Client' | translate }}: </label>
          <select class="form-control mr-1" id="clientSelect" [(ngModel)]="selectedClient">
            <option *ngFor="let client of clients" [ngValue]="client">{{client.id}} - {{client.name}} {{client.lastname}} {{client.phone}}</option>
          </select>
        </div>

        
        <div class="form-group" *ngIf="paymentMethod === 'check'">
          <label for="account-owner">{{ 'AccountOwner' | translate }}:</label>
          <input type="text" class="form-control" id="account-owner" [(ngModel)]="accountowner" />
          <label for="account-number">{{ 'accountNumber' | translate }}:</label>
          <input type="text" class="form-control" id="account-number" [(ngModel)]="checkcompte" />
        </div>
        
        
        <div class="form-group" *ngIf="paymentMethod === 'ticket'">
          <label for="typeticket">{{ 'typeTicket' | translate }}: </label>
          <select class="form-control mr-1" id="TicketSelect" [(ngModel)]="selectedTypeticket">
            <option *ngFor="let typeticket of typetickets" [ngValue]="typeticket">{{typeticket.id}} - {{typeticket.nom}}</option>
          </select>
          <label for="ticket-code">{{ 'TicketCode' | translate }}:</label>
          <input type="text" class="form-control" id="ticket-code" [(ngModel)]="ticketcode" />
          

          <br>
          <button class="btn btn-primary" (click)="addTicketresto()">{{ 'AddTicket' | translate }}</button>
          <div *ngIf="tickets.length > 0">
            <br>
            <table class="table table-striped table-sm">
              <thead>
                <tr>
                  <th><i data-feather="code"></i></th>
                  <th><i data-feather="dollar-sign"></i></th>
                  <th>=</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let ticket of tickets; let i = index">
                  <td>{{ ticket.code }}</td>
                  <td>{{ typeticketrestoMap[ticket.typeticketrestoid]?.nom }}</td>
                  <td>{{ typeticketrestoMap[ticket.typeticketrestoid]?.montant * typeticketrestoMap[ticket.typeticketrestoid]?.pourcentage / 100 }}</td>
                  <td>
                    <button class="btn btn-sm btn-danger" (click)="deleteTicketresto(i)"><i data-feather="trash-2"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
            <br>
            
            <p>{{ 'TotalAmountPaidwithTicket' | translate }}: {{ totalTicketAmount }}</p>
            <p>{{ 'AmounttobePaidinCash' | translate }}:  {{totalPurchase-totalTicketAmount}}</p>
          </div>
        </div>
        
        
        <div class="form-group" *ngIf="paymentMethod === 'ticket&credit'">
          <label for="typeticket">{{ 'typeTicket' | translate }}: </label>
          <select class="form-control mr-1" id="TicketSelect" [(ngModel)]="selectedTypeticket">
            <option *ngFor="let typeticket of typetickets" [ngValue]="typeticket">{{typeticket.id}} - {{typeticket.nom}}</option>
          </select>
          <label for="ticket-code">{{ 'TicketCode' | translate }}:</label>
          <input type="text" class="form-control" id="ticket-code" [(ngModel)]="ticketcode" />
          

          <br>
          <button class="btn btn-primary" (click)="addTicketresto()">{{ 'AddTicket' | translate }}</button>
          <div *ngIf="tickets.length > 0">
            <br>
            <table class="table table-striped table-sm">
              <thead>
                <tr>
                  <th><i data-feather="code"></i></th>
                  <th><i data-feather="dollar-sign"></i></th>
                  <th>=</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let ticket of tickets; let i = index">
                  <td>{{ ticket.code }}</td>
                  <td>{{ typeticketrestoMap[ticket.typeticketrestoid]?.nom }}</td>
                  <td>{{ typeticketrestoMap[ticket.typeticketrestoid]?.montant * typeticketrestoMap[ticket.typeticketrestoid]?.pourcentage / 100 }}</td>
                  <td>
                    <button class="btn btn-sm btn-danger" (click)="deleteTicketresto(i)"><i data-feather="trash-2"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
            <br>
            
            <p>{{ 'TotalAmountPaidwithTicket' | translate }}: {{ totalTicketAmount }}</p>
            <p>{{ 'AmounttobePaidincredit' | translate }}:  {{totalPurchase-totalTicketAmount}}</p>
            <label for="client">{{ 'Client' | translate }}: </label>
          <select class="form-control mr-1" id="clientSelect" [(ngModel)]="selectedClient">
            <option *ngFor="let client of clients" [ngValue]="client">{{client.id}} - {{client.name}}</option>
          </select>
          </div>
        </div>
        
        


<!-- 
        
        <button class="btn btn-success mr-1 btn-sm" (click)="onSave()"><i data-feather="save"></i>  </button>
        <button class="btn btn-info mr-1 btn-sm" (click)="printTicket()"><i data-feather="printer"></i> </button>

        <button class="btn btn-danger btn-sm" (click)="onDelete()"><i data-feather="trash-2"></i> </button>
-->
          <button class="btn btn-success mr-1 btn-sm" (click)="onSave()"><i data-feather="save"></i>  {{ 'Save' | translate }}</button>
        <button class="btn btn-primary mr-1 btn-sm" (click)="printTicket()"><i data-feather="printer"></i> {{ 'printticket' | translate }}</button>

        <button class="btn btn-danger btn-sm" (click)="onDelete()"><i data-feather="trash-2"></i> {{ 'Delete' | translate }}</button>
      </div>
    </div>
  </div>
</div>


