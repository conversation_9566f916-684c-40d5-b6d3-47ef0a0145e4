// ===================================================================
// Modern Design System for MSKStore
// ===================================================================

// ===================================================================
// Color Palette - Modern & Professional
// ===================================================================
:root {
  // Primary Colors
  --color-primary: #6366F1;
  --color-primary-50: #EEF2FF;
  --color-primary-100: #E0E7FF;
  --color-primary-200: #C7D2FE;
  --color-primary-300: #A5B4FC;
  --color-primary-400: #818CF8;
  --color-primary-500: #6366F1;
  --color-primary-600: #4F46E5;
  --color-primary-700: #4338CA;
  --color-primary-800: #3730A3;
  --color-primary-900: #312E81;

  // Secondary Colors
  --color-secondary: #64748B;
  --color-secondary-50: #F8FAFC;
  --color-secondary-100: #F1F5F9;
  --color-secondary-200: #E2E8F0;
  --color-secondary-300: #CBD5E1;
  --color-secondary-400: #94A3B8;
  --color-secondary-500: #64748B;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1E293B;
  --color-secondary-900: #0F172A;

  // Success Colors
  --color-success: #10B981;
  --color-success-50: #ECFDF5;
  --color-success-100: #D1FAE5;
  --color-success-500: #10B981;
  --color-success-600: #059669;

  // Warning Colors
  --color-warning: #F59E0B;
  --color-warning-50: #FFFBEB;
  --color-warning-100: #FEF3C7;
  --color-warning-500: #F59E0B;
  --color-warning-600: #D97706;

  // Danger Colors
  --color-danger: #EF4444;
  --color-danger-50: #FEF2F2;
  --color-danger-100: #FEE2E2;
  --color-danger-500: #EF4444;
  --color-danger-600: #DC2626;

  // Info Colors
  --color-info: #06B6D4;
  --color-info-50: #ECFEFF;
  --color-info-100: #CFFAFE;
  --color-info-500: #06B6D4;
  --color-info-600: #0891B2;

  // Neutral Colors
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;

  // Background Colors
  --bg-body: #F8FAFC;
  --bg-surface: #FFFFFF;
  --bg-surface-secondary: #F8FAFC;
  --bg-overlay: rgba(15, 23, 42, 0.5);

  // Border Colors
  --border-color: #E2E8F0;
  --border-color-light: #F1F5F9;
  --border-color-dark: #CBD5E1;

  // Text Colors
  --text-primary: #0F172A;
  --text-secondary: #64748B;
  --text-muted: #94A3B8;
  --text-inverse: #FFFFFF;

  // Shadows
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  // Gradients
  --gradient-primary: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
  --gradient-success: linear-gradient(135deg, #10B981 0%, #059669 100%);
  --gradient-warning: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  --gradient-danger: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  --gradient-info: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);

  // Spacing Scale
  --spacing-0: 0;
  --spacing-1: 0.25rem;   // 4px
  --spacing-2: 0.5rem;    // 8px
  --spacing-3: 0.75rem;   // 12px
  --spacing-4: 1rem;      // 16px
  --spacing-5: 1.25rem;   // 20px
  --spacing-6: 1.5rem;    // 24px
  --spacing-8: 2rem;      // 32px
  --spacing-10: 2.5rem;   // 40px
  --spacing-12: 3rem;     // 48px
  --spacing-16: 4rem;     // 64px
  --spacing-20: 5rem;     // 80px
  --spacing-24: 6rem;     // 96px

  // Border Radius
  --radius-none: 0;
  --radius-sm: 0.125rem;  // 2px
  --radius-base: 0.25rem; // 4px
  --radius-md: 0.375rem;  // 6px
  --radius-lg: 0.5rem;    // 8px
  --radius-xl: 0.75rem;   // 12px
  --radius-2xl: 1rem;     // 16px
  --radius-3xl: 1.5rem;   // 24px
  --radius-full: 9999px;

  // Typography
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

  // Font Sizes
  --text-xs: 0.75rem;     // 12px
  --text-sm: 0.875rem;    // 14px
  --text-base: 1rem;      // 16px
  --text-lg: 1.125rem;    // 18px
  --text-xl: 1.25rem;     // 20px
  --text-2xl: 1.5rem;     // 24px
  --text-3xl: 1.875rem;   // 30px
  --text-4xl: 2.25rem;    // 36px
  --text-5xl: 3rem;       // 48px

  // Font Weights
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  // Line Heights
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  // Z-Index Scale
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;

  // Transitions
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  // Breakpoints (for reference in JS)
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
}

// ===================================================================
// SCSS Mixins for Modern Design
// ===================================================================

// Responsive Breakpoints
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'sm' {
    @media (min-width: 576px) { @content; }
  }
  @if $breakpoint == 'md' {
    @media (min-width: 768px) { @content; }
  }
  @if $breakpoint == 'lg' {
    @media (min-width: 992px) { @content; }
  }
  @if $breakpoint == 'xl' {
    @media (min-width: 1200px) { @content; }
  }
  @if $breakpoint == 'xxl' {
    @media (min-width: 1400px) { @content; }
  }
}

// Modern Card Component
@mixin modern-card($padding: var(--spacing-6), $radius: var(--radius-lg)) {
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  border-radius: $radius;
  box-shadow: var(--shadow-sm);
  padding: $padding;
  transition: all var(--transition-base);

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }
}

// Modern Button Styles
@mixin modern-button($variant: 'primary') {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  line-height: var(--leading-tight);
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @if $variant == 'primary' {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);

    &:hover:not(:disabled) {
      background: var(--color-primary-600);
      border-color: var(--color-primary-600);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }

  @if $variant == 'secondary' {
    background: var(--bg-surface);
    color: var(--text-primary);
    border-color: var(--border-color);

    &:hover:not(:disabled) {
      background: var(--color-gray-50);
      border-color: var(--border-color-dark);
    }
  }

  @if $variant == 'outline' {
    background: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);

    &:hover:not(:disabled) {
      background: var(--color-primary);
      color: var(--text-inverse);
    }
  }
}

// Modern Form Input
@mixin modern-input() {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-surface);
  color: var(--text-primary);
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  transition: all var(--transition-fast);

  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  &::placeholder {
    color: var(--text-muted);
  }

  &:disabled {
    background: var(--color-gray-50);
    color: var(--text-muted);
    cursor: not-allowed;
  }
}

// Modern Table Styles
@mixin modern-table() {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);

  th, td {
    padding: var(--spacing-4);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }

  th {
    background: var(--color-gray-50);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  td {
    color: var(--text-secondary);
    font-size: var(--text-sm);
  }

  tbody tr {
    transition: background-color var(--transition-fast);

    &:hover {
      background: var(--color-gray-50);
    }

    &:last-child td {
      border-bottom: none;
    }
  }
}

// Utility Classes
.modern-gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-floating-action {
  position: fixed;
  bottom: var(--spacing-6);
  right: var(--spacing-6);
  z-index: var(--z-50);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
}
