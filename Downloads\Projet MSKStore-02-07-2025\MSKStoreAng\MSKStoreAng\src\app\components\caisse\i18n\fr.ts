export const locale = {
	lang: 'fr',
	data: {
		Barcode: "Code à barres",
		Quantity: "Quantité",
		Name: "Nom",
		UnitPrice: "Prix ​​unitaire",
		Reduction: "Réduction",
		Total: "Total",
		Checkout: "Paiement",
		Date:"Date",
		Cashier:"Caissier",
		FactureNumber:"Numéro de facture",
		TotalPurchase:"Total de l'achat",
		PaymentMethod:"Méthode de paiement",
		Cash:"Espèces",
		Credit:"Crédit",
		Check:"Chèque",
		RestaurantTicket:"Ticket restaurant",
		AmountPaid:"Montant payé",
		Return:"Retour",
		Client:"Client",
		AccountOwner:"Propriétaire du compte",
		accountNumber:"Numéro de compte",
		typeTicket:"Type de ticket",
		TicketCode:"Code de ticket",
		TotalAmountPaidwithTicket:"Montant total payé avec des tickets",
		AmounttobePaidinCash:"Montant à payer en espèces",
		Add: "Ajouter",
		Remove: "Supprimer",
		AddTicket: "Ajouter le ticket",
		Save: "Enregistrer",
		Delete: "Supprimer",
		RestaurantTicketcredit:"Ticket restaurant & Crédit",
		Search:"Recherche..",
		TableName: "Nom de la table",
Savepurchasestable: "Enregistrer la table d'achats",
SavedTables: "Tables enregistrées",
Loadpurchasestable: "Charger la table d'achats",
Barcodedoesnotexist: "Le code à barres n'existe pas",
NoDeviceSelected: "Aucun appareil sélectionné",
Result: "Résultat",
Couldntcheckbarcode: "Impossible de vérifier le code à barres",
RefPrice: "Prix.Réf",
Taxe: "Taxe",
SellPrice: "Prix.vente",
printticket: "Imprimer le ticket",
ticketNumber: "Numéro de ticket",
Enablescanner:"Activer le scanner"
	},
}