{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=storedbAdel.db",
    "PathConnection": "https://localhost:7258"
  },
  "Security": {
    "EncryptionKey": "vfQKRwiYoH5Ip2oDWRvX96M9gsxmIyesqr0xKG0hSCY=",
    "EncryptionIV": "vslgI/P7i9SL2niqDHqnEg==",
    "JwtSettings": {
      "SecretKey": "2XzOJJv91/Hpa0g2utCRqTQ/lLf8QCG/l0Y5gSOajIVSeSp4B5oQJ/x4fub/u8Vk2qnp8BOATOzLMhsQ0u2gPg==",
      "Issuer": "MSKStoreAPI",
      "Audience": "MSKStoreClients",
      "ExpirationMinutes": 60,
      "RefreshTokenExpirationDays": 7
    },
    "PasswordPolicy": {
      "MinLength": 8,
      "RequireUppercase": true,
      "RequireLowercase": true,
      "RequireDigit": true,
      "RequireSpecialChar": true,
      "MaxFailedAttempts": 5,
      "LockoutDurationMinutes": 30
    },
    "RateLimiting": {
      "LoginAttempts": {
        "MaxAttempts": 5,
        "WindowMinutes": 15
      },
      "ApiCalls": {
        "MaxRequests": 100,
        "WindowMinutes": 1
      }
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Security": "Warning"
    }
  },
  "AllowedHosts": "*"
}
/*
0- Delete all file in folder migrations

1- change app-setting "DefaultConnection": "server=localhost\\DESKTOP-Q4V3MOQ;database=storedbAdel;
					    Data Source= .;Initial Catalog=storedbAdel;Integrated Security=true;trusted_connection=true ;  TrustServerCertificate=True

2- add-migration initial
3- Update-database  // il va créer votre DB
*/