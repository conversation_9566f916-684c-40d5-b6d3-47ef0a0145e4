    <div class="modern-client-profile">
      <div class="profile-header">
        <div class="profile-avatar">
          <div class="avatar-placeholder" *ngIf="!client?.photo">
            <i data-feather="user" class="avatar-icon"></i>
          </div>
          <img *ngIf="client?.photo" class="avatar-image" [src]="client.photo" [alt]="client.name + ' ' + client.lastname">
        </div>
        <div class="profile-info">
          <h2 class="profile-name">{{ client?.name }} {{ client?.lastname }}</h2>
          <div class="profile-badges">
            <span class="priority-badge" [ngClass]="'priority-' + client?.priority">
              <i data-feather="flag" class="badge-icon"></i>
              {{ client?.priority | titlecase }} Priority
            </span>
            <span class="gender-badge">
              <i [data-feather]="client?.gender === 'male' ? 'user' : 'user'" class="badge-icon"></i>
              {{ client?.gender | titlecase }}
            </span>
          </div>
        </div>
        <div class="profile-actions">
          <button class="btn-modern btn-primary" (click)="onModify()">
            <i data-feather="edit" class="btn-icon"></i>
            {{ 'Modify' | translate }}
          </button>
          <button class="btn-modern btn-danger" (click)="onDelete()">
            <i data-feather="trash-2" class="btn-icon"></i>
            {{ 'Delete' | translate }}
          </button>
        </div>
      </div>

      <div class="profile-content">
        <div class="info-sections">
          <!-- Contact Information -->
          <div class="info-section">
            <h3 class="section-title">
              <i data-feather="phone" class="section-icon"></i>
              Contact Information
            </h3>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="phone" class="info-icon"></i>
                  {{ 'Phone' | translate }}
                </div>
                <div class="info-value">{{ client?.phone || 'Not provided' }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="mail" class="info-icon"></i>
                  {{ 'Email' | translate }}
                </div>
                <div class="info-value">{{ client?.email || 'Not provided' }}</div>
              </div>
              <div class="info-item full-width">
                <div class="info-label">
                  <i data-feather="map-pin" class="info-icon"></i>
                  {{ 'Address' | translate }}
                </div>
                <div class="info-value">{{ client?.adresse || 'Not provided' }}</div>
              </div>
            </div>
          </div>

          <!-- Financial Information -->
          <div class="info-section">
            <h3 class="section-title">
              <i data-feather="credit-card" class="section-icon"></i>
              Financial Information
            </h3>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="credit-card" class="info-icon"></i>
                  {{ 'Credit' | translate }}
                </div>
                <div class="info-value credit-amount">{{ client?.credit | currency }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="calendar" class="info-icon"></i>
                  Client Since
                </div>
                <div class="info-value">Recently added</div>
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="info-section">
            <h3 class="section-title">
              <i data-feather="info" class="section-icon"></i>
              Additional Information
            </h3>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="hash" class="info-icon"></i>
                  Client ID
                </div>
                <div class="info-value">
                  <span class="id-badge">{{ client?.id }}</span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <i data-feather="image" class="info-icon"></i>
                  Photo URL
                </div>
                <div class="info-value">{{ client?.photo || 'No photo provided' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  