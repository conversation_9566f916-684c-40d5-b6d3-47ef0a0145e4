<div class="modern-form-container">
  <div class="modern-form-header">
    <h3 class="modern-form-title">
      <i data-feather="truck" class="title-icon"></i>
      {{ 'AddProvider' | translate }}
    </h3>
    <p class="modern-form-subtitle">Fill in the information below to add a new supplier</p>
  </div>

  <form class="modern-form" (submit)="onSubmit()">
    <!-- Personal Information Section -->
    <div class="modern-form-section">
      <h4 class="section-title">
        <i data-feather="user" class="section-icon"></i>
        Personal Information
      </h4>

      <div class="form-row">
        <div class="form-group col-md-6">
          <label class="modern-form-label" for="firstName">
            <i data-feather="user" class="label-icon"></i>
            {{ 'FirstName' | translate }}
            <span class="required">*</span>
          </label>
          <input
            type="text"
            class="form-control modern-input"
            id="firstname"
            name="name"
            [(ngModel)]="newFournisseur.nom"
            placeholder="Enter first name"
            required>
        </div>
        <div class="form-group col-md-6">
          <label class="modern-form-label" for="lastName">
            <i data-feather="user" class="label-icon"></i>
            {{ 'LastName' | translate }}
            <span class="required">*</span>
          </label>
          <input
            type="text"
            class="form-control modern-input"
            id="lastname"
            name="lastname"
            [(ngModel)]="newFournisseur.prenom"
            placeholder="Enter last name"
            required>
        </div>
      </div>
    </div>

    <!-- Contact Information Section -->
    <div class="modern-form-section">
      <h4 class="section-title">
        <i data-feather="phone" class="section-icon"></i>
        Contact Information
      </h4>

      <div class="form-row">
        <div class="form-group col-md-6">
          <label class="modern-form-label" for="phone">
            <i data-feather="phone" class="label-icon"></i>
            {{ 'Phone' | translate }}
          </label>
          <input
            type="tel"
            class="form-control modern-input"
            id="phone"
            name="phone"
            [(ngModel)]="newFournisseur.telephone"
            placeholder="Enter phone number">
        </div>
        <div class="form-group col-md-6">
          <label class="modern-form-label" for="email">
            <i data-feather="mail" class="label-icon"></i>
            {{ 'Email' | translate }}
          </label>
          <input
            type="email"
            class="form-control modern-input"
            id="email"
            name="email"
            [(ngModel)]="newFournisseur.email"
            placeholder="Enter email address">
        </div>
      </div>

      <div class="form-group">
        <label class="modern-form-label" for="address">
          <i data-feather="map-pin" class="label-icon"></i>
          {{ 'Address' | translate }}
        </label>
        <input
          type="text"
          class="form-control modern-input"
          id="addresse"
          name="addresse"
          [(ngModel)]="newFournisseur.adresse"
          placeholder="Enter full address">
      </div>
    </div>

    <!-- Business Information Section -->
    <div class="modern-form-section">
      <h4 class="section-title">
        <i data-feather="briefcase" class="section-icon"></i>
        Business Information
      </h4>

      <div class="form-row">
        <div class="form-group col-md-6">
          <label class="modern-form-label" for="Type">
            <i data-feather="tag" class="label-icon"></i>
            {{ 'type' | translate }}
          </label>
          <input
            type="text"
            class="form-control modern-input"
            id="Type"
            name="Type"
            [(ngModel)]="newFournisseur.type"
            placeholder="Enter supplier type">
        </div>
        <div class="form-group col-md-6">
          <label class="modern-form-label" for="Code">
            <i data-feather="hash" class="label-icon"></i>
            {{ 'code' | translate }}
          </label>
          <input
            type="number"
            class="form-control modern-input"
            id="Code"
            name="Code"
            [(ngModel)]="newFournisseur.code"
            placeholder="Enter supplier code">
        </div>
      </div>

      <div class="form-group">
        <label class="modern-form-label" for="description">
          <i data-feather="file-text" class="label-icon"></i>
          {{ 'description' | translate }}
        </label>
        <textarea
          class="form-control modern-textarea"
          id="description"
          name="description"
          [(ngModel)]="newFournisseur.description"
          placeholder="Enter supplier description"
          rows="3">
        </textarea>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="modern-form-actions">
      <button type="submit" class="btn-modern btn-primary">
        <i data-feather="plus" class="btn-icon"></i>
        {{ 'AddProvider' | translate }}
      </button>
      <button type="button" class="btn-modern btn-secondary" onclick="history.back()">
        <i data-feather="x" class="btn-icon"></i>
        Cancel
      </button>
    </div>
  </form>
</div>
