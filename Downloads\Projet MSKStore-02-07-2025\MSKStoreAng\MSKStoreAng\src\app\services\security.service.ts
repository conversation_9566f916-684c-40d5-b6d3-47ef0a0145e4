import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface SecurityDashboardData {
  totalEvents: number;
  criticalEvents: number;
  highSeverityEvents: number;
  activeLockouts: number;
  lastScanTime: Date;
  systemStatus: string;
}

export interface SecurityEvent {
  id: number;
  eventType: string;
  description: string;
  severity: string;
  timestamp: Date;
  userId?: string;
  ipAddress?: string;
  isSuccessful: boolean;
}

export interface SecurityAlert {
  id: number;
  alertType: string;
  description: string;
  severity: string;
  timestamp: Date;
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
}

export interface SecurityReport {
  id: number;
  reportType: string;
  title: string;
  description: string;
  generatedAt: Date;
  generatedBy: string;
  data: any;
}

export interface SecurityStatus {
  status: string;
  lastUpdated: Date;
  metrics: SecurityMetric[];
}

export interface SecurityMetric {
  name: string;
  value: number;
  unit: string;
  status: string;
}

export interface SecurityScanResult {
  scanId: string;
  startTime: Date;
  endTime: Date;
  status: string;
  findings: SecurityScanFinding[];
  details: SecurityScanDetail[];
}

export interface SecurityScanFinding {
  category: string;
  severity: string;
  description: string;
  recommendation: string;
}

export interface SecurityScanDetail {
  category: string;
  checkName: string;
  status: string;
  details: string;
}

@Injectable({
  providedIn: 'root'
})
export class SecurityService {
  private apiUrl = `${environment.apiUrl}/api/SecurityMonitoring`;

  constructor(private http: HttpClient) { }

  /**
   * Get security dashboard data
   */
  getSecurityDashboard(): Observable<SecurityDashboardData> {
    return this.http.get<SecurityDashboardData>(`${this.apiUrl}/dashboard`);
  }

  /**
   * Get security events with pagination
   */
  getSecurityEvents(page: number = 1, pageSize: number = 10): Observable<{items: SecurityEvent[], totalCount: number}> {
    return this.http.get<{items: SecurityEvent[], totalCount: number}>(`${this.apiUrl}/events?page=${page}&pageSize=${pageSize}`);
  }

  /**
   * Get security alerts
   */
  getSecurityAlerts(): Observable<SecurityAlert[]> {
    return this.http.get<SecurityAlert[]>(`${this.apiUrl}/alerts`);
  }

  /**
   * Get security reports
   */
  getSecurityReports(): Observable<SecurityReport[]> {
    return this.http.get<SecurityReport[]>(`${this.apiUrl}/report`);
  }

  /**
   * Get security status
   */
  getSecurityStatus(): Observable<SecurityStatus> {
    return this.http.get<SecurityStatus>(`${this.apiUrl}/status`);
  }

  /**
   * Trigger security scan
   */
  triggerSecurityScan(): Observable<SecurityScanResult> {
    return this.http.post<SecurityScanResult>(`${this.apiUrl}/scan`, {});
  }

  /**
   * Resolve security alert
   */
  resolveAlert(alertId: number): Observable<any> {
    return this.http.put(`${this.apiUrl}/alerts/${alertId}/resolve`, {});
  }

  /**
   * Generate security report
   */
  generateReport(reportType: string, startDate: Date, endDate: Date): Observable<SecurityReport> {
    const params = {
      reportType,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    };
    return this.http.post<SecurityReport>(`${this.apiUrl}/report/generate`, params);
  }
}
