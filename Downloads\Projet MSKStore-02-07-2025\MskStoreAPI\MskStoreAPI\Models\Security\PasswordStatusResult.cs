namespace MskStoreAPI.Models.Security
{
    /// <summary>
    /// Result of password status check
    /// </summary>
    public class PasswordStatusResult
    {
        public bool IsExpired { get; set; }
        public bool IsNearExpiration { get; set; }
        public int DaysUntilExpiration { get; set; }
        public bool RequiresChange { get; set; }
        public string? Message { get; set; }
        public DateTime? LastChanged { get; set; }
    }
}
