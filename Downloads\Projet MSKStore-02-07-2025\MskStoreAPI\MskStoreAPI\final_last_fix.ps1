# Final fix for the last error in PasswordValidationService.cs

Write-Host "Fixing the last error in PasswordValidationService.cs..." -ForegroundColor Green

$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath -Raw

# Fix the last parameter order issue around line 347-352
# Current: eventType, description, userId, severity, isSuccessful
# Correct: eventType, description, userId, ipAddress, userAgent, severity, isSuccessful
$content = $content -replace '(\s+)null,(\s+)"INFO",(\s+)true', '$1null,$2null,$2null,$2"INFO",$3true'

Set-Content $passwordServicePath $content

Write-Host "Fixed the last error in PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "Running build..." -ForegroundColor Green
dotnet build
