# Test API endpoints step by step

Write-Host "Testing API endpoints..." -ForegroundColor Green

# Test 1: Check if server is running
Write-Host "`n1. Testing server connectivity..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5095/swagger" -UseBasicParsing
    Write-Host "✓ Server is running - Swagger accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Server not accessible: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Test GET Users endpoint
Write-Host "`n2. Testing GET /api/Users..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5095/api/Users" -Method GET -UseBasicParsing
    Write-Host "✓ GET Users successful (Status: $($response.StatusCode))" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ GET Users failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test POST Users with minimal data
Write-Host "`n3. Testing POST /api/Users with minimal data..." -ForegroundColor Yellow
$minimalUser = @{
    name = "Test"
    lastName = "User"
    email = "<EMAIL>"
    password = "Test123!"
    role = "Admin"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $minimalUser
    Write-Host "✓ POST Users with minimal data successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ POST Users with minimal data failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = [System.IO.StreamReader]::new($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

# Test 4: Test POST Users with phone number
Write-Host "`n4. Testing POST /api/Users with phone number..." -ForegroundColor Yellow
$userWithPhone = @{
    name = "Admin"
    lastName = "User"
    email = "<EMAIL>"
    password = "Admin123!"
    phone = "1234567890"
    role = "Admin"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $userWithPhone
    Write-Host "✓ POST Users with phone successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ POST Users with phone failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = [System.IO.StreamReader]::new($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

# Test 5: Test with different phone format
Write-Host "`n5. Testing POST /api/Users with different phone format..." -ForegroundColor Yellow
$userWithDifferentPhone = @{
    name = "Test2"
    lastName = "User2"
    email = "<EMAIL>"
    password = "Test123!"
    phone = "9876543210"
    role = "Admin"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users" -Method POST -ContentType "application/json" -Body $userWithDifferentPhone
    Write-Host "✓ POST Users with different phone successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ POST Users with different phone failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = [System.IO.StreamReader]::new($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`nAPI testing completed." -ForegroundColor Green
