<div class="breadcrumb-container" role="navigation" aria-label="Breadcrumb">
  <ol class="breadcrumb">
    <li 
      *ngFor="let item of breadcrumbs; let last = last" 
      class="breadcrumb-item"
      [class.active]="last">
      
      <ng-container *ngIf="!last; else activeItem">
        <a [routerLink]="item.url" [attr.aria-label]="'Navigate to ' + item.label">
          <i 
            *ngIf="item.icon" 
            [attr.data-feather]="item.icon" 
            class="breadcrumb-icon"
            [attr.aria-hidden]="true">
          </i>
          {{ item.label }}
        </a>
      </ng-container>
      
      <ng-template #activeItem>
        <span [attr.aria-current]="'page'">
          <i 
            *ngIf="item.icon" 
            [attr.data-feather]="item.icon" 
            class="breadcrumb-icon"
            [attr.aria-hidden]="true">
          </i>
          {{ item.label }}
        </span>
      </ng-template>
    </li>
  </ol>
</div>
