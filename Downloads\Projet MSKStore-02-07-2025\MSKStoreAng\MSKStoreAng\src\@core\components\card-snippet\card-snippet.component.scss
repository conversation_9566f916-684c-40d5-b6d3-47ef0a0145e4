// code icon
.code-icon {
  font: normal normal normal 14px/1 FontAwesome;
  display: inline-block;
  font-weight: 700 !important;
  cursor: pointer;

  &:before {
    content: '\f121';
  }
}

// code toggle animation
.code-toggle {
  transition: transform 0.35s, opacity 0.35s, max-height 0.4s ease-out;
  position: relative;

  &.collapse {
    opacity: 0;
    max-height: 0;
    transform: translateY(100%);

    &.show {
      opacity: 1;
      max-height: 600px;
      transform: translateY(0);
    }

    &:not(.show) {
      display: block;
    }
  }

  pre {
    max-height: 350px;
    overflow: auto;
    white-space: nowrap;
    margin: 0;
    code {
      white-space: pre;
      overflow: visible;
      padding: 2rem 0.5em;
    }
  }
  .icon-copy {
    position: absolute;
    right: 7px;
  }
  .text-copied {
    position: absolute;
    right: 7px;
    top: 5px;
  }
}
