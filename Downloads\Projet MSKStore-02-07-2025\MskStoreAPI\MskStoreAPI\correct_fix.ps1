# Correct fix for PasswordValidationService.cs

Write-Host "Correcting PasswordValidationService.cs..." -ForegroundColor Green

$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath -Raw

# Fix the incorrect ipAddress and userAgent references with null values
$content = $content -replace 'ipAddress,\s*userAgent,', 'null, null,'

# Fix the remaining parameter order issue on line 354
$content = $content -replace '(\s+)userId\?\.ToString\(\),(\s+)null,(\s+)null,(\s+)"INFO",(\s+)true', '$1userId?.ToString(),$2null,$3null,$4"INFO",$5true'

Set-Content $passwordServicePath $content

Write-Host "Fixed PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "Running build..." -ForegroundColor Green
dotnet build
