@startuml
actor admin
rectangle "Système" {
  usecase "Gérer les utilisateurs" as UC_GererUtilisateurs
  usecase "Générer un rapport" as UC_GenererRapport
  usecase "Modifier timbre fiscal" as UC_ModifierTimbre
  usecase "Ajouter un utilisateur" as UC_AjouterUtilisateur
  usecase "Consulter les utilisateurs" as UC_ConsulterUtilisateurs
  usecase "Supprimer un utilisateur" as UC_SupprimerUtilisateur
  usecase "Modifier utilisateur" as UC_ModifierUtilisateur
  usecase "Donner des permissions" as UC_DonnerPermissions

  admin -- UC_GererUtilisateurs
  admin -- UC_GenererRapport
  admin -- UC_ModifierTimbre

  UC_GererUtilisateurs <.. UC_AjouterUtilisateur : <<include>>
  UC_GererUtilisateurs <.. UC_ConsulterUtilisateurs : <<extends>>
  UC_ConsulterUtilisateurs <.. UC_SupprimerUtilisateur : <<extends>>
  UC_ConsulterUtilisateurs <.. UC_ModifierUtilisateur : <<extends>>
  UC_ModifierUtilisateur <.. UC_DonnerPermissions : <<include>>
}
@enduml

