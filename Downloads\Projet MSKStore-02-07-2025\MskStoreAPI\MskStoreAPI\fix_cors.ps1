# PowerShell script to fix CORS configuration

$filePath = "Program.cs"

# Read the file content
$content = Get-Content $filePath -Raw

# Replace the CORS configuration to allow any origin for development
$oldCors = 'builder.WithOrigins("http://localhost:4200", "http://localhost:5095", "https://localhost:7258")
            .AllowAnyMethod()
            .AllowAnyHeader();'

$newCors = 'builder.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();'

$content = $content -replace [regex]::Escape($oldCors), $newCors

# Write the content back
Set-Content $filePath $content -Encoding UTF8

Write-Host "Fixed CORS configuration to allow any origin"
