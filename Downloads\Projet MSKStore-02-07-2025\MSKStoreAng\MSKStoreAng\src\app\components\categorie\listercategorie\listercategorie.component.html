<!-- Modern Page Size Selector -->
<div class="modern-page-size-container">
  <label class="page-size-label">{{ 'Show' | translate }}</label>
  <select class="page-size-select" [(ngModel)]="pageSize" (change)="paginate($event)">
    <option value="5">5</option>
    <option value="10">10</option>
    <option value="20">20</option>
  </select>
</div>

<!-- Modern Table Container -->
<div class="modern-table-container">
  <div class="modern-table-header">
    <h3 class="table-title">
      <i data-feather="folder" class="title-icon"></i>
      {{ 'Category List' | translate }}
    </h3>
    <div class="table-actions">
      <button class="btn-modern btn-primary btn-sm" routerLink="/categorie/ajouter">
        <i data-feather="plus" class="btn-icon"></i>
        {{ 'Add Category' | translate }}
      </button>
    </div>
  </div>

  <div class="table-responsive">
    <table class="table modern-table">
      <thead>
        <tr>
          <th>
            <i data-feather="hash" class="th-icon"></i>
            #
          </th>
          <th>
            <i data-feather="folder-plus" class="th-icon"></i>
            {{ 'Parent' | translate }}
          </th>
          <th>
            <i data-feather="type" class="th-icon"></i>
            {{ 'Title' | translate }}
          </th>
          <th>
            <i data-feather="file-text" class="th-icon"></i>
            {{ 'Description' | translate }}
          </th>
          <th>
            <i data-feather="settings" class="th-icon"></i>
            Actions
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let categorie of categories | paginate: { itemsPerPage: pageSize, currentPage: currentPage }"
            class="modern-table-row">
          <td>
            <a [routerLink]="['/affichercategorie', categorie.id]" class="category-id-link">
              <span class="id-badge">{{categorie?.id}}</span>
            </a>
          </td>
          <td>
            <span *ngIf="categorie.parentid" class="parent-category">
              <span class="modern-badge badge-secondary">
                <i data-feather="folder" class="badge-icon"></i>
                {{ getParentcategorie(categorie.parentid)?.id }} - {{ getParentcategorie(categorie.parentid)?.titre }}
              </span>
            </span>
            <span *ngIf="!categorie.parentid" class="text-muted">
              <i data-feather="minus" class="text-muted"></i>
              Root Category
            </span>
          </td>
          <td>
            <span class="category-title">{{ categorie.titre }}</span>
          </td>
          <td>
            <span class="category-description">{{ categorie.description }}</span>
          </td>
          <td>
            <div class="action-buttons">
              <a [routerLink]="['/affichercategorie', categorie.id]" class="btn-action btn-view" title="View">
                <i data-feather="eye" class="action-icon"></i>
              </a>
              <a [routerLink]="['/modifiercategorie', categorie.id]" class="btn-action btn-edit" title="Edit">
                <i data-feather="edit" class="action-icon"></i>
              </a>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Modern Pagination -->
<div class="modern-pagination-container">
  <div class="pagination-info">
    <span class="pagination-text">
      Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, categories.length) }}
      of {{ categories.length }} categories
    </span>
  </div>
  <div class="modern-pagination">
    <pagination-controls
      (pageChange)="currentPage = $event"
      previousLabel="Previous"
      nextLabel="Next"
      class="modern-pagination-controls">
    </pagination-controls>
  </div>
</div>
  
  
  