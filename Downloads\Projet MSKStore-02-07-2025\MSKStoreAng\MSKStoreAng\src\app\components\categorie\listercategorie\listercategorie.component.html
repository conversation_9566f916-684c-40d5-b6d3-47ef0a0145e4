<div class="float-right mr-3 my-2">
    <label>{{ 'Show' | translate }}</label>
    <select class="form-control" [(ngModel)]="pageSize" (change)="paginate($event)">
      <option value="5">5</option>
      <option value="10">10</option>
      <option value="20">20</option>
    </select>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>#</th>
        <th>{{ 'Parent' | translate }}</th>
        <th>{{ 'Title' | translate }}</th>
        <th>{{ 'Description' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let categorie of categories | paginate: { itemsPerPage: pageSize, currentPage: currentPage }">
        <td> <a [routerLink]="['/affichercategorie', categorie.id]"> {{categorie?.id}}</a></td>
        <td *ngIf="categorie.parentid">        {{ getParentcategorie(categorie.parentid)?.id }} - {{ getParentcategorie(categorie.parentid)?.titre }}
        </td>

        <td *ngIf="!categorie.parentid">-</td>
        <td>{{ categorie.titre }}</td>
        <td>{{ categorie.description }}</td>
        
      </tr>
    </tbody>




  </table>
  

  
  <div class="pagination">
    <pagination-controls (pageChange)="currentPage = $event" previousLabel="Previous" nextLabel="Next"></pagination-controls>
  </div>
  
  
  