// ================================================================================================
//   File Name: aggrid.scss
//   Description: SCC file for Aggrid.
//   ----------------------------------------------------------------------------------------------
//   Item Name: MskStore - Vuejs, React, Angular, HTML & Laravel Admin Dashboard Template
//   Author: Msk
//   Author URL: http://www.themeforest.net/user/Msk
// ================================================================================================

@import '../../bootstrap-extended/include'; // Bootstrap includes
@import '../../components/include'; // Components includes

.aggrid {
  height: 600px;
  font-size: 1rem;
  color: $body-color;

  // Checkbox Color
  .ag-icon-checkbox-checked,
  .ag-icon-checkbox-indeterminate {
    color: $primary !important;
  }

  /*** TABLE BODY HEIGHT - FOOTER HEIGHT ***/
  .ag-root-wrapper-body {
    min-height: calc(100% - 56px);
  }

  /*** HEADER TEXT  ***/
  .ag-header-cell-text {
    font-size: 1rem;
    font-family: $font-family-sans-serif;
  }

  /*** PAGINATION STYLING ***/
  .ag-paging-panel {
    display: block !important;
    align-items: center;
    height: 98px;

    .ag-paging-row-summary-panel {
      display: none;
    }

    .ag-paging-page-summary-panel {
      justify-content: center;
      margin-left: 0;
      margin-top: 18px;

      [ref='lbCurrent'],
      [ref='lbTotal'] {
        color: $white;
        padding: 0.7rem 0.95rem;
        border-radius: 0.5rem;

        @include media-breakpoint-down(xs) {
          margin: 0 0.4rem;
        }
      }
    }

    .ag-paging-button {
      background-color: $gray-300;
      border-radius: 50%;
      padding: 0.5rem 0rem;
      margin: 0 0.5rem;
      color: $body-color !important;
      min-width: 34px;

      @include media-breakpoint-down(xs) {
        margin-left: 0;
        margin-right: 0.4rem;
      }

      .ag-icon {
        color: $body-color !important;
        opacity: 1 !important;
      }

      &.ag-disabled {
        opacity: 0.5 !important;
      }

      &:last-child {
        @include media-breakpoint-down(xs) {
          margin-right: 0;
        }
      }
    }

    span[ref='lbCurrent'] {
      background-color: $primary;
    }

    span[ref='lbTotal'] {
      background-color: $gray-300;
      color: $body-color !important;
    }
  }

  /*** TABLE SCROLLBAR ***/
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: $gray-300;
    border-radius: 20px;
  }

  ::-webkit-scrollbar-track {
    background: $body-bg;
    border-radius: 20px;
  }
}

/*** PAGE FILTER DROPDOWN ***/
.filter-btn {
  padding: 1.2rem !important;
  border-radius: 5rem;
  font-size: 0.95rem;

  &:after {
    background-image: url(str-replace(str-replace($chevron-down, 'currentColor', $body-color), '#', '%23'));
    left: 5px;
  }
}

/*** SORTING DROPDOWN WIDTH ***/
.sort-dropdown {
  .dropdown-menu {
    min-width: 4rem;
  }
}
