using System;
using System.ComponentModel.DataAnnotations;

namespace MskStoreAPI.Models.Security
{
    /// <summary>
    /// Security audit log for tracking security events
    /// </summary>
    public class SecurityAuditLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [Required]
        [MaxLength(50)]
        public string EventType { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string Description { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? UserId { get; set; }

        [MaxLength(45)]
        public string? IpAddress { get; set; }

        [MaxLength(500)]
        public string? UserAgent { get; set; }

        [MaxLength(50)]
        public string Severity { get; set; } = "Info";

        [MaxLength(1000)]
        public string? AdditionalData { get; set; }

        public bool IsSuccessful { get; set; } = true;
    }

    /// <summary>
    /// Security event types for audit logging
    /// </summary>
    public static class SecurityEventTypes
    {
        public const string LOGIN_SUCCESS = "LOGIN_SUCCESS";
        public const string LOGIN_FAILED = "LOGIN_FAILED";
        public const string LOGOUT = "LOGOUT";
        public const string PASSWORD_CHANGE = "PASSWORD_CHANGE";
        public const string ACCOUNT_LOCKED = "ACCOUNT_LOCKED";
        public const string ACCOUNT_UNLOCKED = "ACCOUNT_UNLOCKED";
        public const string UNAUTHORIZED_ACCESS = "UNAUTHORIZED_ACCESS";
        public const string DATA_ACCESS = "DATA_ACCESS";
        public const string DATA_MODIFICATION = "DATA_MODIFICATION";
        public const string SUSPICIOUS_ACTIVITY = "SUSPICIOUS_ACTIVITY";
        public const string RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED";
        public const string ENCRYPTION_ERROR = "ENCRYPTION_ERROR";
        public const string DECRYPTION_ERROR = "DECRYPTION_ERROR";
    }

    /// <summary>
    /// Security severity levels
    /// </summary>
    public static class SecuritySeverity
    {
        public const string LOW = "Low";
        public const string MEDIUM = "Medium";
        public const string HIGH = "High";
        public const string CRITICAL = "Critical";
    }
}
