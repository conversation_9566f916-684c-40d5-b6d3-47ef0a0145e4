  <div class="card">
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          
          <div class="form-group">
            <label for="selectedClient">{{ 'SelectedClient' | translate }}</label>
            <select [(ngModel)]="selectedClient" (ngModelChange)="getClientCredit()" class="form-control">
              <option *ngFor="let client of clients" [value]="client.id">{{client.id}} - {{client.name}} {{client.lastname}} {{client.phone}}</option>
            </select>
          </div>

          <div class="form-group">
            <label for="credit">{{ 'Credit' | translate }}</label>
            <p id="credit" class="form-control-static">{{ clientCredit }}</p>
          </div>

        </div>
        <div class="col-md-6">

          <div class="form-group">
            <label for="credit-payment">{{ 'creditpayment' | translate }}</label>
            <input type="number" id="credit-payment" [(ngModel)]="creditPayment" [disabled]="!selectedClient" class="form-control">
          </div>

          <div class="form-group">
            <label for="credit-rest">{{ 'CreditRest' | translate }}</label>
            <p id="credit-rest" class="form-control-static">{{ clientCredit - creditPayment }}</p>
          </div>

        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <button type="button" class="btn btn-primary" (click)="saveCreditPayment()" [disabled]="!selectedClient || !creditPayment">{{ 'save' | translate }}</button>
        </div>
      </div>

      <hr>
      
      <div *ngIf="selectedClient">
        <h2 class="mb-4">{{ 'UnpaidFactures' | translate }}</h2>
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th>{{ 'Date' | translate }}</th>
                <th>{{ 'Total' | translate }}</th>
                <th>{{ 'Facture' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let facture of latestFactures">
                <td>{{ facture.datefacture | date: 'dd/MM/yyyy' }}</td>
                <td>{{ facture.total }}</td>
                <td>
                  <a [routerLink]="['/afficherfactureclient', facture.id]">
                    {{ facture.id }}
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
