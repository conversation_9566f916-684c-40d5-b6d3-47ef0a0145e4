# CRITICAL FIXES NEEDED FOR COMPILATION

## 1. UsersController.cs Fixes

### Add using statement at top of file:
```csharp
using System.Text.Json;
```

### Fix Line 168-172 (LOGIN_PASSWORD_EXPIRED):
```csharp
await _securityService.LogSecurityEventAsync(
    "LOGIN_PASSWORD_EXPIRED",
    $"Login with expired password for user: {sanitizedEmail}",
    user.id.ToString(),
    clientIp,
    Request.Headers["User-Agent"].ToString(),
    "INFO",
    true
);
```

### Fix Line 191-195 (LOGIN_SUCCESS):
```csharp
await _securityService.LogSecurityEventAsync(
    "LOGIN_SUCCESS",
    $"Successful login for user: {sanitizedEmail}",
    user.id.ToString(),
    clientIp,
    Request.Headers["User-Agent"].ToString(),
    "INFO",
    true
);
```

### Fix Line 221-223 (LOGIN_FAILED):
```csharp
await _securityService.LogSecurityEventAsync(
    "LOGIN_FAILED",
    $"Failed login attempt for user: {sanitizedEmail}",
    null,
    clientIp,
    Request.Headers["User-Agent"].ToString(),
    "WARNING",
    false
);
```

### Fix Line 293-297 (TOKEN_GENERATED):
```csharp
await _securityService.LogSecurityEventAsync(
    "TOKEN_GENERATED",
    $"JWT token generated for user: {user.email}",
    user.id.ToString(),
    clientIp,
    Request.Headers["User-Agent"].ToString(),
    "INFO",
    true,
    JsonSerializer.Serialize(new { sessionId, jti, expiresAt })
);
```

## 2. PasswordValidationService.cs Fixes

### Fix Line 161-163:
```csharp
await _securityService.LogSecurityEventAsync(
    "PASSWORD_VALIDATION_FAILED",
    $"Password validation failed for user {userId}",
    userId?.ToString(),
    ipAddress,
    userAgent,
    "WARNING",
    false
);
```

### Fix Line 206-208:
```csharp
await _securityService.LogSecurityEventAsync(
    "PASSWORD_POLICY_VIOLATION",
    $"Password policy violation for user {userId}",
    userId.ToString(),
    ipAddress,
    userAgent,
    "WARNING",
    false
);
```

### Fix Line 350:
```csharp
await _securityService.LogSecurityEventAsync(
    "PASSWORD_STRENGTH_CHECK",
    $"Password strength check for user {userId}",
    userId?.ToString(),
    ipAddress,
    userAgent,
    "INFO",
    true
);
```

### Fix Line 474-476:
```csharp
await _securityService.LogSecurityEventAsync(
    "PASSWORD_HISTORY_CHECK",
    $"Password history check for user {userId}",
    userId.ToString(),
    ipAddress,
    userAgent,
    "INFO",
    true
);
```

## 3. InputValidationMiddleware.cs Fixes

### Add using statement:
```csharp
using System.Text.Json;
```

### Fix Line 183-186:
```csharp
await _securityService.LogSecurityEventAsync(
    "INPUT_VALIDATION_FAILED",
    "Input validation failed",
    null,
    ipAddress,
    userAgent,
    "WARNING",
    false,
    JsonSerializer.Serialize(new { Path = context.Request.Path, Method = context.Request.Method, QueryString = context.Request.QueryString.ToString() })
);
```

## METHOD SIGNATURE REFERENCE:
```csharp
Task LogSecurityEventAsync(string eventType, string description, string? userId = null, 
    string? ipAddress = null, string? userAgent = null, string severity = "Info", 
    bool isSuccessful = true, string? additionalData = null);
```
