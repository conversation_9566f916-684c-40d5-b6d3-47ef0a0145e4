# Final fix for PasswordValidationService.cs

Write-Host "Fixing PasswordValidationService.cs..." -ForegroundColor Green

$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath -Raw

# Fix line 161-163: Add missing parameters and fix order
$content = $content -replace '(\s+)userId,(\s+)result\.IsValid \? "INFO" : "WARNING",(\s+)result\.IsValid', '$1userId?.ToString(),$2ipAddress,$2userAgent,$2result.IsValid ? "INFO" : "WARNING",$3result.IsValid'

# Fix line 206-208: Add missing parameters and fix order
$content = $content -replace '(\s+)userId,(\s+)"WARNING",(\s+)false', '$1userId.ToString(),$2ipAddress,$2userAgent,$2"WARNING",$3false'

# Fix line 350: Add missing parameters and fix order
$content = $content -replace '(\s+)userId\?\.ToString\(\),(\s+)"INFO",(\s+)true', '$1userId?.ToString(),$2ipAddress,$2userAgent,$2"INFO",$3true'

# Fix line 474-476: Add missing parameters and fix order
$content = $content -replace '(\s+)userId,(\s+)"INFO",(\s+)true', '$1userId.ToString(),$2ipAddress,$2userAgent,$2"INFO",$3true'

# More specific fixes for the exact patterns
$content = $content -replace 'userId,\s*result\.IsValid \? "INFO" : "WARNING",\s*result\.IsValid', 'userId?.ToString(), ipAddress, userAgent, result.IsValid ? "INFO" : "WARNING", result.IsValid'

$content = $content -replace 'userId,\s*"WARNING",\s*false', 'userId.ToString(), ipAddress, userAgent, "WARNING", false'

$content = $content -replace 'userId,\s*"INFO",\s*true', 'userId.ToString(), ipAddress, userAgent, "INFO", true'

Set-Content $passwordServicePath $content

Write-Host "Fixed PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "Running build..." -ForegroundColor Green
dotnet build
