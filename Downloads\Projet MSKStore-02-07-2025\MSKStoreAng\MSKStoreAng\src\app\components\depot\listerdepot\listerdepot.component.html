<div class="float-right mr-3 my-2">
    <label>{{ 'Show' | translate }}</label>
    <select class="form-control" [(ngModel)]="pageSize" (change)="paginate($event)">
      <option value="5">5</option>
      <option value="10">10</option>
      <option value="20">20</option>
    </select>
</div>

<table class="table">
    <thead>
        <tr>
            <th>#</th>
            <th>{{ 'Name' | translate }}</th>
            <th>{{ 'type' | translate }}</th>
        </tr>
    </thead>
    <tbody>
        <tr *ngFor="let depot of depots | paginate: { itemsPerPage: pageSize, currentPage: currentPage }">
            <td><a [routerLink]="['/afficherdepot', depot.id]">{{depot.id}}</a></td>
            <td>{{depot.nom}}</td>
            <td>{{depot.type}}</td>
            
        </tr>
    </tbody>
</table>

<div class="pagination">
    <pagination-controls (pageChange)="currentPage = $event" previousLabel="Previous" nextLabel="Next"></pagination-controls>
</div>