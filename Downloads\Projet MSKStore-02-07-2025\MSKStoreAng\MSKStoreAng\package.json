{"name": "msk-store", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "14.1.0", "@angular/cdk": "13.3.9", "@angular/common": "14.1.0", "@angular/compiler": "14.1.0", "@angular/core": "14.1.0", "@angular/flex-layout": "12.0.0-beta.34", "@angular/forms": "^14.1.0", "@angular/localize": "14.1.0", "@angular/platform-browser": "14.1.0", "@angular/platform-browser-dynamic": "14.1.0", "@angular/router": "14.1.0", "@circlon/angular-tree-component": "11.0.4", "@ng-bootstrap/ng-bootstrap": "11.0.0-rc.0", "@ng-select/ng-select": "6.1.0", "@ngx-translate/core": "13.0.0", "@swimlane/ngx-datatable": "^20.1.0", "@zxing/browser": "^0.1.3", "@zxing/library": "^0.20.0", "@zxing/ngx-scanner": "^3.9.0", "angularx-qrcode": "^15.0.1", "animate.css": "4.1.1", "bootstrap": "4.6.1", "bs-stepper": "1.7.0", "feather-icons": "4.28.0", "flatpickr": "4.6.9", "hammerjs": "2.0.8", "jwt-decode": "^3.1.2", "katex": "0.13.11", "lodash": "4.17.21", "ng-block-ui": "3.0.2", "ngx-barcode": "^0.3.0", "ngx-barcode-scanner": "^0.1.2", "ngx-bootstrap": "^10.2.0", "ngx-highlightjs": "4.1.4", "ngx-pagination": "^6.0.3", "ngx-perfect-scrollbar": "10.1.1", "node-waves": "0.7.6", "nouislider": "15.1.1", "plyr": "3.6.8", "quill": "1.3.7", "rxjs": "^6.6.3", "swiper": "6.7.0", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.10", "@angular/cli": "14.1.0", "@angular/compiler-cli": "14.1.0", "@angularclass/hmr": "3.0.0", "@types/jest": "^29.4.0", "jasmine-core": "3.7.0", "karma": "^6.4.1", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "4.0.0", "karma-jasmine-html-reporter": "1.5.0", "typescript": "4.7.4"}}