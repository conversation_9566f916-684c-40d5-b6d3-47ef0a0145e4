# PowerShell script to update the SQLite database with security tables

Write-Host "Updating database with security tables..." -ForegroundColor Yellow

try {
    # Load the SQL script
    $sqlScript = Get-Content -Path "create-security-tables.sql" -Raw
    
    # Connection string for SQLite database
    $connectionString = "Data Source=storedbAdel.db"
    
    # Load SQLite assembly
    Add-Type -AssemblyName "System.Data.SQLite"
    
    # Create connection
    $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to database successfully" -ForegroundColor Green
    
    # Execute the SQL script
    $command = $connection.CreateCommand()
    $command.CommandText = $sqlScript
    $result = $command.ExecuteNonQuery()
    
    Write-Host "Security tables created successfully!" -ForegroundColor Green
    
    $connection.Close()
    Write-Host "Database update completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "Error updating database: $($_.Exception.Message)" -ForegroundColor Red
    if ($connection -and $connection.State -eq "Open") {
        $connection.Close()
    }
    exit 1
}
