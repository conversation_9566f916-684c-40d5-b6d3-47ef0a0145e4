# Backend Setup Commands

## After installing .NET 7 SDK, run these commands in order:

### 1. Navigate to API directory
```bash
cd "Downloads\Projet MSKStore-02-07-2025\MskStoreAPI\MskStoreAPI"
```

### 2. Restore NuGet packages
```bash
dotnet restore
```

### 3. Build the project
```bash
dotnet build
```

### 4. Create database migration (if needed)
```bash
dotnet ef migrations add InitialCreate
```

### 5. Update database
```bash
dotnet ef database update
```

### 6. Run the API
```bash
dotnet run
```

## Expected Results:
- API should start on: `https://localhost:7258`
- Swagger UI available at: `https://localhost:7258/swagger`
- Angular frontend (already running) will connect to this API

## Database Connection:
- Uses SQL Server with Windows Authentication
- Database name: `storedbAdel`
- Connection string configured in `appsettings.json`

## Fixed Issues:
✅ Target framework consistency (.NET 7.0)
✅ Package version conflicts resolved
✅ DataContext type mapping corrected
✅ Authentication/Authorization order verified
✅ CORS configuration for Angular frontend
✅ Removed conflicting commented code
