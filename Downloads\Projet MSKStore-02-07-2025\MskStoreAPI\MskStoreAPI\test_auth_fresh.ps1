try {
    $body = @{
        authemail = "<EMAIL>"
        authpassword = "SecurePass123!"
    } | ConvertTo-Json

    Write-Host "Testing authentication with fresh server..."
    Write-Host "Request body: $body"
    
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Users/<USER>" -Method POST -ContentType "application/json" -Body $body
    
    Write-Host "SUCCESS: Authentication successful!"
    Write-Host "Response: $($response | ConvertTo-Json)"
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "Status Code: $statusCode"
        
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody"
        } catch {
            Write-Host "Could not read response body"
        }
    }
}
