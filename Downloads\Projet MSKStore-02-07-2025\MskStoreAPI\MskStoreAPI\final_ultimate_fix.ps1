# Final ultimate fix for the last error in PasswordValidationService.cs

Write-Host "Fixing the final error in PasswordValidationService.cs..." -ForegroundColor Green

$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath

# Find and fix the specific pattern around line 353
for ($i = 0; $i -lt $content.Length; $i++) {
    if ($content[$i] -match '^\s+"INFO",\s*$' -and $content[$i+1] -match '^\s+true\s*$') {
        Write-Host "Found problematic pattern at lines $($i+1) and $($i+2)" -ForegroundColor Yellow
        Write-Host "Line $($i+1): $($content[$i])" -ForegroundColor Yellow
        Write-Host "Line $($i+2): $($content[$i+1])" -ForegroundColor Yellow
        
        # Insert missing null parameters before "INFO"
        $newLine1 = "                    null,"
        $newLine2 = "                    null,"
        
        # Insert the new lines before the "INFO" line
        $content = $content[0..($i-1)] + $newLine1 + $newLine2 + $content[$i..($content.Length-1)]
        Write-Host "Inserted missing null parameters" -ForegroundColor Green
        break
    }
}

Set-Content $passwordServicePath $content

Write-Host "Fixed the final error in PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "Running build..." -ForegroundColor Green
dotnet build
