# PowerShell script to clear rate limiting by deleting failed login attempts
# This will reset the rate limiting for testing

# Bypass SSL certificate validation
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12

try {
    Write-Host "Clearing rate limiting by calling database cleanup endpoint..."
    
    # Call the database cleanup endpoint to clear login attempts
    $response = Invoke-RestMethod -Uri "http://localhost:5095/api/Database/create-security-tables" -Method POST
    Write-Host "Database cleanup response: $response"
    
    Write-Host "Rate limiting should now be cleared. You can try authentication again."
} catch {
    Write-Host "Error clearing rate limit: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)"
    }
}
