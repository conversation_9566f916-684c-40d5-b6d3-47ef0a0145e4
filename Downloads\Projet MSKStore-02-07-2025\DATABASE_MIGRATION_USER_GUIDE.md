# 🗄️ Database Migration & User Creation Guide

## 📋 Overview
Your MSKStore project already has a complete user system with:
- ✅ **User Models**: User, Admin, Employee classes
- ✅ **Database Context**: Configured with Entity Framework
- ✅ **User Controller**: API endpoints for user management
- ✅ **Existing Migration**: `20230526195941_initial.cs`

## 🚀 Database Setup Process

### Step 1: Navigate to Backend Directory
```bash
cd "Downloads\Projet MSKStore-02-07-2025\MskStoreAPI\MskStoreAPI"
```

### Step 2: Check Current Migration Status
```bash
# List existing migrations
dotnet ef migrations list

# Check database status
dotnet ef database update --dry-run
```

### Step 3: Apply Existing Migration
```bash
# This will create the database and all tables including User table
dotnet ef database update
```

**Expected Output:**
```
Build started...
Build succeeded.
Applying migration '20230526195941_initial'.
Done.
```

## 👥 User System Architecture

### 🏗️ User Models Structure
```csharp
// Base User class
public class User
{
    public int id { get; set; }
    public string? name { get; set; }
    public string? lastName { get; set; }
    public string? email { get; set; }
    public string? password { get; set; }  // BCrypt hashed
    public string? phone { get; set; }
    public string? role { get; set; }      // "Admin" or "Employee"
    public string? photo { get; set; }
    public string? token { get; set; }
}

// Employee extends User
public class Employee : User
{
    public int? cin { get; set; }
    public string? typecontrat { get; set; }
    public decimal? salaire { get; set; }
    public decimal? salairebrut { get; set; }
    public int? permission { get; set; }   // Bitwise permissions
}

// Admin extends User  
public class Admin : User
{
    // Inherits all User properties
}
```

### 🔐 Permission System
```
Permission bits (Employee only):
- 1 = Sale permission
- 2 = Deposit permission  
- 4 = Provider permission
- 7 = All permissions (1+2+4)
```

## 🛠️ Creating Users

### Method 1: Via Swagger API (Recommended)

#### 1. Start the Backend
```bash
dotnet run
```

#### 2. Open Swagger UI
```
https://localhost:7258/swagger
```

#### 3. Create Admin User
```json
POST /api/Users
{
  "name": "Admin",
  "lastName": "User",
  "email": "<EMAIL>",
  "password": "Admin123!",
  "phone": "**********",
  "role": "Admin",
  "photo": null
}
```

#### 4. Create Employee User
```json
POST /api/Users
{
  "name": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "Employee123!",
  "phone": "**********",
  "role": "Employee",
  "cin": 12345678,
  "typecontrat": "CDI",
  "salaire": 3000.00,
  "salairebrut": 3500.00,
  "permission": 7
}
```

### Method 2: Via Frontend (Angular)

#### 1. Access User Management
```
http://localhost:4200/utilisateur/ajouter
```

#### 2. Fill the Form
- Name, Last Name, Email, Password
- Phone, Role (Admin/Employee)
- For Employee: CIN, Contract Type, Salary, Permissions

### Method 3: Direct Database Insert (SQL)

#### 1. Connect to SQL Server
```sql
USE storedbAdel;
```

#### 2. Insert Admin User
```sql
INSERT INTO [User] (name, lastName, email, password, phone, role, photo, token)
VALUES ('Admin', 'User', '<EMAIL>', 
        '$2a$11$encrypted_password_hash', '**********', 'Admin', NULL, NULL);
```

## 🔄 Migration Commands Reference

### Create New Migration
```bash
# If you modify models and need new migration
dotnet ef migrations add MigrationName
```

### Apply Migrations
```bash
# Apply all pending migrations
dotnet ef database update

# Apply specific migration
dotnet ef database update MigrationName
```

### Remove Last Migration
```bash
# Remove last migration (if not applied to database)
dotnet ef migrations remove
```

### Reset Database
```bash
# Drop database and recreate
dotnet ef database drop
dotnet ef database update
```

## 🧪 Testing User Creation

### 1. Test via Swagger
```
1. POST /api/Users (create user)
2. POST /api/Users/<USER>
3. GET /api/Users (list users - requires auth)
```

### 2. Test via Frontend
```
1. Go to: http://localhost:4200/pages/authentication/login-v2
2. Login with created user
3. Navigate to user management
```

### 3. Verify in Database
```sql
-- Check created users
SELECT id, name, lastName, email, role FROM [User];

-- Check employees with permissions
SELECT u.name, u.email, e.permission, e.salaire 
FROM [User] u 
JOIN [User] e ON u.id = e.id 
WHERE u.role = 'Employee';
```

## 🔐 Password Security

### Automatic Password Hashing
```csharp
// In UsersController.PostUser()
user.password = BCryptNet.HashPassword(userDto.password);
```

### Authentication Process
```csharp
// In UsersController.Authenticate()
var isPasswordValid = BCryptNet.Verify(model.authpassword, user.password);
```

## 📊 Database Schema

### User Table Structure
```sql
CREATE TABLE [User] (
    id int IDENTITY(1,1) PRIMARY KEY,
    name nvarchar(max),
    lastName nvarchar(max),
    email nvarchar(max),
    password nvarchar(max),  -- BCrypt hashed
    phone nvarchar(max),
    role nvarchar(max) NOT NULL,  -- Discriminator
    photo nvarchar(max),
    token nvarchar(max),
    -- Employee specific fields
    cin int,
    typecontrat nvarchar(max),
    salaire decimal(18,2),
    salairebrut decimal(18,2),
    permission int
);
```

## 🎯 Quick Start Commands

```bash
# 1. Setup database
cd MskStoreAPI\MskStoreAPI
dotnet ef database update

# 2. Start backend
dotnet run

# 3. Create first admin user via Swagger
# Go to: https://localhost:7258/swagger
# POST /api/Users with admin data

# 4. Test login
# POST /api/Users/<USER>

# 5. Access frontend
# Go to: http://localhost:4200
```

## ✅ Success Verification

After setup, you should have:
- ✅ Database `storedbAdel` created
- ✅ User table with proper schema
- ✅ Admin user created and can login
- ✅ Employee users with permissions
- ✅ Frontend can authenticate users
- ✅ Swagger API working for user management

**Your user system is ready for production use!** 🎊
