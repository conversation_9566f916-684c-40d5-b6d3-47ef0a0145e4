using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace MskStoreAPI.Attributes
{
    /// <summary>
    /// Validates that the input doesn't contain malicious patterns
    /// </summary>
    public class NoMaliciousContentAttribute : ValidationAttribute
    {
        private static readonly Regex[] MaliciousPatterns = new[]
        {
            new Regex(@"<script[^>]*>.*?</script>", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"javascript:", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"on\w+\s*=", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(union|select|insert|update|delete|drop|create|alter|exec|execute)\s+", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(\||&|;|\$|\`|>|<)", RegexOptions.Compiled),
            new Regex(@"\.\.\/|\.\.\\", RegexOptions.Compiled)
        };

        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrEmpty(value.ToString()))
                return true;

            var input = value.ToString()!;
            return !MaliciousPatterns.Any(pattern => pattern.IsMatch(input));
        }

        public override string FormatErrorMessage(string name)
        {
            return $"Le champ {name} contient du contenu potentiellement malveillant.";
        }
    }

    /// <summary>
    /// Validates email format with enhanced security checks
    /// </summary>
    public class SecureEmailAttribute : ValidationAttribute
    {
        private static readonly Regex EmailRegex = new Regex(
            @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrEmpty(value.ToString()))
                return true;

            var email = value.ToString()!.Trim().ToLowerInvariant();
            
            // Basic format validation
            if (!EmailRegex.IsMatch(email))
                return false;

            // Additional security checks
            if (email.Length > 254) // RFC 5321 limit
                return false;

            // Check for suspicious patterns
            var suspiciousPatterns = new[]
            {
                @"\.{2,}", // Multiple consecutive dots
                @"^\.|\.$", // Starting or ending with dot
                @"@.*@", // Multiple @ symbols
                @"[<>\""]" // Dangerous characters
            };

            return !suspiciousPatterns.Any(pattern => Regex.IsMatch(email, pattern));
        }

        public override string FormatErrorMessage(string name)
        {
            return $"Le champ {name} doit contenir une adresse email valide.";
        }
    }

    /// <summary>
    /// Validates password strength
    /// </summary>
    public class StrongPasswordAttribute : ValidationAttribute
    {
        public int MinLength { get; set; } = 8;
        public int MaxLength { get; set; } = 128;
        public bool RequireUppercase { get; set; } = true;
        public bool RequireLowercase { get; set; } = true;
        public bool RequireDigits { get; set; } = true;
        public bool RequireSpecialCharacters { get; set; } = true;

        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrEmpty(value.ToString()))
                return false;

            var password = value.ToString()!;

            // Length check
            if (password.Length < MinLength || password.Length > MaxLength)
                return false;

            // Character requirements
            if (RequireUppercase && !password.Any(char.IsUpper))
                return false;

            if (RequireLowercase && !password.Any(char.IsLower))
                return false;

            if (RequireDigits && !password.Any(char.IsDigit))
                return false;

            if (RequireSpecialCharacters && !password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)))
                return false;

            // Check for common weak patterns
            var weakPatterns = new[]
            {
                @"(.)\1{2,}", // Repeated characters (3 or more)
                @"(012|123|234|345|456|567|678|789|890)", // Sequential numbers
                @"(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)", // Sequential letters
                @"^(password|admin|user|test|guest|root|administrator)", // Common weak passwords
                @"(qwerty|azerty|asdf|zxcv)" // Keyboard patterns
            };

            return !weakPatterns.Any(pattern => Regex.IsMatch(password.ToLowerInvariant(), pattern));
        }

        public override string FormatErrorMessage(string name)
        {
            var requirements = new List<string>();
            
            if (RequireUppercase) requirements.Add("une majuscule");
            if (RequireLowercase) requirements.Add("une minuscule");
            if (RequireDigits) requirements.Add("un chiffre");
            if (RequireSpecialCharacters) requirements.Add("un caractère spécial");

            return $"Le {name} doit contenir au moins {MinLength} caractères et inclure : {string.Join(", ", requirements)}.";
        }
    }

    /// <summary>
    /// Validates that input doesn't exceed maximum length and contains safe characters
    /// </summary>
    public class SafeStringAttribute : ValidationAttribute
    {
        public int MaxLength { get; set; } = 255;
        public bool AllowSpecialCharacters { get; set; } = false;

        public override bool IsValid(object? value)
        {
            if (value == null)
                return true;

            var input = value.ToString()!;

            // Length check
            if (input.Length > MaxLength)
                return false;

            // Character validation
            if (!AllowSpecialCharacters)
            {
                // Only allow alphanumeric, spaces, and basic punctuation
                var allowedPattern = @"^[a-zA-Z0-9\s\-_.,!?()]+$";
                if (!Regex.IsMatch(input, allowedPattern))
                    return false;
            }

            // Check for malicious patterns
            var maliciousPatterns = new[]
            {
                @"<[^>]*>", // HTML tags
                @"javascript:", // JavaScript protocol
                @"data:", // Data protocol
                @"vbscript:", // VBScript protocol
                @"on\w+\s*=", // Event handlers
                @"(eval|setTimeout|setInterval)\s*\(" // Dangerous functions
            };

            return !maliciousPatterns.Any(pattern => Regex.IsMatch(input, pattern, RegexOptions.IgnoreCase));
        }

        public override string FormatErrorMessage(string name)
        {
            return $"Le champ {name} contient des caractères non autorisés ou dépasse la longueur maximale de {MaxLength} caractères.";
        }
    }

    /// <summary>
    /// Validates phone number format
    /// </summary>
    public class PhoneNumberAttribute : ValidationAttribute
    {
        private static readonly Regex PhoneRegex = new Regex(
            @"^(\+33|0)[1-9](\d{8})$|^(\+33|0)[6-7](\d{8})$",
            RegexOptions.Compiled);

        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrEmpty(value.ToString()))
                return true;

            var phone = value.ToString()!.Replace(" ", "").Replace("-", "").Replace(".", "");
            return PhoneRegex.IsMatch(phone);
        }

        public override string FormatErrorMessage(string name)
        {
            return $"Le champ {name} doit contenir un numéro de téléphone français valide.";
        }
    }

    /// <summary>
    /// Validates that numeric input is within safe range
    /// </summary>
    public class SafeNumericAttribute : ValidationAttribute
    {
        public double MinValue { get; set; } = double.MinValue;
        public double MaxValue { get; set; } = double.MaxValue;

        public override bool IsValid(object? value)
        {
            if (value == null)
                return true;

            if (double.TryParse(value.ToString(), out double numericValue))
            {
                return numericValue >= MinValue && numericValue <= MaxValue;
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return $"Le champ {name} doit être un nombre entre {MinValue} et {MaxValue}.";
        }
    }
}
