<div class="password-strength-container" *ngIf="password">
  <!-- Strength Meter -->
  <div class="strength-meter mb-2">
    <div class="strength-label d-flex justify-content-between align-items-center mb-1">
      <small class="text-muted">Force du mot de passe:</small>
      <small class="font-weight-bold" [style.color]="getStrengthColor()">
        {{ strengthResult.level }} ({{ strengthResult.score }}/100)
      </small>
    </div>
    
    <div class="progress" style="height: 6px;">
      <div 
        class="progress-bar transition-all" 
        [class]="getStrengthClass()"
        [style.width]="getProgressWidth()"
        [style.background-color]="getStrengthColor()">
      </div>
    </div>
  </div>

  <!-- Requirements Checklist -->
  <div class="requirements-checklist mb-2" *ngIf="showRequirements">
    <small class="text-muted d-block mb-1">Exigences:</small>
    <div class="requirement-item d-flex align-items-center mb-1">
      <i class="feather" 
         [class.icon-check]="strengthResult.requirements.length" 
         [class.icon-x]="!strengthResult.requirements.length"
         [class.text-success]="strengthResult.requirements.length"
         [class.text-danger]="!strengthResult.requirements.length"
         style="font-size: 14px; margin-right: 6px;"></i>
      <small [class.text-success]="strengthResult.requirements.length" 
             [class.text-muted]="!strengthResult.requirements.length">
        Au moins 8 caractères
      </small>
    </div>
    
    <div class="requirement-item d-flex align-items-center mb-1">
      <i class="feather" 
         [class.icon-check]="strengthResult.requirements.uppercase" 
         [class.icon-x]="!strengthResult.requirements.uppercase"
         [class.text-success]="strengthResult.requirements.uppercase"
         [class.text-danger]="!strengthResult.requirements.uppercase"
         style="font-size: 14px; margin-right: 6px;"></i>
      <small [class.text-success]="strengthResult.requirements.uppercase" 
             [class.text-muted]="!strengthResult.requirements.uppercase">
        Une lettre majuscule
      </small>
    </div>
    
    <div class="requirement-item d-flex align-items-center mb-1">
      <i class="feather" 
         [class.icon-check]="strengthResult.requirements.lowercase" 
         [class.icon-x]="!strengthResult.requirements.lowercase"
         [class.text-success]="strengthResult.requirements.lowercase"
         [class.text-danger]="!strengthResult.requirements.lowercase"
         style="font-size: 14px; margin-right: 6px;"></i>
      <small [class.text-success]="strengthResult.requirements.lowercase" 
             [class.text-muted]="!strengthResult.requirements.lowercase">
        Une lettre minuscule
      </small>
    </div>
    
    <div class="requirement-item d-flex align-items-center mb-1">
      <i class="feather" 
         [class.icon-check]="strengthResult.requirements.numbers" 
         [class.icon-x]="!strengthResult.requirements.numbers"
         [class.text-success]="strengthResult.requirements.numbers"
         [class.text-danger]="!strengthResult.requirements.numbers"
         style="font-size: 14px; margin-right: 6px;"></i>
      <small [class.text-success]="strengthResult.requirements.numbers" 
             [class.text-muted]="!strengthResult.requirements.numbers">
        Un chiffre
      </small>
    </div>
    
    <div class="requirement-item d-flex align-items-center mb-1">
      <i class="feather" 
         [class.icon-check]="strengthResult.requirements.special" 
         [class.icon-x]="!strengthResult.requirements.special"
         [class.text-success]="strengthResult.requirements.special"
         [class.text-danger]="!strengthResult.requirements.special"
         style="font-size: 14px; margin-right: 6px;"></i>
      <small [class.text-success]="strengthResult.requirements.special" 
             [class.text-muted]="!strengthResult.requirements.special">
        Un caractère spécial (!@#$%^&*)
      </small>
    </div>
  </div>

  <!-- Suggestions -->
  <div class="suggestions" *ngIf="showSuggestions && strengthResult.feedback.length > 0">
    <small class="text-muted d-block mb-1">Suggestions d'amélioration:</small>
    <div class="suggestion-item" *ngFor="let suggestion of strengthResult.feedback">
      <small class="text-info d-flex align-items-center">
        <i class="feather icon-info" style="font-size: 12px; margin-right: 4px;"></i>
        {{ suggestion }}
      </small>
    </div>
  </div>
</div>
