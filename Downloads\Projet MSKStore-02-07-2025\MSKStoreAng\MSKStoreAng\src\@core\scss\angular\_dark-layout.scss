// Dark layout overrides

.dark-layout {
  .popover {
    &.bs-popover-top {
      .arrow:after {
        border-top-color: $theme-dark-card-bg;
      }
    }
    &.bs-popover-right {
      .arrow:after {
        border-right-color: $theme-dark-card-bg;
      }
    }
    &.bs-popover-bottom {
      .arrow:after {
        border-bottom-color: $primary;
      }
    }
    &.bs-popover-left {
      .arrow:after {
        border-left-color: $theme-dark-card-bg;
      }
    }
    &.bs-popover-top {
      .arrow:before {
        border-top-color: $theme-dark-border-color;
      }
    }
    &.bs-popover-right {
      .arrow:before {
        border-right-color: $theme-dark-border-color;
      }
    }
    &.bs-popover-bottom {
      .arrow:before {
        border-bottom-color: $primary;
      }
    }
    &.bs-popover-left {
      .arrow:before {
        border-left-color: $theme-dark-border-color;
      }
    }
  }

  .ng-select,
  .ng-select div,
  .ng-select input,
  .ng-select span {
    color: $theme-dark-body-color;
  }
}
