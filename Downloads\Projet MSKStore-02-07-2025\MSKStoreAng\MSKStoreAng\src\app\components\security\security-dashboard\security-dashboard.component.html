<div class="content-wrapper container-xxl p-0">
  <div class="content-body">
    <!-- Security Dashboard Header -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title">
              <i class="feather icon-shield"></i>
              Tableau de Bord Sécurité
            </h4>
            <div class="btn-group">
              <button class="btn btn-outline-primary btn-sm" (click)="refreshDashboard()">
                <i class="feather icon-refresh-cw"></i>
                Actualiser
              </button>
              <button class="btn btn-outline-success btn-sm" (click)="exportEvents()">
                <i class="feather icon-download"></i>
                Exporter
              </button>
              <button class="btn btn-outline-danger btn-sm" (click)="clearAllEvents()">
                <i class="feather icon-trash-2"></i>
                Effacer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Session Status -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="d-flex align-items-center">
                  <div class="avatar bg-light-success mr-1">
                    <div class="avatar-content">
                      <i class="feather icon-clock font-medium-3"></i>
                    </div>
                  </div>
                  <div class="my-auto">
                    <h4 class="font-weight-bolder mb-0">{{ formatSessionTimeout() }}</h4>
                    <p class="card-text font-small-3 mb-0">Temps de session restant</p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="d-flex align-items-center">
                  <div class="avatar mr-1" [ngClass]="isSessionActive ? 'bg-light-success' : 'bg-light-danger'">
                    <div class="avatar-content">
                      <i class="feather" [ngClass]="isSessionActive ? 'icon-check' : 'icon-x'" class="font-medium-3"></i>
                    </div>
                  </div>
                  <div class="my-auto">
                    <h4 class="font-weight-bolder mb-0">{{ isSessionActive ? 'Active' : 'Inactive' }}</h4>
                    <p class="card-text font-small-3 mb-0">État de la session</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Security Statistics -->
    <div class="row">
      <div class="col-lg-3 col-sm-6 col-12">
        <div class="card">
          <div class="card-header d-flex flex-column align-items-start pb-0">
            <div class="avatar bg-light-primary p-50 m-0">
              <div class="avatar-content">
                <i class="feather icon-activity font-medium-3"></i>
              </div>
            </div>
            <h2 class="font-weight-bolder mt-1">{{ totalEvents }}</h2>
          </div>
          <div class="card-body px-0 pb-1">
            <div id="gained-chart"></div>
            <div class="d-flex justify-content-between">
              <span class="font-small-2">Total Événements</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-sm-6 col-12">
        <div class="card">
          <div class="card-header d-flex flex-column align-items-start pb-0">
            <div class="avatar bg-light-danger p-50 m-0">
              <div class="avatar-content">
                <i class="feather icon-alert-octagon font-medium-3"></i>
              </div>
            </div>
            <h2 class="font-weight-bolder mt-1">{{ criticalEvents }}</h2>
          </div>
          <div class="card-body px-0 pb-1">
            <div class="d-flex justify-content-between">
              <span class="font-small-2">Événements Critiques</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-sm-6 col-12">
        <div class="card">
          <div class="card-header d-flex flex-column align-items-start pb-0">
            <div class="avatar bg-light-warning p-50 m-0">
              <div class="avatar-content">
                <i class="feather icon-alert-triangle font-medium-3"></i>
              </div>
            </div>
            <h2 class="font-weight-bolder mt-1">{{ highSeverityEvents }}</h2>
          </div>
          <div class="card-body px-0 pb-1">
            <div class="d-flex justify-content-between">
              <span class="font-small-2">Haute Sévérité</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-sm-6 col-12">
        <div class="card">
          <div class="card-header d-flex flex-column align-items-start pb-0">
            <div class="avatar bg-light-success p-50 m-0">
              <div class="avatar-content">
                <i class="feather icon-check-circle font-medium-3"></i>
              </div>
            </div>
            <h2 class="font-weight-bolder mt-1">{{ lowSeverityEvents }}</h2>
          </div>
          <div class="card-body px-0 pb-1">
            <div class="d-flex justify-content-between">
              <span class="font-small-2">Faible Sévérité</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Security Events Table -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title">Événements de Sécurité</h4>
            
            <!-- Filters -->
            <div class="row mt-1">
              <div class="col-md-4">
                <label for="severityFilter">Filtrer par Sévérité:</label>
                <select id="severityFilter" class="form-control" [(ngModel)]="selectedSeverity" (change)="applyFilters()">
                  <option value="ALL">Toutes les sévérités</option>
                  <option value="CRITICAL">Critique</option>
                  <option value="HIGH">Haute</option>
                  <option value="MEDIUM">Moyenne</option>
                  <option value="LOW">Faible</option>
                </select>
              </div>
              <div class="col-md-4">
                <label for="eventTypeFilter">Filtrer par Type:</label>
                <select id="eventTypeFilter" class="form-control" [(ngModel)]="selectedEventType" (change)="applyFilters()">
                  <option value="ALL">Tous les types</option>
                  <option *ngFor="let type of getUniqueEventTypes()" [value]="type">{{ type }}</option>
                </select>
              </div>
              <div class="col-md-4">
                <label>&nbsp;</label>
                <div class="d-block">
                  <span class="badge badge-light">{{ filteredEvents.length }} événement(s) trouvé(s)</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Type</th>
                    <th>Description</th>
                    <th>Sévérité</th>
                    <th>Horodatage</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let event of getPaginatedEvents()">
                    <td>
                      <div class="d-flex align-items-center">
                        <i [class]="getEventTypeIcon(event.type)" class="mr-1"></i>
                        <span>{{ event.type }}</span>
                      </div>
                    </td>
                    <td>
                      <span class="font-small-2">{{ event.description }}</span>
                    </td>
                    <td>
                      <span class="badge" [ngClass]="getSeverityBadgeClass(event.severity)">
                        {{ event.severity }}
                      </span>
                    </td>
                    <td>
                      <span class="font-small-2">{{ formatTimestamp(event.timestamp) }}</span>
                    </td>
                  </tr>
                  <tr *ngIf="getPaginatedEvents().length === 0">
                    <td colspan="4" class="text-center">
                      <div class="py-2">
                        <i class="feather icon-info font-large-1 text-muted"></i>
                        <p class="text-muted mt-1">Aucun événement de sécurité trouvé</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- Pagination -->
            <div class="row" *ngIf="totalPages > 1">
              <div class="col-12">
                <nav aria-label="Page navigation">
                  <ul class="pagination justify-content-center">
                    <li class="page-item" [class.disabled]="currentPage === 1">
                      <a class="page-link" (click)="changePage(currentPage - 1)" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                      </a>
                    </li>
                    <li class="page-item" 
                        *ngFor="let page of [].constructor(totalPages); let i = index" 
                        [class.active]="currentPage === i + 1">
                      <a class="page-link" (click)="changePage(i + 1)">{{ i + 1 }}</a>
                    </li>
                    <li class="page-item" [class.disabled]="currentPage === totalPages">
                      <a class="page-link" (click)="changePage(currentPage + 1)" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
