# Final correct fix for PasswordValidationService.cs

Write-Host "Fixing the parameter issue in PasswordValidationService.cs..." -ForegroundColor Green

$passwordServicePath = "Services\PasswordValidationService.cs"
$content = Get-Content $passwordServicePath -Raw

# Fix the incorrect parameter count on line 475
# Remove the extra null parameters that were added incorrectly
$content = $content -replace '(\s+)userId\.ToString\(\),(\s+)null, null,(\s+)"INFO",(\s+)true', '$1userId.ToString(),$2null,$2null,$2"INFO",$4true'

Set-Content $passwordServicePath $content

Write-Host "Fixed the parameter issue in PasswordValidationService.cs" -ForegroundColor Yellow

Write-Host "Running build..." -ForegroundColor Green
dotnet build
