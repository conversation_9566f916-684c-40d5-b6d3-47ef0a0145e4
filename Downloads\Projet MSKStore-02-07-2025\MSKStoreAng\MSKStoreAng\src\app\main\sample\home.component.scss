// Dashboard Header
.dashboard-header {
  .dashboard-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--bs-body-color);
    transition: color 0.3s ease;
  }

  .dashboard-subtitle {
    font-size: 1rem;
    margin-top: 0.25rem;
    color: var(--bs-body-color);
    transition: color 0.3s ease;
  }

  .dashboard-date {
    font-size: 0.875rem;
    color: var(--bs-secondary);
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
  }
}

// Stats Cards
.stats-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  background: var(--bs-card-bg, #fff);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 35px 0 rgba(0, 0, 0, 0.15);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
  }

  .stats-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);

    i {
      width: 28px;
      height: 28px;
    }
  }

  h3 {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
  }

  p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
  }
}

// Gradient backgrounds
.bg-gradient-primary {
  background: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #28c76f 0%, #48da89 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #ff9f43 0%, #ffb976 100%);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #00cfe8 0%, #26e5ff 100%);
}

// Security Overview
.security-overview {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
  background: var(--bs-card-bg, #fff);
  transition: all 0.3s ease;

  .card-header {
    background: linear-gradient(135deg, #ea5455 0%, #feb692 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border: none;

    .card-title {
      color: white;
      display: flex;
      align-items: center;
    }

    .btn-outline-primary {
      border-color: rgba(255, 255, 255, 0.5);
      color: white;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: white;
        color: white;
      }
    }
  }

  .card-body {
    background: var(--bs-card-bg, #fff);
    color: var(--bs-body-color);
    transition: all 0.3s ease;
  }
}

.security-stat {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: var(--bs-light, rgba(0, 0, 0, 0.05));
  }

  .security-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;

    i {
      width: 24px;
      height: 24px;
    }
  }

  .security-stat-content {
    h5 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
      color: var(--bs-body-color);
      transition: color 0.3s ease;
    }

    p {
      color: var(--bs-secondary);
      margin-bottom: 0;
      transition: color 0.3s ease;
    }
  }
}

// Quick Actions
.quick-action-card {
  display: block;
  text-decoration: none;
  color: var(--bs-body-color);
  text-align: center;
  padding: 1.5rem 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: var(--bs-card-bg, #fff);

  &:hover {
    color: var(--bs-body-color);
    text-decoration: none;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
  }

  .quick-action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;

    i {
      width: 28px;
      height: 28px;
    }
  }

  h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--bs-body-color);
    transition: color 0.3s ease;
  }

  small {
    font-size: 0.75rem;
    color: var(--bs-secondary);
    transition: color 0.3s ease;
  }
}

// Recent Events
.recent-events {
  background: var(--bs-card-bg, #fff);
  border-radius: 12px;
  transition: all 0.3s ease;

  .event-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--bs-border-color);
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    .event-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;

      i {
        width: 20px;
        height: 20px;
      }
    }

    .event-content {
      flex: 1;

      h6 {
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: var(--bs-body-color);
        transition: color 0.3s ease;
      }

      small {
        font-size: 0.75rem;
        color: var(--bs-secondary);
        transition: color 0.3s ease;
      }
    }
  }
}

// Light background variants
.bg-light-primary {
  background-color: rgba(115, 103, 240, 0.12);
}

.bg-light-success {
  background-color: rgba(40, 199, 111, 0.12);
}

.bg-light-warning {
  background-color: rgba(255, 159, 67, 0.12);
}

.bg-light-info {
  background-color: rgba(0, 207, 232, 0.12);
}

.bg-light-danger {
  background-color: rgba(234, 84, 85, 0.12);
}

.bg-light-secondary {
  background-color: rgba(130, 134, 139, 0.12);
}

// Dark theme specific styles
.dark-layout {
  .stats-card {
    background: var(--bs-card-bg, #283046);
    box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.25);

    &:hover {
      box-shadow: 0 8px 35px 0 rgba(0, 0, 0, 0.35);
    }

    &::before {
      background: rgba(255, 255, 255, 0.1);
    }
  }

  .security-overview {
    background: var(--bs-card-bg, #283046);
    box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.25);

    .card-body {
      background: var(--bs-card-bg, #283046);
    }
  }

  .quick-action-card {
    background: var(--bs-card-bg, #283046);
    box-shadow: 0 2px 15px 0 rgba(0, 0, 0, 0.2);

    &:hover {
      box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.3);
    }
  }

  .recent-events {
    background: var(--bs-card-bg, #283046);
  }

  .security-stat {
    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
  }
}

// Light theme specific styles
.default-layout {
  .stats-card {
    background: #fff;
    box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);

    &:hover {
      box-shadow: 0 8px 35px 0 rgba(0, 0, 0, 0.15);
    }
  }

  .security-overview {
    background: #fff;
    box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);

    .card-body {
      background: #fff;
    }
  }

  .quick-action-card {
    background: #fff;
    box-shadow: 0 2px 15px 0 rgba(0, 0, 0, 0.08);

    &:hover {
      box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);
    }
  }

  .recent-events {
    background: #fff;
  }

  .security-stat {
    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .dashboard-header {
    text-align: center;
    margin-bottom: 2rem;

    .dashboard-date {
      justify-content: center;
      margin-top: 1rem;
    }
  }

  .stats-card {
    margin-bottom: 1rem;
  }

  .quick-action-card {
    margin-bottom: 1rem;
  }

  .security-stat {
    margin-bottom: 1rem;
  }
}

// Animation for loading states
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.6s ease-out;
}

// Custom scrollbar for events
.recent-events {
  max-height: 300px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bs-light);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--bs-secondary);
    border-radius: 2px;

    &:hover {
      background: var(--bs-dark);
    }
  }
}