// ================================================================================================
//  File Name: app-ecommerce-details.scss
//  Description: App Ecommerce Details SCSS.
//  ----------------------------------------------------------------------------------------------
//  Item Name: MskStore - Vuejs, React, Angular, HTML & Laravel Admin Dashboard Template
//  Author: Msk
//  Author URL: http://www.themeforest.net/user/Msk
// ================================================================================================

@import '../bootstrap-extended/include'; // Bootstrap includes
@import '../components/include'; // Components includes

// App e-commerce detail style
.ecommerce-application {
  .app-ecommerce-details {
    .product-img {
      width: 600px;
    }
    // ratings style
    .ratings-list-item {
      svg,
      i {
        font-size: 1.286rem;
        height: 1.286rem;
        width: 1.286rem;
      }
    }
    .filled-star {
      fill: $warning;
      stroke: $warning;
      color: $warning;
    }
    .unfilled-star {
      stroke: $gray-100;
      color: $gray-100;
    }

    // Item Price
    .item-price {
      color: $primary;
    }
    // Common for list-view, checkout and wishlist
    .item-company {
      display: inline-flex;
      font-weight: $font-weight-normal;
      font-size: 0.875rem;

      .company-name {
        font-weight: $font-weight-bolder;
        margin-left: 0.25rem;
      }
    }

    // Product feature list
    .product-features {
      margin-top: 1.5rem;
      margin-bottom: 1.5rem;
      li {
        svg,
        i {
          height: 1.4rem;
          width: 1.4rem;
          font-size: 1.4rem;
          margin-right: 0.75rem;
        }
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        span {
          font-weight: $font-weight-bolder;
        }
      }
    }

    .product-color-options {
      margin-top: 1.5rem;
      margin-bottom: 1.2rem;
    }
    .btn-wishlist {
      .text-danger {
        color: $danger;
        fill: $danger;
      }
    }
    .btn-share {
      .btn-icon {
        ~ .dropdown-menu {
          min-width: 3rem;
        }
      }
    }
    .item-features {
      background-color: $body-bg;
      padding-top: 5.357rem;
      padding-bottom: 5.357rem;
      i,
      svg {
        font-size: 2.5rem;
        height: 2.5rem;
        width: 2.5rem;
        color: $primary;
      }
    }
  }

  // centered slides option-1
  .swiper-responsive-breakpoints {
    &.swiper-container {
      .swiper-slide {
        text-align: center;
        background-color: $body-bg;
        padding: 1.5rem 3rem;
        border-radius: $card-border-radius;
        .img-container {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 250px;
        }
      }
    }
  }

  //navigation button custom icons
  .swiper-button-prev,
  .swiper-button-next,
  .swiper-container-rtl .swiper-button-prev,
  .swiper-container-rtl .swiper-button-next {
    background-image: none;
    width: 35px;
    font-size: $h1-font-size;

    &:focus {
      outline: none;
    }
  }

  .swiper-button-prev {
    left: 0;

    &:after {
      background-image: url(str-replace(str-replace($chevron-left, 'currentColor', $primary), '#', '%23'));
      background-repeat: no-repeat;
      background-position: center;
      background-size: 44px;
      color: $body-color;
      width: 44px;
      height: 44px;
      content: '';
    }
  }

  .swiper-button-next {
    right: 0;

    &:after {
      background-image: url(str-replace(str-replace($chevron-right, 'currentColor', $primary), '#', '%23'));
      background-repeat: no-repeat;
      background-position: center;
      background-size: 44px;
      color: $body-color;
      width: 44px;
      height: 44px;
      content: '';
    }
  }

  .swiper-container-rtl {
    .swiper-button-prev {
      &:after {
        background-image: url(str-replace(str-replace($chevron-right, 'currentColor', $primary), '#', '%23'));
        background-repeat: no-repeat;
        background-position: center;
        background-size: 44px;
        color: $body-color;
        width: 44px;
        height: 44px;
        content: '';
      }
    }

    .swiper-button-next {
      &:after {
        background-image: url(str-replace(str-replace($chevron-left, 'currentColor', $primary), '#', '%23'));
        background-repeat: no-repeat;
        background-position: center;
        background-size: 44px;
        color: $body-color;
        width: 44px;
        height: 44px;
        content: '';
      }
    }
  }

  // color options

  .product-color-options {
    .color-option {
      border: 1px solid transparent;
      border-radius: 50%;
      position: relative;
      cursor: pointer;
      padding: 3px;

      .filloption {
        height: 18px;
        width: 18px;
        border-radius: 50%;
      }
    }

    // selected
    .selected {
      // these use for border color only page specific
      .b-primary {
        border-color: $primary;
        .filloption {
          box-shadow: 0 2px 4px 0 rgba($primary, 0.4);
        }
      }

      .b-success {
        border-color: $success;
        .filloption {
          box-shadow: 0 2px 4px 0 rgba($success, 0.4);
        }
      }

      .b-danger {
        border-color: $danger;
        .filloption {
          box-shadow: 0 2px 4px 0 rgba($danger, 0.4);
        }
      }

      .b-warning {
        border-color: $warning;
        .filloption {
          box-shadow: 0 2px 4px 0 rgba($warning, 0.4);
        }
      }

      .b-info {
        border-color: $info;
        .filloption {
          box-shadow: 0 2px 4px 0 rgba($info, 0.4);
        }
      }
    }
    .b-primary .filloption {
      box-shadow: 0 2px 4px 0 rgba($primary, 0.4);
    }
    .b-success .filloption {
      box-shadow: 0 2px 4px 0 rgba($success, 0.4);
    }
    .b-danger .filloption {
      box-shadow: 0 2px 4px 0 rgba($danger, 0.4);
    }
    .b-warning .filloption {
      box-shadow: 0 2px 4px 0 rgba($warning, 0.4);
    }
    .b-info .filloption {
      box-shadow: 0 2px 4px 0 rgba($info, 0.4);
    }
  }
}

@include media-breakpoint-down(sm) {
  .ecommerce-application .swiper-responsive-breakpoints.swiper-container .swiper-slide {
    padding: 1rem;
  }
}
@include media-breakpoint-down(sm) {
  .ecommerce-application {
    .app-ecommerce-details {
      // ratings style
      .ratings-list-item {
        svg,
        i {
          font-size: 1rem;
          height: 1rem;
          width: 1rem;
        }
      }
    }
  }
}
