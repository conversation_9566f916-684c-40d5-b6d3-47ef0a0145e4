@import '@core/scss/base/bootstrap-extended/include'; // Bootstrap includes
@import '@core/scss/base/components/include'; // Components includes

// ===================================================================
// Modern Navbar Styles
// ===================================================================

.modern-navbar-header {
  background: var(--bg-surface);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);

  .modern-navbar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    padding: var(--spacing-4) var(--spacing-6);
    transition: all var(--transition-fast);

    &:hover {
      text-decoration: none;
      transform: translateY(-1px);
    }

    .modern-brand-logo {
      margin-right: var(--spacing-3);

      img {
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-sm);
      }
    }

    .modern-brand-text {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}

.modern-navbar-container {
  background: var(--bg-surface);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-3) var(--spacing-6);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 40;

  .modern-navbar-left {
    gap: var(--spacing-4);

    .modern-nav-link {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-2);
      border-radius: var(--radius-md);
      color: var(--text-secondary);
      text-decoration: none;
      transition: all var(--transition-fast);
      background: transparent;
      border: none;
      cursor: pointer;

      &:hover {
        background: var(--color-gray-100);
        color: var(--color-primary);
        transform: translateY(-1px);
        text-decoration: none;
      }

      .modern-nav-icon {
        width: 1.25rem;
        height: 1.25rem;
      }
    }

    .modern-menu-toggle {
      .modern-nav-icon {
        color: var(--color-primary);
      }
    }

    .modern-theme-toggle {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: -2px;
        border-radius: var(--radius-md);
        background: var(--gradient-primary);
        opacity: 0;
        transition: opacity var(--transition-fast);
        z-index: -1;
      }

      &:hover::before {
        opacity: 0.1;
      }
    }
  }

  .modern-quick-actions {
    margin: 0 var(--spacing-6);

    .modern-quick-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      padding: var(--spacing-2) var(--spacing-4);
      border-radius: var(--radius-md);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      transition: all var(--transition-fast);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
      }

      &:hover::before {
        left: 100%;
      }

      .btn-icon {
        width: 1rem;
        height: 1rem;
      }
    }
  }

  .modern-navbar-nav {
    gap: var(--spacing-2);

    .modern-dropdown {
      .modern-nav-link {
        display: flex;
        align-items: center;
        padding: var(--spacing-2) var(--spacing-3);
        border-radius: var(--radius-md);
        color: var(--text-secondary);
        text-decoration: none;
        transition: all var(--transition-fast);

        &:hover {
          background: var(--color-gray-100);
          color: var(--color-primary);
          text-decoration: none;
        }

        .flag-icon {
          margin-right: var(--spacing-2);
          border-radius: var(--radius-sm);
          box-shadow: var(--shadow-sm);
        }
      }

      .dropdown-menu {
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-2);
        margin-top: var(--spacing-2);
        background: var(--bg-surface);

        .dropdown-item {
          padding: var(--spacing-2) var(--spacing-3);
          border-radius: var(--radius-md);
          color: var(--text-secondary);
          font-size: var(--text-sm);
          transition: all var(--transition-fast);

          &:hover {
            background: var(--color-primary-50);
            color: var(--color-primary);
          }

          &.active {
            background: var(--color-primary);
            color: var(--text-inverse);
          }

          .flag-icon {
            margin-right: var(--spacing-2);
            border-radius: var(--radius-sm);
          }
        }
      }
    }
  }
}

// User dropdown styles
.modern-user-dropdown {
  .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);

    &:hover {
      background: var(--color-gray-100);
      color: var(--color-primary);
      text-decoration: none;
    }

    .user-avatar {
      width: 2rem;
      height: 2rem;
      border-radius: var(--radius-full);
      border: 2px solid var(--border-color);
      object-fit: cover;
    }

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .user-name {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        color: var(--text-primary);
        margin: 0;
      }

      .user-role {
        font-size: var(--text-xs);
        color: var(--text-muted);
        margin: 0;
      }
    }
  }

  .dropdown-menu {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-3);
    margin-top: var(--spacing-2);
    background: var(--bg-surface);
    min-width: 200px;

    .dropdown-header {
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--text-xs);
      font-weight: var(--font-semibold);
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: var(--spacing-2);
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--radius-md);
      color: var(--text-secondary);
      font-size: var(--text-sm);
      transition: all var(--transition-fast);

      &:hover {
        background: var(--color-primary-50);
        color: var(--color-primary);
      }

      &.text-danger:hover {
        background: var(--color-danger-50);
        color: var(--color-danger);
      }

      i, svg {
        width: 1rem;
        height: 1rem;
      }
    }

    .dropdown-divider {
      margin: var(--spacing-2) 0;
      border-color: var(--border-color);
    }
  }
}

// To open dd on right
.dropdown-menu-right {
  right: 0 !important;
  left: auto !important;
}

// Cart Touchspin
.touchspin-cart {
  .touchspin-wrapper {
    width: 6.4rem;
  }
  &:focus-within {
    box-shadow: none !important;
  }
}

app-navbar-bookmark {
  display: flex;
}

// Apply style on window scroll for navbar static type
.navbar-static-style-on-scroll {
  background-color: #fff !important;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 4px 20px 0px !important;
}

// Dark Layout
.dark-layout {
  .navbar-container {
    .search-input {
      .search-list {
        li {
          &.auto-suggestion {
            &:hover {
              background-color: $theme-dark-body-bg;
            }
          }
        }
      }
    }
  }
}
