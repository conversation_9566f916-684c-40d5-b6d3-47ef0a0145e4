  <div class="modern-form-container">
    <div class="modern-form-header">
      <h3 class="modern-form-title">
        <i data-feather="package" class="title-icon"></i>
        {{ 'AddProduct' | translate }}
      </h3>
      <p class="modern-form-subtitle">Fill in the product information below</p>
    </div>

    <form class="modern-form" (submit)="onSubmit()">
      <!-- Basic Product Information -->
      <div class="modern-form-section">
        <h4 class="section-title">
          <i data-feather="info" class="section-icon"></i>
          Basic Information
        </h4>

        <div class="form-row">
          <div class="form-group col-md-6">
            <label class="modern-form-label" for="nom">
              <i data-feather="package" class="label-icon"></i>
              {{ 'ProductName' | translate }}
              <span class="required">*</span>
            </label>
            <input
              type="text"
              class="form-control modern-input"
              id="nom"
              name="nom"
              [(ngModel)]="newProduit.nom"
              placeholder="Enter product name"
              required>
          </div>

          <div class="form-group col-md-6">
            <label class="modern-form-label" for="codebar">
              <i data-feather="hash" class="label-icon"></i>
              {{ 'BarCode' | translate }}
            </label>
            <input
              type="number"
              class="form-control modern-input"
              id="codebar"
              name="codebar"
              [(ngModel)]="newProduit.codebar"
              placeholder="Enter barcode">

            <div class="modern-checkbox-container">
              <label class="modern-checkbox">
                <input
                  type="checkbox"
                  id="scannerCheckbox"
                  name="scannerCheckbox"
                  [(ngModel)]="scanner">
                <span class="checkmark"></span>
                <span class="checkbox-label">
                  <i data-feather="camera" class="checkbox-icon"></i>
                  {{ 'Enablescanner' | translate }}
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Scanner Section -->
      <div class="modern-form-section" *ngIf="scanner">
        <h4 class="section-title">
          <i data-feather="camera" class="section-icon"></i>
          Barcode Scanner
        </h4>

        <div class="scanner-container" [hidden]="!hasDevices">
          <div class="scanner-controls">
            <label class="modern-form-label" for="deviceSelect">
              <i data-feather="video" class="label-icon"></i>
              Select Camera Device
            </label>
            <select class="form-control modern-select" id="deviceSelect" (change)="onDeviceSelectChange($event.target.value)">
              <option value="" [selected]="!currentDevice">{{ 'NoDeviceSelected' | translate }}</option>
              <option *ngFor="let device of availableDevices" [value]="device.deviceId"
                [selected]="currentDevice && device.deviceId === currentDevice.deviceId">{{device.label}}</option>
            </select>
          </div>

          <div class="scanner-viewport">
            <zxing-scanner
              class="modern-scanner"
              [torch]="torchEnabled"
              [(device)]="currentDevice"
              (scanSuccess)="onCodeResult($event)"
              [formats]="formatsEnabled"
              [tryHarder]="tryHarder"
              (permissionResponse)="onHasPermission($event)"
              (camerasFound)="onCamerasFound($event)"
              (torchCompatible)="onTorchCompatible($event)">
            </zxing-scanner>
          </div>

          <div class="scanner-result" *ngIf="qrResultString">
            <div class="result-card">
              <i data-feather="check-circle" class="result-icon"></i>
              <div class="result-content">
                <small class="result-label">{{ 'Result' | translate }}</small>
                <strong class="result-value">{{ qrResultString }}</strong>
              </div>
            </div>
          </div>
        </div>

        <div class="scanner-error" *ngIf="hasPermission === false || hasPermission === undefined || hasDevices === undefined || hasDevices === false">
          <div class="error-card">
            <i data-feather="alert-circle" class="error-icon"></i>
            <h4 class="error-title">{{ 'Couldntcheckbarcode' | translate }}</h4>
            <p class="error-message">Please check camera permissions and device availability</p>
          </div>
        </div>
      </div>

      <!-- Product Details Section -->
      <div class="modern-form-section">
        <h4 class="section-title">
          <i data-feather="file-text" class="section-icon"></i>
          Product Details
        </h4>

        <div class="form-row">
          <div class="form-group col-md-6">
            <label class="modern-form-label" for="description">
              <i data-feather="file-text" class="label-icon"></i>
              {{ 'Description' | translate }}
            </label>
            <textarea
              class="form-control modern-textarea"
              id="description"
              name="description"
              [(ngModel)]="newProduit.description"
              placeholder="Enter product description"
              rows="4"></textarea>
          </div>

          <div class="form-group col-md-6">
            <label class="modern-form-label" for="color">
              <i data-feather="palette" class="label-icon"></i>
              {{ 'Color' | translate }}
            </label>
            <input
              type="text"
              class="form-control modern-input"
              id="color"
              name="color"
              [(ngModel)]="newProduit.color"
              placeholder="Enter product color">
          </div>
        </div>
      </div>


      <!-- Pricing Information Section -->
      <div class="modern-form-section">
        <h4 class="section-title">
          <i data-feather="dollar-sign" class="section-icon"></i>
          Pricing Information
        </h4>

        <div class="form-row">
          <div class="form-group col-md-6">
            <label class="modern-form-label" for="price">
              <i data-feather="tag" class="label-icon"></i>
              {{ 'Price' | translate }}
              <span class="required">*</span>
            </label>
            <input
              type="number"
              class="form-control modern-input"
              id="price"
              name="price"
              [(ngModel)]="newProduit.prixvente"
              placeholder="Enter selling price"
              min="0"
              step="0.01"
              required>
          </div>

          <div class="form-group col-md-6" *ngIf="!newProduit.ispiece">
            <label class="modern-form-label" for="prixFournisseur">
              <i data-feather="shopping-cart" class="label-icon"></i>
              {{ 'ProviderPrice' | translate }}
            </label>
            <input
              type="number"
              class="form-control modern-input"
              id="prixFournisseur"
              name="prixFournisseur"
              [(ngModel)]="newProduit.prixFournisseur"
              placeholder="Enter provider price"
              min="0"
              step="0.01">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-6">
            <label class="modern-form-label" for="reductionprix">
              <i data-feather="percent" class="label-icon"></i>
              {{ 'PriceReduction' | translate }}
            </label>
            <input
              type="number"
              class="form-control modern-input"
              id="reductionprix"
              name="reductionprix"
              [(ngModel)]="newProduit.reductionprix"
              placeholder="Enter price reduction"
              min="0"
              step="0.01">
          </div>

          <div class="form-group col-md-6">
            <label class="modern-form-label" for="taxe">
              <i data-feather="calculator" class="label-icon"></i>
              {{ 'Taxe' | translate }}
            </label>
            <input
              type="number"
              class="form-control modern-input"
              id="taxe"
              name="taxe"
              [(ngModel)]="newProduit.taxe"
              placeholder="Enter tax amount"
              min="0"
              step="0.01">
          </div>
        </div>
      </div>

      <!-- Category and Expiration Section -->
      <div class="modern-form-section">
        <h4 class="section-title">
          <i data-feather="folder" class="section-icon"></i>
          Category & Expiration
        </h4>

        <div class="form-row">
          <div class="form-group col-md-6">
            <label class="modern-form-label" for="category">
              <i data-feather="folder" class="label-icon"></i>
              {{ 'Category' | translate }}
              <span class="required">*</span>
            </label>
            <select
              id="category"
              name="category"
              class="form-control modern-select"
              [(ngModel)]="newProduit.categorieid"
              required>
              <option value="" disabled>Select a category</option>
              <option *ngFor="let category of categories" [value]="category.id">{{category.id}} - {{category.titre}}</option>
            </select>
          </div>

          <div class="form-group col-md-6">
            <label class="modern-form-label" for="dateexpiration">
              <i data-feather="calendar" class="label-icon"></i>
              {{ 'ExpirationDate' | translate }}
            </label>
            <input
              type="date"
              class="form-control modern-input"
              id="dateexpiration"
              name="dateexpiration"
              [(ngModel)]="newProduit.dateexpiration">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-6">
            <div class="modern-checkbox-container">
              <label class="modern-checkbox">
                <input
                  type="checkbox"
                  id="ispiece"
                  name="ispiece"
                  [(ngModel)]="newProduit.ispiece">
                <span class="checkmark"></span>
                <span class="checkbox-label">
                  <i data-feather="package" class="checkbox-icon"></i>
                  {{ 'IsPiece' | translate }}
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <!-- Piece Configuration Section -->
      <div class="modern-form-section" *ngIf="newProduit.ispiece">
        <h4 class="section-title">
          <i data-feather="layers" class="section-icon"></i>
          Piece Configuration
        </h4>

        <div class="form-row">
          <div class="form-group col-md-6">
            <label class="modern-form-label" for="produitparent">
              <i data-feather="link" class="label-icon"></i>
              {{ 'IsPieceof' | translate }}
            </label>
            <input
              type="number"
              class="form-control modern-input"
              id="produitparent"
              name="produitparent"
              [(ngModel)]="newProduit.produitparent"
              (ngModelChange)="onProduitParentChange()"
              placeholder="Enter parent product ID">
            <div class="parent-info" *ngIf="parent">
              <i data-feather="info" class="info-icon"></i>
              <span class="info-text">{{ 'ispieceof' | translate }} {{parent.nom}}</span>
            </div>
          </div>

          <div class="form-group col-md-6">
            <label class="modern-form-label" for="qteInPacket">
              <i data-feather="package" class="label-icon"></i>
              {{ 'QuantityinPacket' | translate }}
            </label>
            <input
              type="number"
              class="form-control modern-input"
              id="qteInPacket"
              name="qteInPacket"
              [(ngModel)]="newProduit.qteInPacket"
              placeholder="Enter quantity in packet"
              min="1">
          </div>
        </div>
      </div>

      <!-- Stock Management Section -->
      <div class="modern-form-section" *ngIf="!newProduit.ispiece">
        <h4 class="section-title">
          <i data-feather="archive" class="section-icon"></i>
          Stock Management
        </h4>

        <div class="form-row">
          <div class="form-group col-md-6">
            <label class="modern-form-label" for="selectedDepot">
              <i data-feather="map-pin" class="label-icon"></i>
              {{ 'colonne' | translate }}
            </label>
            <select
              [(ngModel)]="selectedColonne"
              class="form-control modern-select"
              [ngModelOptions]="{standalone: true}">
              <option value="" disabled>Select a column</option>
              <option *ngFor="let colonne of colonnes" [value]="colonne.id">{{ colonne.nom }}</option>
            </select>
          </div>

          <div class="form-group col-md-6">
            <label class="modern-form-label" for="stock">
              <i data-feather="layers" class="label-icon"></i>
              {{ 'stock' | translate }}
            </label>
            <input
              type="number"
              class="form-control modern-input"
              id="stock"
              name="stock"
              [(ngModel)]="stock"
              placeholder="Enter initial stock quantity"
              min="0">
          </div>
        </div>
      </div>

      <!-- Photo Upload Section -->
      <div class="modern-form-section">
        <h4 class="section-title">
          <i data-feather="image" class="section-icon"></i>
          Product Photos
        </h4>

        <div class="form-group">
          <label class="modern-form-label" for="photoUpload">
            <i data-feather="upload" class="label-icon"></i>
            {{ 'photo' | translate }}
          </label>
          <div class="file-upload-container">
            <input
              type="file"
              class="form-control modern-file-input"
              id="photoUpload"
              name="photoUpload"
              (change)="onFileSelect($event)"
              multiple
              accept="image/*">
            <div class="file-upload-hint">
              <i data-feather="upload-cloud" class="hint-icon"></i>
              <span>Select multiple images for this product</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="modern-form-actions">
        <button type="submit" class="btn-modern btn-primary">
          <i data-feather="plus" class="btn-icon"></i>
          {{ 'AddProduct' | translate }}
        </button>
        <button type="button" class="btn-modern btn-secondary" onclick="history.back()">
          <i data-feather="x" class="btn-icon"></i>
          Cancel
        </button>
      </div>
    </form>
  </div>