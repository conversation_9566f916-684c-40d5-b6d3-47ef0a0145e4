import { Injectable } from '@angular/core';
import { SecurityService } from './security.service';

export interface CSPDirective {
  directive: string;
  sources: string[];
}

export interface CSPPolicy {
  directives: CSPDirective[];
  reportUri?: string;
  reportOnly?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CSPService {
  private defaultPolicy: CSPPolicy = {
    directives: [
      {
        directive: 'default-src',
        sources: ["'self'"]
      },
      {
        directive: 'script-src',
        sources: ["'self'", "'unsafe-inline'", "'unsafe-eval'"]
      },
      {
        directive: 'style-src',
        sources: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com']
      },
      {
        directive: 'img-src',
        sources: ["'self'", 'data:', 'https:']
      },
      {
        directive: 'font-src',
        sources: ["'self'", 'https://fonts.gstatic.com', 'data:']
      },
      {
        directive: 'connect-src',
        sources: ["'self'", 'https://api.mskstore.com']
      },
      {
        directive: 'frame-ancestors',
        sources: ["'none'"]
      },
      {
        directive: 'base-uri',
        sources: ["'self'"]
      },
      {
        directive: 'form-action',
        sources: ["'self'"]
      },
      {
        directive: 'object-src',
        sources: ["'none'"]
      },
      {
        directive: 'media-src',
        sources: ["'self'"]
      }
    ],
    reportUri: '/api/security/csp-report',
    reportOnly: false
  };

  constructor(private securityService: SecurityService) {
    this.initializeCSP();
    this.monitorCSPViolations();
  }

  /**
   * Initialize Content Security Policy
   */
  private initializeCSP(): void {
    try {
      // Apply CSP via meta tag (fallback if server headers are not set)
      this.applyCSPMetaTag();
      
      // Log CSP initialization
      this.securityService.logSecurityEvent({
        type: 'CSP_INITIALIZED',
        description: 'Content Security Policy initialized',
        timestamp: new Date(),
        severity: 'LOW'
      });
    } catch (error) {
      this.securityService.logSecurityEvent({
        type: 'CSP_INIT_ERROR',
        description: `Error initializing CSP: ${error}`,
        timestamp: new Date(),
        severity: 'HIGH'
      });
    }
  }

  /**
   * Apply CSP via meta tag
   */
  private applyCSPMetaTag(): void {
    const existingMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (existingMeta) {
      return; // CSP already set by server
    }

    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = this.buildCSPString(this.defaultPolicy);
    
    document.head.appendChild(meta);
  }

  /**
   * Build CSP string from policy object
   */
  private buildCSPString(policy: CSPPolicy): string {
    const directives = policy.directives.map(directive => 
      `${directive.directive} ${directive.sources.join(' ')}`
    );

    if (policy.reportUri) {
      directives.push(`report-uri ${policy.reportUri}`);
    }

    return directives.join('; ');
  }

  /**
   * Monitor CSP violations
   */
  private monitorCSPViolations(): void {
    document.addEventListener('securitypolicyviolation', (event) => {
      this.handleCSPViolation(event as SecurityPolicyViolationEvent);
    });

    // Also listen for report-uri violations (older browsers)
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'csp-violation') {
        this.handleCSPViolationReport(event.data);
      }
    });
  }

  /**
   * Handle CSP violation events
   */
  private handleCSPViolation(event: SecurityPolicyViolationEvent): void {
    const violation = {
      blockedURI: event.blockedURI,
      violatedDirective: event.violatedDirective,
      originalPolicy: event.originalPolicy,
      sourceFile: event.sourceFile,
      lineNumber: event.lineNumber,
      columnNumber: event.columnNumber,
      sample: event.sample
    };

    // Determine severity based on violation type
    let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM';
    
    if (violation.violatedDirective.includes('script-src')) {
      severity = 'HIGH'; // Script violations are serious
    } else if (violation.violatedDirective.includes('object-src') || 
               violation.violatedDirective.includes('frame-ancestors')) {
      severity = 'CRITICAL'; // These could indicate XSS attempts
    }

    this.securityService.logSecurityEvent({
      type: 'CSP_VIOLATION',
      description: `CSP violation: ${violation.violatedDirective} blocked ${violation.blockedURI}`,
      timestamp: new Date(),
      severity
    });

    // Send violation report to server
    this.sendViolationReport(violation);
  }

  /**
   * Handle CSP violation reports (legacy)
   */
  private handleCSPViolationReport(report: any): void {
    this.securityService.logSecurityEvent({
      type: 'CSP_VIOLATION_REPORT',
      description: `CSP violation report: ${JSON.stringify(report)}`,
      timestamp: new Date(),
      severity: 'MEDIUM'
    });
  }

  /**
   * Send violation report to server
   */
  private sendViolationReport(violation: any): void {
    try {
      fetch('/api/security/csp-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          'csp-report': violation,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      }).catch(error => {
        console.error('Failed to send CSP violation report:', error);
      });
    } catch (error) {
      console.error('Error sending CSP violation report:', error);
    }
  }

  /**
   * Update CSP policy dynamically
   */
  updatePolicy(newPolicy: Partial<CSPPolicy>): void {
    try {
      // Merge with default policy
      const updatedPolicy: CSPPolicy = {
        ...this.defaultPolicy,
        ...newPolicy,
        directives: newPolicy.directives || this.defaultPolicy.directives
      };

      // Remove existing meta tag
      const existingMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      if (existingMeta) {
        existingMeta.remove();
      }

      // Apply new policy
      const meta = document.createElement('meta');
      meta.httpEquiv = 'Content-Security-Policy';
      meta.content = this.buildCSPString(updatedPolicy);
      
      document.head.appendChild(meta);

      this.securityService.logSecurityEvent({
        type: 'CSP_UPDATED',
        description: 'Content Security Policy updated',
        timestamp: new Date(),
        severity: 'LOW'
      });
    } catch (error) {
      this.securityService.logSecurityEvent({
        type: 'CSP_UPDATE_ERROR',
        description: `Error updating CSP: ${error}`,
        timestamp: new Date(),
        severity: 'HIGH'
      });
    }
  }

  /**
   * Add source to specific directive
   */
  addSource(directive: string, source: string): void {
    try {
      const directiveObj = this.defaultPolicy.directives.find(d => d.directive === directive);
      if (directiveObj && !directiveObj.sources.includes(source)) {
        directiveObj.sources.push(source);
        this.updatePolicy(this.defaultPolicy);
        
        this.securityService.logSecurityEvent({
          type: 'CSP_SOURCE_ADDED',
          description: `Added source ${source} to ${directive}`,
          timestamp: new Date(),
          severity: 'LOW'
        });
      }
    } catch (error) {
      this.securityService.logSecurityEvent({
        type: 'CSP_SOURCE_ADD_ERROR',
        description: `Error adding source to CSP: ${error}`,
        timestamp: new Date(),
        severity: 'MEDIUM'
      });
    }
  }

  /**
   * Remove source from specific directive
   */
  removeSource(directive: string, source: string): void {
    try {
      const directiveObj = this.defaultPolicy.directives.find(d => d.directive === directive);
      if (directiveObj) {
        const index = directiveObj.sources.indexOf(source);
        if (index > -1) {
          directiveObj.sources.splice(index, 1);
          this.updatePolicy(this.defaultPolicy);
          
          this.securityService.logSecurityEvent({
            type: 'CSP_SOURCE_REMOVED',
            description: `Removed source ${source} from ${directive}`,
            timestamp: new Date(),
            severity: 'LOW'
          });
        }
      }
    } catch (error) {
      this.securityService.logSecurityEvent({
        type: 'CSP_SOURCE_REMOVE_ERROR',
        description: `Error removing source from CSP: ${error}`,
        timestamp: new Date(),
        severity: 'MEDIUM'
      });
    }
  }

  /**
   * Get current CSP policy
   */
  getCurrentPolicy(): CSPPolicy {
    return { ...this.defaultPolicy };
  }

  /**
   * Validate if a source is allowed for a directive
   */
  isSourceAllowed(directive: string, source: string): boolean {
    const directiveObj = this.defaultPolicy.directives.find(d => d.directive === directive);
    if (!directiveObj) {
      return false;
    }

    return directiveObj.sources.includes(source) || 
           directiveObj.sources.includes("'self'") ||
           directiveObj.sources.includes('*');
  }
}
