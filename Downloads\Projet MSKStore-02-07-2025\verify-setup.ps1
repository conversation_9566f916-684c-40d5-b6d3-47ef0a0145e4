# MSKStore Project Verification Script
# Run this script after installing .NET 7 SDK

Write-Host "🔍 MSKStore Project Setup Verification" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Check .NET SDK
Write-Host "`n1. Checking .NET SDK..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET SDK not found. Please install .NET 7 SDK" -ForegroundColor Red
    exit 1
}

# Check if we're in the right directory
Write-Host "`n2. Checking project structure..." -ForegroundColor Yellow
$currentPath = Get-Location
Write-Host "Current directory: $currentPath"

if (Test-Path "MskStoreAPI\MskStoreAPI\MskStoreAPI.csproj") {
    Write-Host "✅ Backend project found" -ForegroundColor Green
} else {
    Write-Host "❌ Backend project not found. Make sure you're in the project root directory" -ForegroundColor Red
}

if (Test-Path "MSKStoreAng\MSKStoreAng\package.json") {
    Write-Host "✅ Frontend project found" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend project not found" -ForegroundColor Red
}

# Check Angular process
Write-Host "`n3. Checking Angular frontend..." -ForegroundColor Yellow
$angularProcess = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object {$_.ProcessName -eq "node"}
if ($angularProcess) {
    Write-Host "✅ Angular appears to be running" -ForegroundColor Green
} else {
    Write-Host "⚠️  Angular may not be running. Start it with 'ng serve' in MSKStoreAng\MSKStoreAng" -ForegroundColor Yellow
}

Write-Host "`n🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Navigate to backend: cd MskStoreAPI\MskStoreAPI" -ForegroundColor White
Write-Host "2. Restore packages: dotnet restore" -ForegroundColor White
Write-Host "3. Build project: dotnet build" -ForegroundColor White
Write-Host "4. Setup database: dotnet ef database update" -ForegroundColor White
Write-Host "5. Run backend: dotnet run" -ForegroundColor White
Write-Host "6. Open Swagger: https://localhost:7258/swagger" -ForegroundColor White
Write-Host "7. Open Frontend: http://localhost:4200" -ForegroundColor White

Write-Host "`n📚 Documentation:" -ForegroundColor Cyan
Write-Host "- Complete guide: COMPLETE_SETUP_GUIDE.md" -ForegroundColor White
Write-Host "- Backend commands: BACKEND_SETUP_COMMANDS.md" -ForegroundColor White

Write-Host "`n✨ Ready to start your MSKStore application!" -ForegroundColor Green
