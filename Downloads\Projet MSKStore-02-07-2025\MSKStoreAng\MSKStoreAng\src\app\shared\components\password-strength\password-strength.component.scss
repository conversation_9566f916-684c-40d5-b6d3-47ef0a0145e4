.password-strength-container {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  border: 1px solid #e9ecef;

  .strength-meter {
    .strength-label {
      font-size: 0.875rem;
    }

    .progress {
      background-color: #e9ecef;
      border-radius: 3px;
      overflow: hidden;

      .progress-bar {
        transition: all 0.3s ease-in-out;
        border-radius: 3px;

        &.strength-weak {
          background-color: #dc3545 !important;
        }

        &.strength-fair {
          background-color: #fd7e14 !important;
        }

        &.strength-good {
          background-color: #ffc107 !important;
        }

        &.strength-strong {
          background-color: #20c997 !important;
        }

        &.strength-excellent {
          background-color: #28a745 !important;
        }
      }
    }
  }

  .requirements-checklist {
    .requirement-item {
      font-size: 0.8rem;
      line-height: 1.2;

      i {
        min-width: 16px;
        text-align: center;
      }

      &:last-child {
        margin-bottom: 0 !important;
      }
    }
  }

  .suggestions {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e9ecef;

    .suggestion-item {
      margin-bottom: 0.25rem;
      font-size: 0.8rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Animation classes
.transition-all {
  transition: all 0.3s ease-in-out;
}

// Responsive adjustments
@media (max-width: 576px) {
  .password-strength-container {
    padding: 0.5rem;
    font-size: 0.875rem;

    .requirements-checklist .requirement-item,
    .suggestions .suggestion-item {
      font-size: 0.75rem;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .password-strength-container {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;

    .progress {
      background-color: #4a5568;
    }

    .suggestions {
      border-top-color: #4a5568;
    }
  }
}
