<svg aria-roledescription="class" role="graphics-document document" viewBox="0 0 4727.859375 1798" style="max-width: 4727.859375px;" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a"><style>#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .error-icon{fill:#a44141;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .error-text{fill:#ddd;stroke:#ddd;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edge-thickness-normal{stroke-width:1px;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edge-thickness-thick{stroke-width:3.5px;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edge-pattern-solid{stroke-dasharray:0;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .marker.cross{stroke:lightgrey;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a p{margin:0;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a g.classGroup text{fill:#ccc;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a g.classGroup text .title{font-weight:bolder;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .nodeLabel,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edgeLabel{color:#e0dfdf;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edgeLabel .label rect{fill:#1f2020;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .label text{fill:#e0dfdf;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .labelBkg{background:#1f2020;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edgeLabel .label span{background:#1f2020;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .classTitle{font-weight:bolder;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .node rect,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .node circle,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .node ellipse,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .node polygon,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .divider{stroke:#ccc;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a g.clickable{cursor:pointer;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a g.classGroup rect{fill:#1f2020;stroke:#ccc;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a g.classGroup line{stroke:#ccc;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .classLabel .box{stroke:none;stroke-width:0;fill:#1f2020;opacity:0.5;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .classLabel .label{fill:#ccc;font-size:10px;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .relation{stroke:lightgrey;stroke-width:1;fill:none;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .dashed-line{stroke-dasharray:3;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .dotted-line{stroke-dasharray:1 2;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #compositionStart,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #compositionEnd,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #dependencyStart,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #dependencyStart,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #extensionStart,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #extensionEnd,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #aggregationStart,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #aggregationEnd,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #lollipopStart,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a #lollipopEnd,#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .edgeTerminals{font-size:11px;line-height:initial;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a .classTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-start="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Admin_1" d="M1419.756,529.981L1419.609,533.15C1419.461,536.32,1419.167,542.66,1419.019,571.997C1418.872,601.333,1418.872,653.667,1418.872,679.833L1418.872,706"></path><path marker-start="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Employe_2" d="M1285.364,315.083L1181.362,354.069C1077.359,393.055,869.355,471.028,765.352,524.18C661.35,577.333,661.35,605.667,661.35,619.833L661.35,634"></path><path marker-start="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_IEncryptionService_EncryptionService_3" d="M942.874,385.626L962.409,412.855C981.943,440.084,1021.012,494.542,1038.831,549.938C1056.649,605.333,1053.217,661.667,1051.502,689.833L1049.786,718"></path><path marker-start="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_IPasswordValidationService_PasswordValidationService_4" d="M2370.644,377L2370.644,405.667C2370.644,434.333,2370.644,491.667,2370.208,550.5C2369.773,609.333,2368.902,669.667,2368.466,699.833L2368.031,730"></path><path marker-start="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_ISecurityService_SecurityService_5" d="M1836.509,376.975L1834.994,405.646C1833.48,434.317,1830.451,491.658,1828.936,548.496C1827.422,605.333,1827.422,661.667,1827.422,689.833L1827.422,718"></path><path marker-end="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_EncryptionService_6" d="M1302.219,326.802L1230.102,363.835C1157.985,400.868,1013.752,474.934,957.979,539.276C902.205,603.617,934.892,658.234,951.235,685.543L967.578,712.852"></path><path marker-end="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_PasswordValidationService_7" d="M1562.394,315.578L1653.453,354.481C1744.513,393.385,1926.632,471.193,2043.928,539.517C2161.224,607.841,2213.696,666.681,2239.932,696.102L2266.168,725.522"></path><path marker-end="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Admin_SecurityMonitoringService_8" d="M1418.872,970L1418.872,996.167C1418.872,1022.333,1418.872,1074.667,1271.338,1125.572C1123.804,1176.477,828.736,1225.954,681.201,1250.693L533.667,1275.432"></path><path marker-end="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_SecurityService_SecurityEvent_9" d="M1827.422,958L1827.422,986.167C1827.422,1014.333,1827.422,1070.667,1823.714,1108.072C1820.007,1145.477,1812.592,1163.954,1808.885,1173.193L1805.177,1182.432"></path><path marker-end="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_SecurityMonitoringService_LoginAttempt_10" d="M267.875,1440L267.875,1452.167C267.875,1464.333,267.875,1488.667,267.875,1506C267.875,1523.333,267.875,1533.667,267.875,1538.833L267.875,1544"></path><path marker-end="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PasswordValidationService_PasswordPolicy_11" d="M2366.472,946L2366.472,976.167C2366.472,1006.333,2366.472,1066.667,2366.472,1102C2366.472,1137.333,2366.472,1147.667,2366.472,1152.833L2366.472,1158"></path><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Produit_Categorie_12" d="M4577.734,1090L4577.734,1096.167C4577.734,1102.333,4577.734,1114.667,4577.734,1127C4577.734,1139.333,4577.734,1151.667,4577.734,1157.833L4577.734,1164"></path><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Facture_Client_13" d="M4253.95,317.153L4148.479,355.794C4043.008,394.436,3832.067,471.718,3726.596,526.526C3621.125,581.333,3621.125,613.667,3621.125,629.833L3621.125,646"></path><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Facture_Produit_14" d="M4521.419,452L4530.805,468.167C4540.191,484.333,4558.963,516.667,4568.349,539C4577.734,561.333,4577.734,573.667,4577.734,579.833L4577.734,586"></path><path marker-end="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_SecurityMonitoringController_SecurityMonitoringService_15" d="M267.875,961L267.875,988.667C267.875,1016.333,267.875,1071.667,267.875,1110.5C267.875,1149.333,267.875,1171.667,267.875,1182.833L267.875,1194"></path><path marker-end="url(#mermaid-67d3da3f-740d-48e9-b1b0-125a522b6b7a_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_SecurityMonitoringController_SecurityEvent_16" d="M467.078,880.611L659.052,921.676C851.027,962.741,1234.975,1044.87,1433.291,1095.275C1631.606,1145.679,1644.289,1164.357,1650.631,1173.697L1656.972,1183.036"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(869.5187563896179, 549)" class="edgeLabel"><g transform="translate(-15.21250057220459, -12)" class="label"><foreignObject height="24" width="30.42500114440918"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(2108.751567363739, 549)" class="edgeLabel"><g transform="translate(-15.21250057220459, -12)" class="label"><foreignObject height="24" width="30.42500114440918"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(1418.8718719482422, 1127)" class="edgeLabel"><g transform="translate(-31.399999618530273, -12)" class="label"><foreignObject height="24" width="62.79999923706055"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>monitors</p></span></div></foreignObject></g></g><g transform="translate(1827.4218859672546, 1127)" class="edgeLabel"><g transform="translate(-26.412500381469727, -12)" class="label"><foreignObject height="24" width="52.82500076293945"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates</p></span></div></foreignObject></g></g><g transform="translate(267.875, 1513)" class="edgeLabel"><g transform="translate(-13.90625, -12)" class="label"><foreignObject height="24" width="27.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>logs</p></span></div></foreignObject></g></g><g transform="translate(2366.4718890190125, 1127)" class="edgeLabel"><g transform="translate(-15.21250057220459, -12)" class="label"><foreignObject height="24" width="30.42500114440918"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(4577.734408855438, 1127)" class="edgeLabel"><g transform="translate(-36.97500228881836, -12)" class="label"><foreignObject height="24" width="73.95000457763672"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>belongs to</p></span></div></foreignObject></g></g><g transform="translate(3621.125018596649, 549)" class="edgeLabel"><g transform="translate(-31.825000762939453, -12)" class="label"><foreignObject height="24" width="63.650001525878906"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>issued to</p></span></div></foreignObject></g></g><g transform="translate(4577.734408855438, 549)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(267.875, 1127)" class="edgeLabel"><g transform="translate(-15.21250057220459, -12)" class="label"><foreignObject height="24" width="30.42500114440918"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(1618.9234371185303, 1127)" class="edgeLabel"><g transform="translate(-31.037500381469727, -12)" class="label"><foreignObject height="24" width="62.07500076293945"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>manages</p></span></div></foreignObject></g></g><g transform="translate(4562.73440942772, 1107.5000004905264)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g></g><g transform="translate(4232.358002575005, 309.0889798463183)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(4517.233478535553, 474.66560085267724)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(4587.73440942772, 1141.5000004905264)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(3631.1250192983243, 623.500000601436)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(4587.73440942772, 563.5000004905265)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"></g><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(1432.306254863739, 260)" id="classId-User-21" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-130.0875015258789 -252 L130.0875015258789 -252 L130.0875015258789 252 L-130.0875015258789 252"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-130.0875015258789 -252 C-61.49360447920077 -252, 7.100292567477368 -252, 130.0875015258789 -252 M-130.0875015258789 -252 C-49.65020541383764 -252, 30.787090698203627 -252, 130.0875015258789 -252 M130.0875015258789 -252 C130.0875015258789 -144.21445910091518, 130.0875015258789 -36.428918201830385, 130.0875015258789 252 M130.0875015258789 -252 C130.0875015258789 -138.50520889380766, 130.0875015258789 -25.010417787615324, 130.0875015258789 252 M130.0875015258789 252 C66.8560439872586 252, 3.6245864486383113 252, -130.0875015258789 252 M130.0875015258789 252 C52.28861653759225 252, -25.51026845069441 252, -130.0875015258789 252 M-130.0875015258789 252 C-130.0875015258789 103.48559095575868, -130.0875015258789 -45.02881808848264, -130.0875015258789 -252 M-130.0875015258789 252 C-130.0875015258789 146.21981893127463, -130.0875015258789 40.43963786254923, -130.0875015258789 -252"></path></g><g transform="translate(0, -228)" class="annotation-group text"></g><g transform="translate(-16.887500762939453, -228)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="33.775001525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 83px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>User</p></span></div></foreignObject></g></g><g transform="translate(-118.0875015258789, -180)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="43.82500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="81.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string nom</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="105.55000305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 163px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string prenom</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="179.7375030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 231px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string email [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="219.28750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 272px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string mot_de_passe [hashed]</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="79.32500457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string role</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="212.27500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 264px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string telephone [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="193.9875030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 246px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string adresse [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="179.47500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 230px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime date_creation</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="210.35000610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 262px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime date_modification</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="104.73750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 158px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-bool is_active</p></span></div></foreignObject></g><g transform="translate(0,252)" style="" class="label"><foreignObject height="24" width="148.16250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 202px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime last_login</p></span></div></foreignObject></g></g><g transform="translate(-118.0875015258789, 132)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="122.36250305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 177px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getUser() : User</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="143.6999969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 195px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+createUser() : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="147.40000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 202px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updateUser() : void</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="143.5124969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 196px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleteUser() : void</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="189.53750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 243px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validatePassword() : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-130.08750915527344 -204 C-43.48255945072151 -204, 43.12239025383042 -204, 130.08750915527344 -204 M-130.08750915527344 -204 C-67.27126070789194 -204, -4.455012260510458 -204, 130.08750915527344 -204"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-130.08750915527344 108 C-58.08633319762809 108, 13.914842760017251 108, 130.08750915527344 108 M-130.08750915527344 108 C-63.232281760459315 108, 3.6229456343548065 108, 130.08750915527344 108"></path></g></g><g transform="translate(1418.8718719482422, 838)" id="classId-Admin-22" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-139.5437469482422 -132 L139.5437469482422 -132 L139.5437469482422 132 L-139.5437469482422 132"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-139.5437469482422 -132 C-82.53039297504853 -132, -25.517039001854855 -132, 139.5437469482422 -132 M-139.5437469482422 -132 C-79.59054366375406 -132, -19.637340379265936 -132, 139.5437469482422 -132 M139.5437469482422 -132 C139.5437469482422 -53.830654038058086, 139.5437469482422 24.33869192388383, 139.5437469482422 132 M139.5437469482422 -132 C139.5437469482422 -42.335370664583294, 139.5437469482422 47.32925867083341, 139.5437469482422 132 M139.5437469482422 132 C40.69514040535992 132, -58.153466137522344 132, -139.5437469482422 132 M139.5437469482422 132 C74.29328772346827 132, 9.042828498694348 132, -139.5437469482422 132 M-139.5437469482422 132 C-139.5437469482422 68.86309807995157, -139.5437469482422 5.7261961599031395, -139.5437469482422 -132 M-139.5437469482422 132 C-139.5437469482422 54.51016084674194, -139.5437469482422 -22.97967830651612, -139.5437469482422 -132"></path></g><g transform="translate(0, -108)" class="annotation-group text"></g><g transform="translate(-23.700000762939453, -108)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="47.400001525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 97px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Admin</p></span></div></foreignObject></g></g><g transform="translate(-127.54374694824219, -60)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="134.10000610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string permissions</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="228.40000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 315px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-List&lt;string&gt; security_clearance</p></span></div></foreignObject></g></g><g transform="translate(-127.54374694824219, 12)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="185.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 235px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererUtilisateurs() : void</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="177.65000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+genererRapport() : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="217.1374969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 267px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+modifierTimbreFiscal() : void</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="221.33750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 274px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+consulterSecurityLogs() : void</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="231.3874969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 282px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererPolitiquesSecurity() : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-139.5437469482422 -84 C-60.237212332855336 -84, 19.069322282531516 -84, 139.5437469482422 -84 M-139.5437469482422 -84 C-68.85246529979437 -84, 1.8388163486534381 -84, 139.5437469482422 -84"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-139.5437469482422 -12 C-41.809562066636104 -12, 55.92462281496998 -12, 139.5437469482422 -12 M-139.5437469482422 -12 C-43.04885170669303 -12, 53.44604353485613 -12, 139.5437469482422 -12"></path></g></g><g transform="translate(661.3500061035156, 838)" id="classId-Employe-23" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-144.27188110351562 -204 L144.27188110351562 -204 L144.27188110351562 204 L-144.27188110351562 204"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-144.27188110351562 -204 C-39.66128328410551 -204, 64.94931453530461 -204, 144.27188110351562 -204 M-144.27188110351562 -204 C-60.50923226713165 -204, 23.253416569252323 -204, 144.27188110351562 -204 M144.27188110351562 -204 C144.27188110351562 -62.08874412512853, 144.27188110351562 79.82251174974294, 144.27188110351562 204 M144.27188110351562 -204 C144.27188110351562 -44.27303842484551, 144.27188110351562 115.45392315030898, 144.27188110351562 204 M144.27188110351562 204 C52.792874876399026 204, -38.686131350717574 204, -144.27188110351562 204 M144.27188110351562 204 C46.9729009747207 204, -50.32607915407422 204, -144.27188110351562 204 M-144.27188110351562 204 C-144.27188110351562 112.03368596682583, -144.27188110351562 20.067371933651657, -144.27188110351562 -204 M-144.27188110351562 204 C-144.27188110351562 118.17051417367445, -144.27188110351562 32.341028347348896, -144.27188110351562 -204"></path></g><g transform="translate(0, -180)" class="annotation-group text"></g><g transform="translate(-31.84375, -180)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="63.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 111px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Employe</p></span></div></foreignObject></g></g><g transform="translate(-132.27188110351562, -132)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="203.71250915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 252px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-decimal salaire [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="135.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string department</p></span></div></foreignObject></g></g><g transform="translate(-132.27188110351562, -60)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="147.6999969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 201px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererCaisse() : void</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="164.71250915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererFactures() : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="154.3000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 207px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererCredits() : void</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="152.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 205px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererClients() : void</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="223.08750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 273px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererFermetureCaisse() : void</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="225.9875030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 278px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererTypesTicketResto() : void</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="161.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererProduits() : void</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="179.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 233px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererCategories() : void</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="152.33750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 208px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererDepots() : void</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="191.85000610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 245px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererFournisseurs() : void</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="232.6999969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 286px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+gererAchatsFournisseur() : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-144.27188110351562 -156 C-30.592939818319323 -156, 83.08600146687698 -156, 144.27188110351562 -156 M-144.27188110351562 -156 C-36.14518107717355 -156, 71.98151894916853 -156, 144.27188110351562 -156"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-144.27188110351562 -84 C-65.65098331837328 -84, 12.969914466769069 -84, 144.27188110351562 -84 M-144.27188110351562 -84 C-81.47564764302868 -84, -18.67941418254175 -84, 144.27188110351562 -84"></path></g></g><g transform="translate(852.7484250068665, 260)" id="classId-IEncryptionService-24" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-187.9656219482422 -111 L187.9656219482422 -111 L187.9656219482422 111 L-187.9656219482422 111"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-187.9656219482422 -111 C-109.27035518035642 -111, -30.57508841247065 -111, 187.9656219482422 -111 M-187.9656219482422 -111 C-88.51776972471833 -111, 10.930082498805518 -111, 187.9656219482422 -111 M187.9656219482422 -111 C187.9656219482422 -36.406132161302935, 187.9656219482422 38.18773567739413, 187.9656219482422 111 M187.9656219482422 -111 C187.9656219482422 -57.51508949792713, 187.9656219482422 -4.030178995854257, 187.9656219482422 111 M187.9656219482422 111 C56.89284873573689 111, -74.1799244767684 111, -187.9656219482422 111 M187.9656219482422 111 C102.3071256566253 111, 16.648629365008418 111, -187.9656219482422 111 M-187.9656219482422 111 C-187.9656219482422 65.05729425021153, -187.9656219482422 19.11458850042304, -187.9656219482422 -111 M-187.9656219482422 111 C-187.9656219482422 58.88167652583608, -187.9656219482422 6.763353051672155, -187.9656219482422 -111"></path></g><g transform="translate(-41.17499923706055, -87)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="82.3499984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-70.40625, -63)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="140.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>IEncryptionService</p></span></div></foreignObject></g></g><g transform="translate(-175.9656219482422, -15)" class="members-group text"></g><g transform="translate(-175.9656219482422, 15)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="208.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 258px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+encrypt(string data) : string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="281.5249938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 331px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+decrypt(string encryptedData) : string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="165.28750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 218px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+generateKey() : string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="278.13751220703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 327px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validateEncryption(string data) : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-187.9656219482422 -39 C-57.44538695608267 -39, 73.07484803607684 -39, 187.9656219482422 -39 M-187.9656219482422 -39 C-84.88409789674805 -39, 18.197426154746097 -39, 187.9656219482422 -39"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-187.9656219482422 -15 C-42.73519839349743 -15, 102.49522516124733 -15, 187.9656219482422 -15 M-187.9656219482422 -15 C-78.79210846961281 -15, 30.38140500901656 -15, 187.9656219482422 -15"></path></g></g><g transform="translate(1042.4750061035156, 838)" id="classId-EncryptionService-25" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-186.85311889648438 -120 L186.85311889648438 -120 L186.85311889648438 120 L-186.85311889648438 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-186.85311889648438 -120 C-99.81013061694749 -120, -12.767142337410604 -120, 186.85311889648438 -120 M-186.85311889648438 -120 C-74.71308524950224 -120, 37.42694839747989 -120, 186.85311889648438 -120 M186.85311889648438 -120 C186.85311889648438 -66.87346956806203, 186.85311889648438 -13.746939136124055, 186.85311889648438 120 M186.85311889648438 -120 C186.85311889648438 -55.799413578465135, 186.85311889648438 8.40117284306973, 186.85311889648438 120 M186.85311889648438 120 C91.7198292385778 120, -3.4134604193287714 120, -186.85311889648438 120 M186.85311889648438 120 C44.50809624050467 120, -97.83692641547503 120, -186.85311889648438 120 M-186.85311889648438 120 C-186.85311889648438 64.46174149986442, -186.85311889648438 8.923482999728847, -186.85311889648438 -120 M-186.85311889648438 120 C-186.85311889648438 53.336158387690745, -186.85311889648438 -13.32768322461851, -186.85311889648438 -120"></path></g><g transform="translate(0, -96)" class="annotation-group text"></g><g transform="translate(-68.1812515258789, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="136.3625030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>EncryptionService</p></span></div></foreignObject></g></g><g transform="translate(-174.85311889648438, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="153.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 208px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string encryptionKey</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="217.40000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 272px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-AesCryptoServiceProvider aes</p></span></div></foreignObject></g></g><g transform="translate(-174.85311889648438, 24)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="208.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 258px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+encrypt(string data) : string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="281.5249938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 331px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+decrypt(string encryptedData) : string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="165.28750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 218px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+generateKey() : string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="278.13751220703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 327px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validateEncryption(string data) : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-186.85311889648438 -72 C-84.8241388235859 -72, 17.20484124931258 -72, 186.85311889648438 -72 M-186.85311889648438 -72 C-63.80482936882291 -72, 59.24346015883856 -72, 186.85311889648438 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-186.85311889648438 0 C-109.83547448668907 0, -32.817830076893756 0, 186.85311889648438 0 M-186.85311889648438 0 C-55.82346562729637 0, 75.20618764189163 0, 186.85311889648438 0"></path></g></g><g transform="translate(2370.643756389618, 260)" id="classId-IPasswordValidationService-26" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-255.09375 -99 L255.09375 -99 L255.09375 99 L-255.09375 99"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-255.09375 -99 C-130.4754490921476 -99, -5.857148184295227 -99, 255.09375 -99 M-255.09375 -99 C-63.91886072204946 -99, 127.25602855590108 -99, 255.09375 -99 M255.09375 -99 C255.09375 -34.9947569119786, 255.09375 29.010486176042804, 255.09375 99 M255.09375 -99 C255.09375 -57.83912674813995, 255.09375 -16.678253496279893, 255.09375 99 M255.09375 99 C60.574556716069054 99, -133.9446365678619 99, -255.09375 99 M255.09375 99 C55.88691602806867 99, -143.31991794386266 99, -255.09375 99 M-255.09375 99 C-255.09375 46.8100686959298, -255.09375 -5.3798626081403995, -255.09375 -99 M-255.09375 99 C-255.09375 52.95872589876052, -255.09375 6.917451797521039, -255.09375 -99"></path></g><g transform="translate(-41.17499923706055, -75)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="82.3499984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-101.3125, -51)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="202.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 240px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>IPasswordValidationService</p></span></div></foreignObject></g></g><g transform="translate(-243.09375, -3)" class="members-group text"></g><g transform="translate(-243.09375, 27)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="384.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 435px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validatePassword(string password) : ValidationResult</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="325.95001220703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 380px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+checkPasswordPolicy(string password) : bool</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="253.15000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 306px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+generateSecurePassword() : string</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-255.09375 -27 C-112.83840034368384 -27, 29.416949312632312 -27, 255.09375 -27 M-255.09375 -27 C-152.6981875167049 -27, -50.30262503340981 -27, 255.09375 -27"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-255.09375 -3 C-95.40636813394696 -3, 64.28101373210609 -3, 255.09375 -3 M-255.09375 -3 C-106.44335963330641 -3, 42.20703073338717 -3, 255.09375 -3"></path></g></g><g transform="translate(2366.4718890190125, 838)" id="classId-PasswordValidationService-27" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-268.2124938964844 -108 L268.2124938964844 -108 L268.2124938964844 108 L-268.2124938964844 108"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-268.2124938964844 -108 C-134.58771362531127 -108, -0.9629333541381584 -108, 268.2124938964844 -108 M-268.2124938964844 -108 C-139.30909252920335 -108, -10.405691161922334 -108, 268.2124938964844 -108 M268.2124938964844 -108 C268.2124938964844 -64.51954190475325, 268.2124938964844 -21.039083809506494, 268.2124938964844 108 M268.2124938964844 -108 C268.2124938964844 -24.618337987694886, 268.2124938964844 58.76332402461023, 268.2124938964844 108 M268.2124938964844 108 C84.60270205513427 108, -99.00708978621583 108, -268.2124938964844 108 M268.2124938964844 108 C100.23750289553803 108, -67.73748810540832 108, -268.2124938964844 108 M-268.2124938964844 108 C-268.2124938964844 30.209852065684956, -268.2124938964844 -47.58029586863009, -268.2124938964844 -108 M-268.2124938964844 108 C-268.2124938964844 31.047052224398186, -268.2124938964844 -45.90589555120363, -268.2124938964844 -108"></path></g><g transform="translate(0, -84)" class="annotation-group text"></g><g transform="translate(-99.0875015258789, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="198.1750030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 236px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PasswordValidationService</p></span></div></foreignObject></g></g><g transform="translate(-256.2124938964844, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="160.3000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-PasswordPolicy policy</p></span></div></foreignObject></g></g><g transform="translate(-256.2124938964844, 12)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="384.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 435px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validatePassword(string password) : ValidationResult</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="325.95001220703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 380px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+checkPasswordPolicy(string password) : bool</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="253.15000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 306px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+generateSecurePassword() : string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="413.3374938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 464px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+checkPasswordHistory(int userId, string password) : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-268.2124938964844 -60 C-93.28092683311883 -60, 81.65064023024672 -60, 268.2124938964844 -60 M-268.2124938964844 -60 C-112.8794316605817 -60, 42.45363057532097 -60, 268.2124938964844 -60"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-268.2124938964844 -12 C-87.32464670631296 -12, 93.56320048385845 -12, 268.2124938964844 -12 M-268.2124938964844 -12 C-113.5893793976503 -12, 41.033735101183765 -12, 268.2124938964844 -12"></path></g></g><g transform="translate(1842.6875109672546, 260)" id="classId-ISecurityService-28" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-206.68751525878906 -99 L206.68751525878906 -99 L206.68751525878906 99 L-206.68751525878906 99"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-206.68751525878906 -99 C-116.49539519871773 -99, -26.3032751386464 -99, 206.68751525878906 -99 M-206.68751525878906 -99 C-106.92849241798893 -99, -7.169469577188806 -99, 206.68751525878906 -99 M206.68751525878906 -99 C206.68751525878906 -20.741578421634657, 206.68751525878906 57.51684315673069, 206.68751525878906 99 M206.68751525878906 -99 C206.68751525878906 -43.694748869877536, 206.68751525878906 11.610502260244928, 206.68751525878906 99 M206.68751525878906 99 C118.9850108740465 99, 31.282506489303927 99, -206.68751525878906 99 M206.68751525878906 99 C102.98731160021347 99, -0.7128920583621152 99, -206.68751525878906 99 M-206.68751525878906 99 C-206.68751525878906 53.33917658013637, -206.68751525878906 7.678353160272735, -206.68751525878906 -99 M-206.68751525878906 99 C-206.68751525878906 54.604513925904804, -206.68751525878906 10.209027851809608, -206.68751525878906 -99"></path></g><g transform="translate(-41.17499923706055, -75)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="82.3499984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-60.63750076293945, -51)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="121.2750015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ISecurityService</p></span></div></foreignObject></g></g><g transform="translate(-194.68751525878906, -3)" class="members-group text"></g><g transform="translate(-194.68751525878906, 27)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="274.0375061035156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 323px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+generateJwtToken(User user) : string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="277.3374938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 326px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validateJwtToken(string token) : bool</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="328.7375183105469"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 375px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+logSecurityEvent(SecurityEvent event) : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-206.68751525878906 -27 C-67.37149824335549 -27, 71.94451877207808 -27, 206.68751525878906 -27 M-206.68751525878906 -27 C-78.63940963688955 -27, 49.408695985009956 -27, 206.68751525878906 -27"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-206.68751525878906 -3 C-107.93676477965815 -3, -9.186014300527233 -3, 206.68751525878906 -3 M-206.68751525878906 -3 C-111.67165046889635 -3, -16.65578567900363 -3, 206.68751525878906 -3"></path></g></g><g transform="translate(1827.4218859672546, 838)" id="classId-SecurityService-29" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-205.57188415527344 -120 L205.57188415527344 -120 L205.57188415527344 120 L-205.57188415527344 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-205.57188415527344 -120 C-118.9927598301999 -120, -32.413635505126365 -120, 205.57188415527344 -120 M-205.57188415527344 -120 C-104.42156820102062 -120, -3.2712522467678014 -120, 205.57188415527344 -120 M205.57188415527344 -120 C205.57188415527344 -66.68009607187865, 205.57188415527344 -13.360192143757303, 205.57188415527344 120 M205.57188415527344 -120 C205.57188415527344 -27.49420219645522, 205.57188415527344 65.01159560708956, 205.57188415527344 120 M205.57188415527344 120 C64.42746790148658 120, -76.71694835230028 120, -205.57188415527344 120 M205.57188415527344 120 C50.52336320892911 120, -104.52515773741521 120, -205.57188415527344 120 M-205.57188415527344 120 C-205.57188415527344 51.19961812549917, -205.57188415527344 -17.600763749001658, -205.57188415527344 -120 M-205.57188415527344 120 C-205.57188415527344 33.58030847425434, -205.57188415527344 -52.839383051491325, -205.57188415527344 -120"></path></g><g transform="translate(0, -96)" class="annotation-group text"></g><g transform="translate(-58.40625, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="116.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 156px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SecurityService</p></span></div></foreignObject></g></g><g transform="translate(-193.57188415527344, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="120.82500457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 172px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string jwtSecret</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="164.3000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 220px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-TimeSpan tokenExpiry</p></span></div></foreignObject></g></g><g transform="translate(-193.57188415527344, 24)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="274.0375061035156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 323px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+generateJwtToken(User user) : string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="277.3374938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 326px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validateJwtToken(string token) : bool</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="328.7375183105469"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 375px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+logSecurityEvent(SecurityEvent event) : void</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="254.15000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 305px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+refreshToken(string token) : string</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-205.57188415527344 -72 C-107.4090780080202 -72, -9.246271860766967 -72, 205.57188415527344 -72 M-205.57188415527344 -72 C-51.63533527331455 -72, 102.30121360864433 -72, 205.57188415527344 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-205.57188415527344 0 C-107.33297268561374 0, -9.094061215954042 0, 205.57188415527344 0 M-205.57188415527344 0 C-93.81402553818545 0, 17.943833078902543 0, 205.57188415527344 0"></path></g></g><g transform="translate(267.875, 1320)" id="classId-SecurityMonitoringService-30" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-259.875 -120 L259.875 -120 L259.875 120 L-259.875 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-259.875 -120 C-137.90430997590767 -120, -15.93361995181533 -120, 259.875 -120 M-259.875 -120 C-80.65775891348594 -120, 98.55948217302813 -120, 259.875 -120 M259.875 -120 C259.875 -65.62020363668809, 259.875 -11.240407273376178, 259.875 120 M259.875 -120 C259.875 -70.26763692323271, 259.875 -20.53527384646543, 259.875 120 M259.875 120 C145.89865353951103 120, 31.92230707902209 120, -259.875 120 M259.875 120 C95.17489555915449 120, -69.52520888169101 120, -259.875 120 M-259.875 120 C-259.875 34.87790065873736, -259.875 -50.24419868252528, -259.875 -120 M-259.875 120 C-259.875 66.15402906584336, -259.875 12.308058131686721, -259.875 -120"></path></g><g transform="translate(0, -96)" class="annotation-group text"></g><g transform="translate(-98.25, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="196.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 235px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SecurityMonitoringService</p></span></div></foreignObject></g></g><g transform="translate(-247.875, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.1624984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 168px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-ILogger logger</p></span></div></foreignObject></g></g><g transform="translate(-247.875, 0)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="344.8999938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 398px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+logLoginAttempt(LoginAttempt attempt) : void</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="397.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 445px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+logSecurityViolation(SecurityViolation violation) : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="310.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 359px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+generateSecurityReport() : SecurityReport</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="317"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 406px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+detectAnomalies() : List&lt;SecurityAnomaly&gt;</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="248.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 303px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+blockSuspiciousIP(string ip) : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-259.875 -72 C-121.05635449631086 -72, 17.76229100737828 -72, 259.875 -72 M-259.875 -72 C-106.32736866762593 -72, 47.220262664748134 -72, 259.875 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-259.875 -24 C-149.1712328324211 -24, -38.46746566484222 -24, 259.875 -24 M-259.875 -24 C-99.57459047748847 -24, 60.72581904502306 -24, 259.875 -24"></path></g></g><g transform="translate(2934.343768596649, 260)" id="classId-InputValidationMiddleware-31" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-258.6062469482422 -108 L258.6062469482422 -108 L258.6062469482422 108 L-258.6062469482422 108"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-258.6062469482422 -108 C-106.95954194824614 -108, 44.687163051749906 -108, 258.6062469482422 -108 M-258.6062469482422 -108 C-77.33281422307115 -108, 103.94061850209988 -108, 258.6062469482422 -108 M258.6062469482422 -108 C258.6062469482422 -31.978250803359856, 258.6062469482422 44.04349839328029, 258.6062469482422 108 M258.6062469482422 -108 C258.6062469482422 -37.14388539636926, 258.6062469482422 33.71222920726149, 258.6062469482422 108 M258.6062469482422 108 C148.03551921916534 108, 37.46479149008846 108, -258.6062469482422 108 M258.6062469482422 108 C124.41207328837442 108, -9.782100371493357 108, -258.6062469482422 108 M-258.6062469482422 108 C-258.6062469482422 63.39449401813105, -258.6062469482422 18.788988036262097, -258.6062469482422 -108 M-258.6062469482422 108 C-258.6062469482422 23.575422470786847, -258.6062469482422 -60.849155058426305, -258.6062469482422 -108"></path></g><g transform="translate(0, -84)" class="annotation-group text"></g><g transform="translate(-99.75, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="199.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 240px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>InputValidationMiddleware</p></span></div></foreignObject></g></g><g transform="translate(-246.6062469482422, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="190.27500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 282px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-List&lt;ValidationRule&gt; rules</p></span></div></foreignObject></g></g><g transform="translate(-246.6062469482422, 12)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="393.4624938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 440px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validateInput(HttpRequest request) : ValidationResult</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="251.15000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+sanitizeInput(string input) : string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="220.2375030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 274px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+detectXSS(string input) : bool</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="286.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 336px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+detectSQLInjection(string input) : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-258.60626220703125 -60 C-133.60942377511213 -60, -8.61258534319299 -60, 258.60626220703125 -60 M-258.60626220703125 -60 C-92.62144460056052 -60, 73.3633730059102 -60, 258.60626220703125 -60"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-258.60626220703125 -12 C-104.48143773319299 -12, 49.64338674064527 -12, 258.60626220703125 -12 M-258.60626220703125 -12 C-141.40784231920372 -12, -24.209422431376225 -12, 258.60626220703125 -12"></path></g></g><g transform="translate(3447.834399700165, 260)" id="classId-RateLimitingMiddleware-32" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-204.88436889648438 -96 L204.88436889648438 -96 L204.88436889648438 96 L-204.88436889648438 96"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-204.88436889648438 -96 C-90.70718490160542 -96, 23.469999093273543 -96, 204.88436889648438 -96 M-204.88436889648438 -96 C-85.65410749941924 -96, 33.576153897645895 -96, 204.88436889648438 -96 M204.88436889648438 -96 C204.88436889648438 -44.298316654998324, 204.88436889648438 7.403366690003352, 204.88436889648438 96 M204.88436889648438 -96 C204.88436889648438 -44.88964815089081, 204.88436889648438 6.2207036982183865, 204.88436889648438 96 M204.88436889648438 96 C112.54869854195539 96, 20.213028187426403 96, -204.88436889648438 96 M204.88436889648438 96 C59.46467011486348 96, -85.95502866675741 96, -204.88436889648438 96 M-204.88436889648438 96 C-204.88436889648438 50.309201334905204, -204.88436889648438 4.618402669810408, -204.88436889648438 -96 M-204.88436889648438 96 C-204.88436889648438 47.021670775178876, -204.88436889648438 -1.9566584496422479, -204.88436889648438 -96"></path></g><g transform="translate(0, -72)" class="annotation-group text"></g><g transform="translate(-90.4312515258789, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="180.8625030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 221px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>RateLimitingMiddleware</p></span></div></foreignObject></g></g><g transform="translate(-192.88436889648438, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="294.26251220703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 379px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Dictionary&lt;string, RateLimit&gt; rateLimits</p></span></div></foreignObject></g></g><g transform="translate(-192.88436889648438, 24)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="277.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 324px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+checkRateLimit(string clientId) : bool</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="295.3374938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 344px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+incrementCounter(string clientId) : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="258.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 307px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+resetCounter(string clientId) : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-204.88436889648438 -48 C-98.23773028370827 -48, 8.408908329067827 -48, 204.88436889648438 -48 M-204.88436889648438 -48 C-45.93546003622913 -48, 113.01344882402611 -48, 204.88436889648438 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-204.88436889648438 0 C-102.41876442377503 0, 0.04684004893431393 0, 204.88436889648438 0 M-204.88436889648438 0 C-117.98429898582147 0, -31.08422907515856 0, 204.88436889648438 0"></path></g></g><g transform="translate(3953.334399700165, 260)" id="classId-SecurityHeadersMiddleware-33" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-250.61563110351562 -87 L250.61563110351562 -87 L250.61563110351562 87 L-250.61563110351562 87"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-250.61563110351562 -87 C-82.2715226214984 -87, 86.07258586051881 -87, 250.61563110351562 -87 M-250.61563110351562 -87 C-92.85456392882841 -87, 64.9065032458588 -87, 250.61563110351562 -87 M250.61563110351562 -87 C250.61563110351562 -39.62246176099683, 250.61563110351562 7.755076478006345, 250.61563110351562 87 M250.61563110351562 -87 C250.61563110351562 -19.299177032548627, 250.61563110351562 48.401645934902746, 250.61563110351562 87 M250.61563110351562 87 C138.45212875132893 87, 26.28862639914226 87, -250.61563110351562 87 M250.61563110351562 87 C80.91797870189006 87, -88.7796736997355 87, -250.61563110351562 87 M-250.61563110351562 87 C-250.61563110351562 23.717681551074172, -250.61563110351562 -39.564636897851656, -250.61563110351562 -87 M-250.61563110351562 87 C-250.61563110351562 51.559327304931465, -250.61563110351562 16.11865460986293, -250.61563110351562 -87"></path></g><g transform="translate(0, -63)" class="annotation-group text"></g><g transform="translate(-104.33125305175781, -63)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="208.66250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 247px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SecurityHeadersMiddleware</p></span></div></foreignObject></g></g><g transform="translate(-238.61563110351562, -15)" class="members-group text"></g><g transform="translate(-238.61563110351562, 15)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="372.8999938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 428px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+addSecurityHeaders(HttpResponse response) : void</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="347.4750061035156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 405px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+setCORSHeaders(HttpResponse response) : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="336.3000183105469"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 393px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+setCSPHeaders(HttpResponse response) : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-250.61563110351562 -39 C-110.24963598522723 -39, 30.116359133061167 -39, 250.61563110351562 -39 M-250.61563110351562 -39 C-123.54048867670909 -39, 3.5346537500974478 -39, 250.61563110351562 -39"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-250.61563110351562 -15 C-52.36115096904007 -15, 145.8933291654355 -15, 250.61563110351562 -15 M-250.61563110351562 -15 C-148.86808627986193 -15, -47.1205414562082 -15, 250.61563110351562 -15"></path></g></g><g transform="translate(1749.9718852043152, 1320)" id="classId-SecurityEvent-34" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-115.77188110351562 -132 L115.77188110351562 -132 L115.77188110351562 132 L-115.77188110351562 132"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.77188110351562 -132 C-55.51240486265108 -132, 4.747071378213462 -132, 115.77188110351562 -132 M-115.77188110351562 -132 C-50.3745787901033 -132, 15.022723523309025 -132, 115.77188110351562 -132 M115.77188110351562 -132 C115.77188110351562 -29.656054112337387, 115.77188110351562 72.68789177532523, 115.77188110351562 132 M115.77188110351562 -132 C115.77188110351562 -57.746268830916264, 115.77188110351562 16.50746233816747, 115.77188110351562 132 M115.77188110351562 132 C49.920470803427904 132, -15.930939496659818 132, -115.77188110351562 132 M115.77188110351562 132 C62.874758170337174 132, 9.977635237158722 132, -115.77188110351562 132 M-115.77188110351562 132 C-115.77188110351562 38.86335445236324, -115.77188110351562 -54.27329109527352, -115.77188110351562 -132 M-115.77188110351562 132 C-115.77188110351562 37.4266324978729, -115.77188110351562 -57.1467350042542, -115.77188110351562 -132"></path></g><g transform="translate(0, -108)" class="annotation-group text"></g><g transform="translate(-52.01874923706055, -108)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="104.0374984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SecurityEvent</p></span></div></foreignObject></g></g><g transform="translate(-103.77188110351562, -60)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="43.82500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="131.71250915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string event_type</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="131.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string description</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="103.11250305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 157px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string user_id</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="127.07500457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 182px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string ip_address</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="155.52500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 210px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime timestamp</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="107.86250305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 162px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string severity</p></span></div></foreignObject></g></g><g transform="translate(-103.77188110351562, 132)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.77188110351562 -84 C-34.012456965476815 -84, 47.746967172561995 -84, 115.77188110351562 -84 M-115.77188110351562 -84 C-43.57457991701992 -84, 28.62272126947579 -84, 115.77188110351562 -84"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.77188110351562 108 C-23.502966157670073 108, 68.76594878817548 108, 115.77188110351562 108 M-115.77188110351562 108 C-50.63609750503433 108, 14.499686093446968 108, 115.77188110351562 108"></path></g></g><g transform="translate(267.875, 1670)" id="classId-LoginAttempt-35" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-115.15937805175781 -120 L115.15937805175781 -120 L115.15937805175781 120 L-115.15937805175781 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.15937805175781 -120 C-30.197324599192697 -120, 54.76472885337242 -120, 115.15937805175781 -120 M-115.15937805175781 -120 C-58.049711882051604 -120, -0.9400457123453947 -120, 115.15937805175781 -120 M115.15937805175781 -120 C115.15937805175781 -64.61853730944819, 115.15937805175781 -9.237074618896386, 115.15937805175781 120 M115.15937805175781 -120 C115.15937805175781 -53.88685726187924, 115.15937805175781 12.226285476241515, 115.15937805175781 120 M115.15937805175781 120 C25.857594085149685 120, -63.44418988145844 120, -115.15937805175781 120 M115.15937805175781 120 C28.46558008989504 120, -58.22821787196773 120, -115.15937805175781 120 M-115.15937805175781 120 C-115.15937805175781 48.26129344878214, -115.15937805175781 -23.47741310243572, -115.15937805175781 -120 M-115.15937805175781 120 C-115.15937805175781 51.62552063955735, -115.15937805175781 -16.748958720885298, -115.15937805175781 -120"></path></g><g transform="translate(0, -96)" class="annotation-group text"></g><g transform="translate(-50.79375076293945, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="101.5875015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>LoginAttempt</p></span></div></foreignObject></g></g><g transform="translate(-103.15937805175781, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="43.82500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.7750015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string email</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="127.07500457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 182px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string ip_address</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="94.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-bool success</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="155.52500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 210px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime timestamp</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="153.91250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 206px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string failure_reason</p></span></div></foreignObject></g></g><g transform="translate(-103.15937805175781, 120)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.15937805175781 -72 C-38.092026788807004 -72, 38.975324474143804 -72, 115.15937805175781 -72 M-115.15937805175781 -72 C-31.451323710116768 -72, 52.25673063152428 -72, 115.15937805175781 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.15937805175781 96 C-64.28808624074242 96, -13.416794429727034 96, 115.15937805175781 96 M-115.15937805175781 96 C-31.332053073575864 96, 52.495271904606085 96, 115.15937805175781 96"></path></g></g><g transform="translate(2366.4718890190125, 1320)" id="classId-PasswordPolicy-36" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-145.8093719482422 -156 L145.8093719482422 -156 L145.8093719482422 156 L-145.8093719482422 156"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-145.8093719482422 -156 C-71.26021019352856 -156, 3.2889515611850584 -156, 145.8093719482422 -156 M-145.8093719482422 -156 C-47.42693993548771 -156, 50.95549207726677 -156, 145.8093719482422 -156 M145.8093719482422 -156 C145.8093719482422 -49.166312845816705, 145.8093719482422 57.66737430836659, 145.8093719482422 156 M145.8093719482422 -156 C145.8093719482422 -35.35228106120978, 145.8093719482422 85.29543787758044, 145.8093719482422 156 M145.8093719482422 156 C41.67519091014317 156, -62.45899012795584 156, -145.8093719482422 156 M145.8093719482422 156 C64.54373877073756 156, -16.721894406767063 156, -145.8093719482422 156 M-145.8093719482422 156 C-145.8093719482422 73.06416543783583, -145.8093719482422 -9.87166912432835, -145.8093719482422 -156 M-145.8093719482422 156 C-145.8093719482422 43.14366655268957, -145.8093719482422 -69.71266689462087, -145.8093719482422 -156"></path></g><g transform="translate(0, -132)" class="annotation-group text"></g><g transform="translate(-56.29375076293945, -132)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="112.5875015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 157px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PasswordPolicy</p></span></div></foreignObject></g></g><g transform="translate(-133.8093719482422, -84)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="110.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 165px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int min_length</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="113.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int max_length</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="175.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 229px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-bool require_uppercase</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="173.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 226px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-bool require_lowercase</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="163.1125030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 219px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-bool require_numbers</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="197.8874969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 248px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-bool require_special_chars</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="211.3249969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 260px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string allowed_special_chars</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="133.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int max_age_days</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="127.9000015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 181px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int history_count</p></span></div></foreignObject></g></g><g transform="translate(-133.8093719482422, 156)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-145.8093719482422 -108 C-42.24345743307488 -108, 61.32245708209243 -108, 145.8093719482422 -108 M-145.8093719482422 -108 C-47.145809937710624 -108, 51.51775207282094 -108, 145.8093719482422 -108"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-145.8093719482422 132 C-86.9182989164539 132, -28.027225884665626 132, 145.8093719482422 132 M-145.8093719482422 132 C-73.03881274606266 132, -0.2682535438831337 132, 145.8093719482422 132"></path></g></g><g transform="translate(4577.734408855438, 838)" id="classId-Produit-37" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-142.125 -252 L142.125 -252 L142.125 252 L-142.125 252"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.125 -252 C-67.93797719640337 -252, 6.249045607193267 -252, 142.125 -252 M-142.125 -252 C-83.48245453883027 -252, -24.83990907766055 -252, 142.125 -252 M142.125 -252 C142.125 -118.69485750553153, 142.125 14.61028498893694, 142.125 252 M142.125 -252 C142.125 -142.4982613533113, 142.125 -32.99652270662261, 142.125 252 M142.125 252 C46.547652987539934 252, -49.02969402492013 252, -142.125 252 M142.125 252 C36.53868273511628 252, -69.04763452976744 252, -142.125 252 M-142.125 252 C-142.125 121.3786403227366, -142.125 -9.242719354526798, -142.125 -252 M-142.125 252 C-142.125 131.42647372073162, -142.125 10.852947441463243, -142.125 -252"></path></g><g transform="translate(0, -228)" class="annotation-group text"></g><g transform="translate(-27.575000762939453, -228)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="55.150001525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Produit</p></span></div></foreignObject></g></g><g transform="translate(-130.125, -180)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="43.82500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="81.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string nom</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="131.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string description</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="232.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 278px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-decimal prix_achat [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="232.6750030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 279px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-decimal prix_vente [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="138.5749969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string code_barres</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="193.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 244px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime date_expiration</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="91.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int quantite</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="134.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 190px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string image_path</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="157.58750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 208px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime created_at</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="161.28750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime updated_at</p></span></div></foreignObject></g><g transform="translate(0,252)" style="" class="label"><foreignObject height="24" width="131.52500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string created_by</p></span></div></foreignObject></g></g><g transform="translate(-130.125, 132)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="161.83750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getProduit() : Produit</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="163.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+createProduit() : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="167.15000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 222px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updateProduit() : void</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="163.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleteProduit() : void</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="163"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+validateStock() : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.125 -204 C-72.92974840686523 -204, -3.734496813730459 -204, 142.125 -204 M-142.125 -204 C-57.78589910942479 -204, 26.55320178115042 -204, 142.125 -204"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-142.125 108 C-78.1052741593881 108, -14.085548318776205 108, 142.125 108 M-142.125 108 C-69.87586399282468 108, 2.373272014350647 108, 142.125 108"></path></g></g><g transform="translate(4577.734408855438, 1320)" id="classId-Categorie-38" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-128.4937515258789 -156 L128.4937515258789 -156 L128.4937515258789 156 L-128.4937515258789 156"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.4937515258789 -156 C-37.79718960519121 -156, 52.89937231549649 -156, 128.4937515258789 -156 M-128.4937515258789 -156 C-68.59519795030188 -156, -8.696644374724855 -156, 128.4937515258789 -156 M128.4937515258789 -156 C128.4937515258789 -49.91683094359347, 128.4937515258789 56.166338112813065, 128.4937515258789 156 M128.4937515258789 -156 C128.4937515258789 -46.70375506411237, 128.4937515258789 62.59248987177526, 128.4937515258789 156 M128.4937515258789 156 C75.01865938921229 156, 21.54356725254567 156, -128.4937515258789 156 M128.4937515258789 156 C50.55033530202216 156, -27.39308092183458 156, -128.4937515258789 156 M-128.4937515258789 156 C-128.4937515258789 59.73483491577079, -128.4937515258789 -36.53033016845842, -128.4937515258789 -156 M-128.4937515258789 156 C-128.4937515258789 65.29025490069932, -128.4937515258789 -25.419490198601352, -128.4937515258789 -156"></path></g><g transform="translate(0, -132)" class="annotation-group text"></g><g transform="translate(-35.875, -132)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="71.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Categorie</p></span></div></foreignObject></g></g><g transform="translate(-116.4937515258789, -84)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="43.82500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="81.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string nom</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="131.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string description</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="104.73750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 158px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-bool is_active</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="157.58750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 208px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime created_at</p></span></div></foreignObject></g></g><g transform="translate(-116.4937515258789, 60)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="197.1125030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 250px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getCategorie() : Categorie</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="181.0749969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+createCategorie() : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="184.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 238px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updateCategorie() : void</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="180.8874969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleteCategorie() : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.49375915527344 -108 C-59.89771334474459 -108, 8.698332465784262 -108, 128.49375915527344 -108 M-128.49375915527344 -108 C-52.34955716751881 -108, 23.794644820235817 -108, 128.49375915527344 -108"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.49375915527344 36 C-29.14042652620705 36, 70.21290610285934 36, 128.49375915527344 36 M-128.49375915527344 36 C-67.6384911034323 36, -6.783223051591165 36, 128.49375915527344 36"></path></g></g><g transform="translate(3621.125018596649, 838)" id="classId-Client-39" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-143.44375610351562 -192 L143.44375610351562 -192 L143.44375610351562 192 L-143.44375610351562 192"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-143.44375610351562 -192 C-82.4067725836945 -192, -21.36978906387337 -192, 143.44375610351562 -192 M-143.44375610351562 -192 C-69.16234929321458 -192, 5.119057517086475 -192, 143.44375610351562 -192 M143.44375610351562 -192 C143.44375610351562 -90.94495120696146, 143.44375610351562 10.110097586077075, 143.44375610351562 192 M143.44375610351562 -192 C143.44375610351562 -55.952581598815925, 143.44375610351562 80.09483680236815, 143.44375610351562 192 M143.44375610351562 192 C75.22492877001693 192, 7.006101436518236 192, -143.44375610351562 192 M143.44375610351562 192 C39.22152471209124 192, -65.00070667933315 192, -143.44375610351562 192 M-143.44375610351562 192 C-143.44375610351562 99.59382824853628, -143.44375610351562 7.187656497072567, -143.44375610351562 -192 M-143.44375610351562 192 C-143.44375610351562 65.53166365408183, -143.44375610351562 -60.93667269183635, -143.44375610351562 -192"></path></g><g transform="translate(0, -168)" class="annotation-group text"></g><g transform="translate(-22.137500762939453, -168)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="44.275001525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 91px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Client</p></span></div></foreignObject></g></g><g transform="translate(-131.44375610351562, -120)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="43.82500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="170.65000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 225px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string nom [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="194.5124969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 248px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string prenom [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="212.27500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 264px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string telephone [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="179.7375030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 231px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string email [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="193.9875030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 246px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string adresse [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="240.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 285px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-decimal credit_limit [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="157.58750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 208px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime created_at</p></span></div></foreignObject></g></g><g transform="translate(-131.44375610351562, 96)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="144.08750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getClient() : Client</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="154.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 204px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+createClient() : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="158.27500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 210px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updateClient() : void</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="154.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 204px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleteClient() : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-143.44375610351562 -144 C-35.09448958829887 -144, 73.25477692691788 -144, 143.44375610351562 -144 M-143.44375610351562 -144 C-83.05395880868906 -144, -22.664161513862496 -144, 143.44375610351562 -144"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-143.44375610351562 72 C-76.34587576032534 72, -9.247995417135058 72, 143.44375610351562 72 M-143.44375610351562 72 C-44.851346455936095 72, 53.741063191643434 72, 143.44375610351562 72"></path></g></g><g transform="translate(4409.95003080368, 260)" id="classId-Facture-40" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-156 -192 L156 -192 L156 192 L-156 192"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-156 -192 C-34.425733180574824 -192, 87.14853363885035 -192, 156 -192 M-156 -192 C-79.37009430102735 -192, -2.740188602054701 -192, 156 -192 M156 -192 C156 -73.80733301165398, 156 44.38533397669204, 156 192 M156 -192 C156 -90.58952509490203, 156 10.820949810195941, 156 192 M156 192 C70.63002128109562 192, -14.739957437808755 192, -156 192 M156 192 C77.51867484861405 192, -0.9626503027718911 192, -156 192 M-156 192 C-156 114.89054565101766, -156 37.78109130203532, -156 -192 M-156 192 C-156 80.75317424539156, -156 -30.493651509216875, -156 -192"></path></g><g transform="translate(0, -168)" class="annotation-group text"></g><g transform="translate(-28.5625, -168)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="57.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 101px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Facture</p></span></div></foreignObject></g></g><g transform="translate(-144, -120)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="43.82500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-int id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="172.2375030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 222px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime date_facture</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="259.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 306px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-decimal montant_total [encrypted]</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="93.73750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string statut</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="176.3625030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string payment_method</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="157.58750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 208px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-DateTime created_at</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="131.52500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-string created_by</p></span></div></foreignObject></g></g><g transform="translate(-144, 72)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="168.27500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getFacture() : Facture</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="166.66250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 214px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+createFacture() : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="170.3625030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 221px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updateFacture() : void</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="166.47500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleteFacture() : void</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="193.4499969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 240px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculateTotal() : decimal</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-156 -144 C-55.938376783103735 -144, 44.12324643379253 -144, 156 -144 M-156 -144 C-78.27859094249794 -144, -0.5571818849958845 -144, 156 -144"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-156 48 C-37.25260364495746 48, 81.49479271008508 48, 156 48 M-156 48 C-91.1599869976638 48, -26.319973995327587 48, 156 48"></path></g></g><g transform="translate(267.875, 838)" id="classId-SecurityMonitoringController-41" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-199.203125 -123 L199.203125 -123 L199.203125 123 L-199.203125 123"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-199.203125 -123 C-74.23026181998976 -123, 50.74260136002047 -123, 199.203125 -123 M-199.203125 -123 C-60.63086094919882 -123, 77.94140310160236 -123, 199.203125 -123 M199.203125 -123 C199.203125 -72.71708209703564, 199.203125 -22.434164194071286, 199.203125 123 M199.203125 -123 C199.203125 -45.21473532272988, 199.203125 32.57052935454024, 199.203125 123 M199.203125 123 C111.40312020418779 123, 23.603115408375572 123, -199.203125 123 M199.203125 123 C85.6412404094533 123, -27.920644181093394 123, -199.203125 123 M-199.203125 123 C-199.203125 53.79358275638745, -199.203125 -15.412834487225098, -199.203125 -123 M-199.203125 123 C-199.203125 32.18487753023152, -199.203125 -58.630244939536965, -199.203125 -123"></path></g><g transform="translate(0, -99)" class="annotation-group text"></g><g transform="translate(-108.59375, -99)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="217.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 257px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SecurityMonitoringController</p></span></div></foreignObject></g></g><g transform="translate(-187.203125, -51)" class="members-group text"></g><g transform="translate(-187.203125, -21)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="265.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 323px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getDashboard() : SecurityDashboard</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="236.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 323px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getAlerts() : List&lt;SecurityAlert&gt;</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="252.58750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 303px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+generateReport() : SecurityReport</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="246.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 333px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getEvents() : List&lt;SecurityEvent&gt;</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="197.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 249px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getStatus() : SystemStatus</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="202.72500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 256px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+performScan() : ScanResult</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-199.203125 -75 C-66.97183137651581 -75, 65.25946224696838 -75, 199.203125 -75 M-199.203125 -75 C-54.508399616872225 -75, 90.18632576625555 -75, 199.203125 -75"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-199.203125 -51 C-61.935279928878316 -51, 75.33256514224337 -51, 199.203125 -51 M-199.203125 -51 C-47.045462854356344 -51, 105.11219929128731 -51, 199.203125 -51"></path></g></g></g></g></g></svg>