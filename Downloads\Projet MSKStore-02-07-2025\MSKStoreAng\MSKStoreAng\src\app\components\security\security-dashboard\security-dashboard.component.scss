// Security Dashboard Styles

.security-dashboard {
  .card {
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);
    border: none;
    border-radius: 0.428rem;
    
    .card-header {
      border-bottom: 1px solid #ebe9f1;
      padding: 1.5rem;
      
      .card-title {
        font-weight: 600;
        color: #5e5873;
        margin-bottom: 0;
        
        i {
          margin-right: 0.5rem;
          color: #7367f0;
        }
      }
    }
    
    .card-body {
      padding: 1.5rem;
    }
  }
  
  // Statistics Cards
  .stats-card {
    .avatar {
      width: 44px;
      height: 44px;
      
      .avatar-content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        
        i {
          font-size: 1.2rem;
        }
      }
    }
    
    h2 {
      font-size: 2rem;
      font-weight: 700;
      color: #5e5873;
      margin-bottom: 0;
    }
    
    .font-small-2 {
      font-size: 0.857rem;
      color: #b9b9c3;
    }
  }
  
  // Session Status
  .session-status {
    .session-indicator {
      &.active {
        .indicator-dot {
          background-color: #28c76f;
          animation: pulse 2s infinite;
        }
      }
      
      &.inactive {
        .indicator-dot {
          background-color: #ea5455;
        }
      }
      
      .indicator-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 0.5rem;
      }
    }
    
    .session-timeout {
      font-family: 'Courier New', monospace;
      font-weight: 600;
      
      &.warning {
        color: #ff9f43;
      }
      
      &.danger {
        color: #ea5455;
      }
    }
  }
  
  // Events Table
  .events-table {
    .table {
      margin-bottom: 0;
      
      thead th {
        border-top: none;
        border-bottom: 1px solid #ebe9f1;
        font-weight: 600;
        color: #5e5873;
        font-size: 0.857rem;
        padding: 1rem 0.75rem;
      }
      
      tbody td {
        border-top: 1px solid #ebe9f1;
        padding: 0.75rem;
        vertical-align: middle;
        
        .event-type {
          display: flex;
          align-items: center;
          
          i {
            margin-right: 0.5rem;
            font-size: 1rem;
          }
        }
        
        .event-description {
          max-width: 300px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      
      .table-hover tbody tr:hover {
        background-color: #f8f8f8;
      }
    }
    
    // Severity Badges
    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      
      &.badge-danger {
        background-color: #ea5455;
        color: #fff;
      }
      
      &.badge-warning {
        background-color: #ff9f43;
        color: #fff;
      }
      
      &.badge-info {
        background-color: #00cfe8;
        color: #fff;
      }
      
      &.badge-success {
        background-color: #28c76f;
        color: #fff;
      }
      
      &.badge-light {
        background-color: #f8f8f8;
        color: #5e5873;
      }
    }
  }
  
  // Filters
  .filters-section {
    background-color: #f8f8f8;
    padding: 1rem;
    border-radius: 0.428rem;
    margin-bottom: 1rem;
    
    .form-control {
      border: 1px solid #d8d6de;
      border-radius: 0.357rem;
      padding: 0.438rem 1rem;
      font-size: 0.857rem;
      
      &:focus {
        border-color: #7367f0;
        box-shadow: 0 3px 10px 0 rgba(34, 41, 47, 0.1);
      }
    }
    
    label {
      font-weight: 600;
      color: #5e5873;
      margin-bottom: 0.5rem;
      font-size: 0.857rem;
    }
  }
  
  // Pagination
  .pagination {
    .page-item {
      .page-link {
        border: 1px solid #d8d6de;
        color: #5e5873;
        padding: 0.5rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 0.357rem;
        
        &:hover {
          background-color: #7367f0;
          border-color: #7367f0;
          color: #fff;
        }
      }
      
      &.active .page-link {
        background-color: #7367f0;
        border-color: #7367f0;
        color: #fff;
      }
      
      &.disabled .page-link {
        color: #b9b9c3;
        background-color: #fff;
        border-color: #d8d6de;
      }
    }
  }
  
  // Action Buttons
  .btn-group {
    .btn {
      border-radius: 0.357rem;
      padding: 0.438rem 1rem;
      font-size: 0.857rem;
      font-weight: 500;
      
      &:not(:last-child) {
        margin-right: 0.5rem;
      }
      
      i {
        margin-right: 0.25rem;
        font-size: 0.857rem;
      }
    }
  }
  
  // Empty State
  .empty-state {
    text-align: center;
    padding: 2rem;
    
    i {
      font-size: 3rem;
      color: #b9b9c3;
      margin-bottom: 1rem;
    }
    
    p {
      color: #b9b9c3;
      font-size: 1rem;
      margin-bottom: 0;
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(40, 199, 111, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(40, 199, 111, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(40, 199, 111, 0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .security-dashboard {
    .card-header {
      .btn-group {
        flex-direction: column;
        
        .btn {
          margin-bottom: 0.5rem;
          margin-right: 0;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .filters-section {
      .row {
        .col-md-4 {
          margin-bottom: 1rem;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .events-table {
      .table {
        font-size: 0.75rem;
        
        .event-description {
          max-width: 150px;
        }
      }
    }
  }
}
