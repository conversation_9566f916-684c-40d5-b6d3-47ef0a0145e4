"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */function patchLongStackTrace(t){const n={},e="__creationTrace__",a="STACKTRACE TRACKING",r="__SEP_TAG__";let c=r+"@[native]";class o{error=u();timestamp=new Date}function s(){return new Error(a)}function i(){try{throw s()}catch(t){return t}}const l=s(),_=i(),u=l.stack?s:_.stack?i:s;function f(t){return t.stack?t.stack.split("\n"):[]}function k(t,e){let a=f(e);for(let e=0;e<a.length;e++)n.hasOwnProperty(a[e])||t.push(a[e])}function h(t,n){const e=[n?n.trim():""];if(t){let n=(new Date).getTime();for(let a=0;a<t.length;a++){const o=t[a],s=o.timestamp;let i=`____________________Elapsed ${n-s.getTime()} ms; At: ${s}`;i=i.replace(/[^\w\d]/g,"_"),e.push(c.replace(r,i)),k(e,o.error),n=s.getTime()}}return e.join("\n")}function T(){return Error.stackTraceLimit>0}function g(t,n){n>0&&(t.push(f((new o).error)),g(t,n-1))}t.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(n){if(!n)return;const e=n[t.__symbol__("currentTaskTrace")];return e?h(e,n.stack):n.stack},onScheduleTask:function(n,a,r,c){if(T()){const n=t.currentTask;let a=n&&n.data&&n.data[e]||[];a=[new o].concat(a),a.length>this.longStackTraceLimit&&(a.length=this.longStackTraceLimit),c.data||(c.data={}),"eventTask"===c.type&&(c.data={...c.data}),c.data[e]=a}return n.scheduleTask(r,c)},onHandleError:function(n,a,r,c){if(T()){const n=t.currentTask||c.task;if(c instanceof Error&&n){const t=h(n.data&&n.data[e],c.stack);try{c.stack=c.longStack=t}catch(t){}}}return n.handleError(r,c)}},function d(){if(!T())return;const t=[];g(t,2);const e=t[0],o=t[1];for(let t=0;t<e.length;t++){const n=e[t];if(-1==n.indexOf(a)){let t=n.match(/^\s*at\s+/);if(t){c=t[0]+r+" (http://localhost)";break}}}for(let t=0;t<e.length;t++){const a=e[t];if(a!==o[t])break;n[a]=!0}}()}patchLongStackTrace(Zone);