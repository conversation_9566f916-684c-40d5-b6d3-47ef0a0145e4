<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2108.07177734375 1450.85693359375" style="max-width: 2108.07177734375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0"><style>#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .error-icon{fill:#a44141;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edge-thickness-normal{stroke-width:1px;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .marker.cross{stroke:lightgrey;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 p{margin:0;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .cluster-label text{fill:#F9FFFE;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .cluster-label span{color:#F9FFFE;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .cluster-label span p{background-color:transparent;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .label text,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 span{fill:#ccc;color:#ccc;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node rect,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node circle,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node ellipse,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node polygon,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .rough-node .label text,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node .label text,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .image-shape .label,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .icon-shape .label{text-anchor:middle;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .rough-node .label,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node .label,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .image-shape .label,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .icon-shape .label{text-align:center;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .node.clickable{cursor:pointer;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .arrowheadPath{fill:lightgrey;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .cluster text{fill:#F9FFFE;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .cluster span{color:#F9FFFE;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 rect.text{fill:none;stroke-width:0;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .icon-shape,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .icon-shape p,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .icon-shape rect,#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph5" class="cluster"><rect height="283.85687255859375" width="585.3968811035156" y="1159" x="561.4906234741211" style=""></rect><g transform="translate(792.8515625, 1159)" class="cluster-label"><foreignObject height="24" width="122.67500305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Security</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="283.85687255859375" width="519.09375" y="1159" x="1580.9781303405762" style=""></rect><g transform="translate(1801.9562530517578, 1159)" class="cluster-label"><foreignObject height="24" width="77.13750457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="104" width="1260.7906303405762" y="981" x="838.3125" style=""></rect><g transform="translate(1408.1640644073486, 981)" class="cluster-label"><foreignObject height="24" width="121.0875015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="128" width="1137.478126525879" y="779" x="946.2750053405762" style=""></rect><g transform="translate(1461.7390670776367, 779)" class="cluster-label"><foreignObject height="24" width="106.55000305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Controllers</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="464" width="1486.862506866455" y="241" x="552.8968734741211" style=""></rect><g transform="translate(1224.128122329712, 241)" class="cluster-label"><foreignObject height="24" width="144.40000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Middleware Pipeline</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="1434.8568725585938" width="512.9437484741211" y="8" x="8" style=""></rect><g transform="translate(203.015625, 8)" class="cluster-label"><foreignObject height="24" width="122.9124984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend Angular</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_AuthGuard_0" d="M158.208,87L152.168,91.167C146.128,95.333,134.049,103.667,128.009,116.5C121.969,129.333,121.969,146.667,121.969,164C121.969,181.333,121.969,198.667,121.969,211.5C121.969,224.333,121.969,232.667,121.969,247.5C121.969,262.333,121.969,283.667,121.969,305C121.969,326.333,121.969,347.667,121.969,367C121.969,386.333,121.969,403.667,121.969,421C121.969,438.333,121.969,455.667,121.969,475C121.969,494.333,121.969,515.667,121.969,537C121.969,558.333,121.969,579.667,121.969,599C121.969,618.333,121.969,635.667,121.969,653C121.969,670.333,121.969,687.667,121.969,702.5C121.969,717.333,121.969,729.667,121.969,742C121.969,754.333,121.969,766.667,121.969,783.5C121.969,800.333,121.969,821.667,121.969,843C121.969,864.333,121.969,885.667,121.969,902.5C121.969,919.333,121.969,931.667,121.969,944C121.969,956.333,121.969,968.667,121.969,983.5C121.969,998.333,121.969,1015.667,121.969,1033C121.969,1050.333,121.969,1067.667,121.969,1082.5C121.969,1097.333,121.969,1109.667,121.969,1122C121.969,1134.333,121.969,1146.667,121.969,1157.996C121.969,1169.326,121.969,1179.652,121.969,1184.816L121.969,1189.979"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthGuard_TokenService_1" d="M121.969,1247.979L121.969,1255.808C121.969,1263.638,121.969,1279.298,121.969,1295.286C121.969,1311.274,121.969,1327.59,121.969,1335.749L121.969,1343.907"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_HttpInterceptor_2" d="M224.073,87L228.197,91.167C232.321,95.333,240.57,103.667,244.694,111.333C248.819,119,248.819,126,248.819,129.5L248.819,133"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HttpInterceptor_SecurityHeaders_3" d="M248.819,191L248.819,195.167C248.819,199.333,248.819,207.667,248.819,216C248.819,224.333,248.819,232.667,380.879,245.957C512.939,259.248,777.058,277.495,909.118,286.619L1041.178,295.743"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SecurityHeaders_RateLimit_4" d="M1175.169,344L1175.169,348.167C1175.169,352.333,1175.169,360.667,1175.169,368.333C1175.169,376,1175.169,383,1175.169,386.5L1175.169,390"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RateLimit_InputValidation_5" d="M1286.075,448L1303.19,452.167C1320.305,456.333,1354.535,464.667,1371.65,472.333C1388.766,480,1388.766,487,1388.766,490.5L1388.766,494"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InputValidation_CORS_6" d="M1439.601,576L1445.032,580.167C1450.463,584.333,1461.325,592.667,1466.756,600.333C1472.188,608,1472.188,615,1472.188,618.5L1472.188,622"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CORS_UsersController_7" d="M1423.821,680L1416.357,684.167C1408.893,688.333,1393.965,696.667,1386.501,707C1379.038,717.333,1379.038,729.667,1379.038,742C1379.038,754.333,1379.038,766.667,1379.038,778.333C1379.038,790,1379.038,801,1379.038,806.5L1379.038,812"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CORS_SecurityController_8" d="M1379.875,666.3L1335.108,672.75C1290.342,679.2,1200.808,692.1,1156.042,704.717C1111.275,717.333,1111.275,729.667,1111.275,742C1111.275,754.333,1111.275,766.667,1111.275,776.333C1111.275,786,1111.275,793,1111.275,796.5L1111.275,800"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CORS_BusinessControllers_9" d="M1564.5,663.104L1628.293,670.087C1692.086,677.07,1819.673,691.035,1883.466,704.184C1947.259,717.333,1947.259,729.667,1947.259,742C1947.259,754.333,1947.259,766.667,1947.259,778.333C1947.259,790,1947.259,801,1947.259,806.5L1947.259,812"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UsersController_SecurityService_10" d="M1296.189,870L1277.267,876.167C1258.345,882.333,1220.501,894.667,1164.572,907C1108.644,919.333,1034.631,931.667,997.625,944C960.619,956.333,960.619,968.667,960.619,978.333C960.619,988,960.619,995,960.619,998.5L960.619,1002"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UsersController_EncryptionService_11" d="M1466.8,855.806L1525.272,864.339C1583.744,872.871,1700.688,889.935,1759.159,904.634C1817.631,919.333,1817.631,931.667,1817.631,944C1817.631,956.333,1817.631,968.667,1826.605,978.734C1835.58,988.802,1853.528,996.604,1862.502,1000.505L1871.476,1004.405"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UsersController_PasswordService_12" d="M1466.8,867.657L1490.14,874.214C1513.479,880.771,1560.158,893.886,1583.498,906.609C1606.838,919.333,1606.838,931.667,1606.838,944C1606.838,956.333,1606.838,968.667,1606.838,978.333C1606.838,988,1606.838,995,1606.838,998.5L1606.838,1002"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SecurityController_MonitoringService_13" d="M1235.481,882L1248.751,886.167C1262.021,890.333,1288.56,898.667,1266.491,909C1244.421,919.333,1173.742,931.667,1138.402,944C1103.063,956.333,1103.063,968.667,1112.26,978.739C1121.458,988.812,1139.853,996.624,1149.05,1000.53L1158.248,1004.436"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BusinessControllers_EncryptionService_14" d="M1947.259,870L1947.259,876.167C1947.259,882.333,1947.259,894.667,1947.259,907C1947.259,919.333,1947.259,931.667,1947.259,944C1947.259,956.333,1947.259,968.667,1946.584,978.345C1945.909,988.024,1944.558,995.048,1943.882,998.56L1943.207,1002.072"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SecurityService_JWT_15" d="M960.619,1060L960.619,1064.167C960.619,1068.333,960.619,1076.667,917.633,1087C874.647,1097.333,788.675,1109.667,745.689,1122C702.703,1134.333,702.703,1146.667,702.703,1157.996C702.703,1169.326,702.703,1179.652,702.703,1184.816L702.703,1189.979"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SecurityService_DataContext_16" d="M1047.925,1039.394L1151.71,1046.995C1255.495,1054.596,1463.065,1069.798,1566.849,1083.566C1670.634,1097.333,1670.634,1109.667,1670.634,1122C1670.634,1134.333,1670.634,1146.667,1673.688,1158.087C1676.743,1169.507,1682.851,1180.014,1685.905,1185.267L1688.959,1190.521"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EncryptionService_DataContext_17" d="M1859.488,1060L1847.487,1064.167C1835.485,1068.333,1811.482,1076.667,1799.48,1087C1787.478,1097.333,1787.478,1109.667,1787.478,1122C1787.478,1134.333,1787.478,1146.667,1780.406,1158.257C1773.334,1169.848,1759.189,1180.696,1752.117,1186.12L1745.044,1191.544"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MonitoringService_SecurityLogs_18" d="M1353.088,1043.01L1442.288,1050.008C1531.489,1057.006,1709.89,1071.003,1799.09,1084.168C1888.291,1097.333,1888.291,1109.667,1888.291,1122C1888.291,1134.333,1888.291,1146.667,1888.291,1156.333C1888.291,1166,1888.291,1173,1888.291,1176.5L1888.291,1180"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MonitoringService_ThreatDetection_19" d="M1156.737,1060L1146.125,1064.167C1135.512,1068.333,1114.287,1076.667,1060.896,1087C1007.504,1097.333,921.946,1109.667,879.167,1122C836.388,1134.333,836.388,1146.667,847.31,1158.362C858.232,1170.057,880.077,1181.115,890.999,1186.643L901.922,1192.172"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DataContext_SQLiteDB_20" d="M1706.666,1247.979L1706.666,1255.808C1706.666,1263.638,1706.666,1279.298,1717.651,1293.892C1728.636,1308.486,1750.606,1322.015,1761.592,1328.78L1772.577,1335.544"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ThreatDetection_IPBlocking_21" d="M958.831,1247.979L958.831,1255.808C958.831,1263.638,958.831,1279.298,965.381,1295.429C971.931,1311.56,985.03,1328.164,991.58,1336.465L998.13,1344.767"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RateLimit_IPBlocking_22" d="M1053.638,433.158L987.264,439.799C920.891,446.439,788.144,459.719,721.77,477.026C655.397,494.333,655.397,515.667,655.397,537C655.397,558.333,655.397,579.667,655.397,599C655.397,618.333,655.397,635.667,655.397,653C655.397,670.333,655.397,687.667,655.397,702.5C655.397,717.333,655.397,729.667,655.397,742C655.397,754.333,655.397,766.667,655.397,783.5C655.397,800.333,655.397,821.667,655.397,843C655.397,864.333,655.397,885.667,655.397,902.5C655.397,919.333,655.397,931.667,655.397,944C655.397,956.333,655.397,968.667,655.397,983.5C655.397,998.333,655.397,1015.667,655.397,1033C655.397,1050.333,655.397,1067.667,726.995,1082.5C798.594,1097.333,941.791,1109.667,1013.389,1122C1084.988,1134.333,1084.988,1146.667,1084.988,1163.163C1084.988,1179.66,1084.988,1200.319,1084.988,1222.979C1084.988,1245.638,1084.988,1270.298,1078.438,1290.929C1071.888,1311.56,1058.789,1328.164,1052.239,1336.465L1045.689,1344.767"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InputValidation_MonitoringService_23" d="M1258.766,554.99L1203.351,562.658C1147.935,570.326,1037.105,585.663,981.69,601.998C926.275,618.333,926.275,635.667,926.275,653C926.275,670.333,926.275,687.667,926.275,702.5C926.275,717.333,926.275,729.667,926.275,742C926.275,754.333,926.275,766.667,926.275,783.5C926.275,800.333,926.275,821.667,926.275,843C926.275,864.333,926.275,885.667,975.277,902.5C1024.279,919.333,1122.283,931.667,1171.285,944C1220.288,956.333,1220.288,968.667,1220.639,978.337C1220.991,988.007,1221.694,995.013,1222.045,998.517L1222.397,1002.02"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_JWT_TokenService_24" d="M702.703,1247.979L702.703,1255.808C702.703,1263.638,702.703,1279.298,619.736,1298.55C536.769,1317.801,370.834,1340.646,287.867,1352.068L204.9,1363.49"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ThreatDetection_MonitoringService_25" d="M978.093,1193.979L982.252,1188.149C986.411,1182.319,994.729,1170.66,998.888,1158.663C1003.047,1146.667,1003.047,1134.333,1040.123,1122C1077.2,1109.667,1151.353,1097.333,1188.43,1087.667C1225.506,1078,1225.506,1071,1225.506,1067.5L1225.506,1064"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SecurityHeaders_UI_26" d="M1045.169,293.709L944.025,284.924C842.881,276.139,640.594,258.57,539.45,245.618C438.306,232.667,438.306,224.333,438.306,211.5C438.306,198.667,438.306,181.333,438.306,164C438.306,146.667,438.306,129.333,415.944,115.841C393.581,102.348,348.857,92.696,326.494,87.871L304.132,83.045"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EncryptionService_SQLiteDB_27" d="M1973.07,1060L1978.596,1064.167C1984.123,1068.333,1995.175,1076.667,2000.702,1087C2006.228,1097.333,2006.228,1109.667,2006.228,1122C2006.228,1134.333,2006.228,1146.667,2006.228,1163.163C2006.228,1179.66,2006.228,1200.319,2006.228,1222.979C2006.228,1245.638,2006.228,1270.298,1995.243,1289.392C1984.258,1308.486,1962.287,1322.015,1951.302,1328.78L1940.317,1335.544"></path><path marker-end="url(#mermaid-c0999514-65e0-439f-9ea9-7e2584834ef0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MonitoringService_SecurityController_28" d="M1353.088,1009.138L1378.161,1004.448C1403.235,999.759,1453.383,990.379,1478.457,979.523C1503.531,968.667,1503.531,956.333,1503.531,944C1503.531,931.667,1503.531,919.333,1460.48,906.142C1417.428,892.952,1331.326,878.903,1288.274,871.879L1245.223,864.855"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(655.3968734741211, 843)" class="edgeLabel"><g transform="translate(-19.181249618530273, -12)" class="label"><foreignObject height="24" width="38.36249923706055"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Block</p></span></div></foreignObject></g></g><g transform="translate(926.2750053405762, 742)" class="edgeLabel"><g transform="translate(-37.59375, -12)" class="label"><foreignObject height="24" width="75.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Log Attack</p></span></div></foreignObject></g></g><g transform="translate(702.703125, 1294.9572830200195)" class="edgeLabel"><g transform="translate(-29.11250114440918, -12)" class="label"><foreignObject height="24" width="58.22500228881836"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Validate</p></span></div></foreignObject></g></g><g transform="translate(1003.046877861023, 1122)" class="edgeLabel"><g transform="translate(-17.725000381469727, -12)" class="label"><foreignObject height="24" width="35.45000076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Alert</p></span></div></foreignObject></g></g><g transform="translate(438.30624771118164, 164)" class="edgeLabel"><g transform="translate(-62.63750076293945, -12)" class="label"><foreignObject height="24" width="125.2750015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS, CSP, HSTS</p></span></div></foreignObject></g></g><g transform="translate(2006.2281303405762, 1220.9786415100098)" class="edgeLabel"><g transform="translate(-28.375, -12)" class="label"><foreignObject height="24" width="56.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>AES-256</p></span></div></foreignObject></g></g><g transform="translate(1503.5312576293945, 944)" class="edgeLabel"><g transform="translate(-34.65625, -12)" class="label"><foreignObject height="24" width="69.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Real-time</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(197.34687423706055, 60)" id="flowchart-UI-735" class="node default"><rect height="54" width="205.75" y="-27" x="-102.875" style="" class="basic label-container"></rect><g transform="translate(-72.875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="145.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Interface Utilisateur</p></span></div></foreignObject></g></g><g transform="translate(121.96875, 1220.9786415100098)" id="flowchart-AuthGuard-736" class="node default"><rect height="54" width="141.1875" y="-27" x="-70.59375" style="" class="basic label-container"></rect><g transform="translate(-40.59375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="81.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Auth Guard</p></span></div></foreignObject></g></g><g transform="translate(248.8187484741211, 164)" id="flowchart-HttpInterceptor-737" class="node default"><rect height="54" width="183.70000457763672" y="-27" x="-91.85000228881836" style="" class="basic label-container"></rect><g transform="translate(-61.85000228881836, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="123.70000457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HTTP Interceptor</p></span></div></foreignObject></g></g><g transform="translate(121.96875, 1374.9070777893066)" id="flowchart-TokenService-738" class="node default"><rect height="54" width="157.9375" y="-27" x="-78.96875" style="" class="basic label-container"></rect><g transform="translate(-48.96875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="97.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Token Service</p></span></div></foreignObject></g></g><g transform="translate(1175.1687545776367, 305)" id="flowchart-SecurityHeaders-739" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Headers Middleware</p></span></div></foreignObject></g></g><g transform="translate(1175.1687545776367, 421)" id="flowchart-RateLimit-740" class="node default"><rect height="54" width="243.0625" y="-27" x="-121.53125" style="" class="basic label-container"></rect><g transform="translate(-91.53125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="183.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Rate Limiting Middleware</p></span></div></foreignObject></g></g><g transform="translate(1388.7656326293945, 537)" id="flowchart-InputValidation-741" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Input Validation Middleware</p></span></div></foreignObject></g></g><g transform="translate(1472.1875076293945, 653)" id="flowchart-CORS-742" class="node default"><rect height="54" width="184.625" y="-27" x="-92.3125" style="" class="basic label-container"></rect><g transform="translate(-62.3125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="124.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CORS Middleware</p></span></div></foreignObject></g></g><g transform="translate(1379.0375022888184, 843)" id="flowchart-UsersController-743" class="node default"><rect height="54" width="175.5250015258789" y="-27" x="-87.76250076293945" style="" class="basic label-container"></rect><g transform="translate(-57.76250076293945, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="115.5250015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Users Controller</p></span></div></foreignObject></g></g><g transform="translate(1111.2750053405762, 843)" id="flowchart-SecurityController-744" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Monitoring Controller</p></span></div></foreignObject></g></g><g transform="translate(1947.2593803405762, 843)" id="flowchart-BusinessControllers-745" class="node default"><rect height="54" width="202.9875030517578" y="-27" x="-101.4937515258789" style="" class="basic label-container"></rect><g transform="translate(-71.4937515258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="142.9875030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Business Controllers</p></span></div></foreignObject></g></g><g transform="translate(960.6187515258789, 1033)" id="flowchart-SecurityService-746" class="node default"><rect height="54" width="174.6125030517578" y="-27" x="-87.3062515258789" style="" class="basic label-container"></rect><g transform="translate(-57.306251525878906, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="114.61250305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Service</p></span></div></foreignObject></g></g><g transform="translate(1937.2593803405762, 1033)" id="flowchart-EncryptionService-747" class="node default"><rect height="54" width="253.6875" y="-27" x="-126.84375" style="" class="basic label-container"></rect><g transform="translate(-96.84375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="193.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Encryption Service AES-256</p></span></div></foreignObject></g></g><g transform="translate(1606.8375053405762, 1033)" id="flowchart-PasswordService-748" class="node default"><rect height="54" width="257.875" y="-27" x="-128.9375" style="" class="basic label-container"></rect><g transform="translate(-98.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="197.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Password Validation Service</p></span></div></foreignObject></g></g><g transform="translate(1225.5062561035156, 1033)" id="flowchart-MonitoringService-749" class="node default"><rect height="54" width="255.16250610351562" y="-27" x="-127.58125305175781" style="" class="basic label-container"></rect><g transform="translate(-97.58125305175781, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="195.16250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Monitoring Service</p></span></div></foreignObject></g></g><g transform="translate(1706.6656303405762, 1220.9786415100098)" id="flowchart-DataContext-750" class="node default"><rect height="54" width="154.125" y="-27" x="-77.0625" style="" class="basic label-container"></rect><g transform="translate(-47.0625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="94.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Context</p></span></div></foreignObject></g></g><g transform="translate(1856.4468803405762, 1374.9070777893066)" id="flowchart-SQLiteDB-751" class="node default"><path transform="translate(-104.3125, -42.949793930310975)" style="" class="basic label-container" d="M0,15.633195953540652 a104.3125,15.633195953540652 0,0,0 208.625,0 a104.3125,15.633195953540652 0,0,0 -208.625,0 l0,54.63319595354065 a104.3125,15.633195953540652 0,0,0 208.625,0 l0,-54.63319595354065"></path><g transform="translate(-96.8125, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="193.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SQLite Database Encrypted</p></span></div></foreignObject></g></g><g transform="translate(1888.2906303405762, 1220.9786415100098)" id="flowchart-SecurityLogs-752" class="node default"><path transform="translate(-54.5625, -36.978643886812606)" style="" class="basic label-container" d="M0,11.652429257875067 a54.5625,11.652429257875067 0,0,0 109.125,0 a54.5625,11.652429257875067 0,0,0 -109.125,0 l0,50.65242925787507 a54.5625,11.652429257875067 0,0,0 109.125,0 l0,-50.65242925787507"></path><g transform="translate(-47.0625, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="94.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Logs</p></span></div></foreignObject></g></g><g transform="translate(702.703125, 1220.9786415100098)" id="flowchart-JWT-753" class="node default"><rect height="54" width="212.4250030517578" y="-27" x="-106.2125015258789" style="" class="basic label-container"></rect><g transform="translate(-76.2125015258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="152.4250030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JWT Token Validation</p></span></div></foreignObject></g></g><g transform="translate(958.8312530517578, 1220.9786415100098)" id="flowchart-ThreatDetection-754" class="node default"><rect height="54" width="182.3125" y="-27" x="-91.15625" style="" class="basic label-container"></rect><g transform="translate(-61.15625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="122.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Threat Detection</p></span></div></foreignObject></g></g><g transform="translate(1021.9093780517578, 1374.9070777893066)" id="flowchart-IPBlocking-755" class="node default"><rect height="54" width="137.5999984741211" y="-27" x="-68.79999923706055" style="" class="basic label-container"></rect><g transform="translate(-38.79999923706055, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="77.5999984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>IP Blocking</p></span></div></foreignObject></g></g></g></g></g></svg>