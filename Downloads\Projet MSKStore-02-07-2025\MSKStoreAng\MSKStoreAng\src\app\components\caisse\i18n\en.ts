export const locale = {
	lang: 'en',
	data: {
		Barcode: "Barcode",
		Quantity: "Quantity",
		Name: "Name",
		UnitPrice: "Unit Price",
		Reduction: "Reduction",
		Total: "Total",
		Checkout: "Checkout",
		Date:"Date",
		Cashier:"Cashier",
		FactureNumber:"Facture Number",
		TotalPurchase:"Total Purchase",
		PaymentMethod:"Payment Method",
		Cash:"Cash",
		Credit:"Credit",
		Check:"Check",
		RestaurantTicket:"Restaurant Ticket",
		AmountPaid:"Amount Paid",
		Return:"Return",
		Client:"Client",
		AccountOwner:"Account Owner",
		accountNumber:"Account Number",
		typeTicket:"Type Ticket",
		TicketCode:"Ticket Code",
		TotalAmountPaidwithTicket:"Total Amount Paid with Ticket",
		AmounttobePaidinCash:"Amount To Be Paid In Cash",
		RestaurantTicketcredit:"Restaurant Ticket & Credit",
		Search:"Search..",
		TableName:"Table Name",
		Savepurchasestable:"Save purchases table",
		SavedTables:"Saved Tables",
		Loadpurchasestable:"Load purchases table",
		Barcodedoesnotexist:"Barcode does not exist",
		NoDeviceSelected:"No Device Selected",
		Result:"Result",
		Couldntcheckbarcode:"Couldn't check barcode",
		RefPrice:"Ref.Price",
		Taxe:"Taxe",
		SellPrice:"SellPrice",
		printticket:"Print ticket",
		ticketNumber:"Ticket number",
		Enablescanner:"Enable scanner"
	},
}