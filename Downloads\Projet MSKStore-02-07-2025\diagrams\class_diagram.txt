@startuml
class User {
  - id: int
  - nom: string
  - prenom: string
  - email: string
  - mot_de_passe: string
  - role: string
  - telephone: string
  - adresse: string
  - date_creation: date
  - date_modification: date
  + getUser(): User
  + createUser(): void
  + updateUser(): void
  + deleteUser(): void
}

class Admin extends User {
  - permissions: string
  + gererUtilisateurs(): void
  + genererRapport(): void
  + modifierTimbreFiscal(): void
}

class Employe extends User {
  - salaire: decimal
  + gererCaisse(): void
  + gererFactures(): void
  + gererCredits(): void
  + gererClients(): void
  + gererFermetureCaisse(): void
  + gererTypesTicketResto(): void
  + gererProduits(): void
  + gererCategories(): void
  + gererDepots(): void
  + gererFournisseurs(): void
  + gererAchatsFournisseur(): void
}

class Produit {
  - id: int
  - nom: string
  - description: string
  - prix_achat: decimal
  - prix_vente: decimal
  - code_barres: string
  - date_expiration: date
  - quantite: int
  - image: string
  + getProduit(): Produit
  + createProduit(): void
  + updateProduit(): void
  + deleteProduit(): void
}

class Categorie {
  - id: int
  - nom: string
  - description: string
  + getCategorie(): Categorie
  + createCategorie(): void
  + updateCategorie(): void
  + deleteCategorie(): void
}

class Depot {
  - id: int
  - nom: string
  - adresse: string
  - capacite: int
  + getDepot(): Depot
  + createDepot(): void
  + updateDepot(): void
  + deleteDepot(): void
}

class Fournisseur {
  - id: int
  - nom: string
  - contact: string
  - telephone: string
  - email: string
  - adresse: string
  + getFournisseur(): Fournisseur
  + createFournisseur(): void
  + updateFournisseur(): void
  + deleteFournisseur(): void
}

class CommandeFournisseur {
  - id: int
  - date_commande: date
  - statut: string
  - montant_total: decimal
  + getCommandeFournisseur(): CommandeFournisseur
  + createCommandeFournisseur(): void
  + updateCommandeFournisseur(): void
  + deleteCommandeFournisseur(): void
}

class Facture {
  - id: int
  - date_facture: date
  - montant_total: decimal
  - statut: string
  + getFacture(): Facture
  + createFacture(): void
  + updateFacture(): void
  + deleteFacture(): void
}

class Client {
  - id: int
  - nom: string
  - prenom: string
  - telephone: string
  - email: string
  - adresse: string
  + getClient(): Client
  + createClient(): void
  + updateClient(): void
  + deleteClient(): void
}

class TicketResto {
  - id: int
  - nom: string
  - valeur: decimal
  + getTicketResto(): TicketResto
  + createTicketResto(): void
  + updateTicketResto(): void
  + deleteTicketResto(): void
}

class FermetureCaisse {
  - id: int
  - date_fermeture: date
  - montant_final: decimal
  - montant_attendu: decimal
  + getFermetureCaisse(): FermetureCaisse
  + createFermetureCaisse(): void
  + updateFermetureCaisse(): void
  + deleteFermetureCaisse(): void
}

class Inventaire {
  - id: int
  - date_inventaire: date
  - type_inventaire: string
  + getInventaire(): Inventaire
  + createInventaire(): void
  + updateInventaire(): void
  + deleteInventaire(): void
}

User <|-- Admin
User <|-- Employe

Produit "0..*" -- "1" Categorie : contient
Produit "0..*" -- "1" Depot : stocké dans

CommandeFournisseur "1" -- "1" Fournisseur : passe
CommandeFournisseur "1" -- "0..*" Produit : contient

Facture "1" -- "1" Client : émise à
Facture "1" -- "0..*" Produit : contient

FermetureCaisse "1" -- "1" Employe : effectuée par

Inventaire "1" -- "0..*" Produit : contient

@enduml

